@startuml
skinparam defaultFontName Verdana
title Register Tenant
hide footbox
autonumber "<b>[0]"

actor User
boundary RegisterPage
control ApiGateway

control TenantEndpoint
control TenantService
control TenantRepository
control UserService
control UserRepository
control MailService

User -> RegisterPage: fill all info\nand click Register
activate User
activate RegisterPage

RegisterPage -> ApiGateway: $http request
activate ApiGateway
note left
    method POST
    body request {
        //userEntity info and tenantEntity info
    }
end note

ApiGateway -> TenantEndpoint: RequestUtils.amqp(event)
activate TenantEndpoint
note left
    Event event{
        method: create
        payload: ObjectMapperUtil.toStringJson(tenantEntity)
        routingKey: "routingKey.Tenant"
    }
end note

TenantEndpoint -> TenantService: process(Event event)
activate TenantService

TenantService -> TenantService: processCreate(Event event)
note left
    @Override
    protected Event processCreate(Event event){
        //check duplicates email
        ...
        return super.processCreate(event);
    }
end note

alt case Duplicates Email
    TenantService --> TenantEndpoint: return event
    note left
        Build Event contains ErrorInfo
            errorInfo.setErrorKey(ErrorKey.UserErrorKey.DUPLICATE_EMAIL);
            event.statusCode = Constants.ResultStatus.ERROR;
    end note

    TenantEndpoint --> ApiGateway: processError(Event event)
    note left
        ErrorInfo errorInfo = ObjectMapperUtil.objectMapper(event.payload, ErrorInfo.class);
        String key = errorInfo.getErrorKey();
        switch (key){
            case key:
                throw new BadRequestAlertException("", "Duplicates Email", key);
                break;
        }
    end note

    ApiGateway --> RegisterPage: throw new BadRequestAlertException("", "Duplicates Email", key);

    RegisterPage --> User: show log error
end

TenantService -> TenantService: beforeCreate(Tenant tenantEntity)
note left
    @Override
    protected void beforeCreate(Tenant entity) {
        //generate tenantEntity-code
        //init User with Role TENANT_ADMIN, and default Role USER
    }
end note

TenantService -> UserService: create(User userEntity)
activate UserService

UserService -> UserService: beforeCreate(User userEntity)
note left
    @Override
    protected void beforeCreate(User entity){
        //set tenantId
        //set password by setEncryptedPassword
        //setActive(Constants.EntityStatus.REGISTER);
        //set fullName
    }
end note

UserService -> UserRepository: save(User userEntity)
activate UserRepository

UserRepository -> UserRepository: save(User userEntity)

UserRepository --> UserService: return userEntity
deactivate UserRepository

UserService -> UserService: afterCreate(User userEntity)
note left
    @Override
    protected void afterCreate(User entity) {
        //create ROLE_TENANT_ADMIN for userEntity
    }
end note

UserService -> MailService: sendCreationEmail(User userEntity)
activate MailService

MailService --> UserService:
deactivate MailService

UserService --> TenantService: return userEntity
deactivate UserService

TenantService -> TenantRepository
activate TenantRepository

TenantRepository -> TenantRepository: save(tenantEntity)

TenantRepository -> TenantService: return tenantEntity
deactivate TenantRepository

TenantService -> TenantService: afterCreate(Tenant tenantEntity)
note left
    @Override
    protected void afterCreate(Tenant entity) {
        super.afterCreate(entity);
        //update tenantEntity id for tenantAdmin
    }
end note

TenantService --> TenantEndpoint: return event
deactivate TenantService

TenantEndpoint --> ApiGateway: return event
deactivate TenantEndpoint

ApiGateway --> RegisterPage: return JSON data tenantEntity
deactivate ApiGateway

RegisterPage --> User: show message success
deactivate RegisterPage
deactivate User

@enduml
