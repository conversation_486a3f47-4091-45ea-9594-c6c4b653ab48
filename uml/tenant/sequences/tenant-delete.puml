@startuml
skinparam defaultFontName Verdana
title Delete Tenant
hide footbox
autonumber "<b>[0]"

actor User
boundary HomeTenantPage
control ApiGateway

control TenantEndpoint
control TenantService
control TenantRepository

User -> HomeTenantPage: fill info\nand click create
activate User
activate HomeTenantPage

HomeTenantPage -> ApiGateway: $http request
activate ApiGateway
note left
    method: DELETE
    url: /api/tenants/{id}
end note

ApiGateway -> TenantEndpoint: RequestUtils.amqp(event)
activate TenantEndpoint
note left
    IdInfo idInfo = new IdInfo(id)
    Event event{
        method: delete
        payload: ObjectMapperUtil.toStringJson(idInfo)
        routingKey: "routingKey.User"
    }
end note

TenantEndpoint -> TenantService: process(Event event)
activate TenantService

TenantService -> TenantService: processDelete(Event event)
note left
    protected Event processDelete(Event event){
        //check userEntity created by SYSTEM
        ...
    }
end note

alt case userEntity created by SYSTEM
    TenantService --> TenantEndpoint: return event
    note left
        Build Event contains ErrorInfo
            errorInfo.setErrorKey(ErrorKey.CommonErrorKey.REMOVE_SYSTEM_ENTITY);
            event.statusCode = Constants.ResultStatus.ERROR;
    end note

    TenantEndpoint --> ApiGateway: processError(Event event)
    note left
        ErrorInfo errorInfo = ObjectMapperUtil.objectMapper(event.payload, ErrorInfo.class);
        String key = errorInfo.getErrorKey();
        switch (key){
            case key:
                throw new BadRequestAlertException("" , "Remove entity created by System", key);
                break;
        }
    end note

    ApiGateway --> HomeTenantPage: throw new\nBadRequestAlertException\n("" , "Remove entity created by System", key);

    HomeTenantPage --> User: show log error
end

TenantService -> TenantService: beforeSoftDelete(Tenant entity)
note left
    // @Override CrudService function beforeSoftDelete
    entity.setActive(Constants.EntityStatus.DELETED);
end note

TenantService -> TenantRepository: save(Tenant entity)
activate TenantRepository

TenantRepository -> TenantRepository: save(Tenant entity)

TenantRepository --> TenantService: return tenantEntity
deactivate TenantRepository

TenantService -> TenantService: afterSoftDelete(Tenant entity)
note left
    change status all userEntity
    of tenantEntity to DELETED
end note

TenantService --> TenantEndpoint: return event
deactivate TenantService

note left
    Event event {
        errorCode: Constants.ResultStatus.SUCCESS
    }
end note

TenantEndpoint --> ApiGateway: return event
deactivate TenantEndpoint

ApiGateway --> HomeTenantPage: return HTTP success message code
deactivate ApiGateway

HomeTenantPage --> User: show message success
deactivate HomeTenantPage
deactivate User

@enduml
