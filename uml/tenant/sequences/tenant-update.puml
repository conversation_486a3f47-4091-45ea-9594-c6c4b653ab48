@startuml
skinparam defaultFontName Verdana
title Update Tenant
hide footbox
autonumber "<b>[0]"

actor User
boundary UpdateTenantPage
control ApiGateway

control TenantEndpoint
control TenantService
control TenantRepository

User -> UpdateTenantPage: fill info\nand click update
activate User
activate UpdateTenantPage

UpdateTenantPage -> ApiGateway: $http request
activate ApiGateway
note left
    method: PUT
    url: /api/tenants/{id}
    bodyRequest{
        id: ...,
        name:...,
        ...
    }
end note

ApiGateway -> TenantEndpoint: RequestUtils.amqp(event)
activate TenantEndpoint
note left
    Event event{
        method: update
        payload: ObjectMapperUtil.toStringJson(tenantEntity)
        routingKey: "routingKey.Tenant"
    }
end note

TenantEndpoint -> TenantService: process(Event event)
activate TenantService

TenantService -> TenantService: processUpdate(Event event)

TenantService -> TenantRepository: save(Tenant entity)
activate TenantRepository

TenantRepository -> TenantRepository: save(Tenant entity)

TenantRepository --> TenantService: return tenantEntity
deactivate TenantRepository

TenantService --> TenantEndpoint: return event
deactivate TenantService

note left
    Event event {
        payload: ObjectMapperUtil.toStringJson(tenantEntity),
        errorCode: Constants.ResultStatus.SUCCESS
    }
end note

TenantEndpoint --> ApiGateway: return event
deactivate TenantEndpoint

ApiGateway --> UpdateTenantPage: return JSON userEntity
deactivate ApiGateway

UpdateTenantPage --> User: show message success
deactivate UpdateTenantPage
deactivate User

@enduml
