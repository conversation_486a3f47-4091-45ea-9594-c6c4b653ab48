@startuml
skinparam defaultFontName Tahoma
title <u>Tenant Module</u>

package vn.vnpt.oneiot.core.jpa {
     package tenantEntity.endpoints {
        class TenantEndpoint{
            + TenantEndpoint(TenantService service, EventBus eventBus)
            + void process(Event event)
        }
        note right of TenantEndpoint
            queue = "queue-from-nbi-tenantEntity"
            exchange = Constants.TOPIC_EXCHANGE
            routingKey = Constants.ROUTING_KEY_TENANT
        end note
     }

     package tenantEntity.services {
         class TenantService {
            - UserService userService
            - RoleService roleService
            - UserRepository userRepository
            + set() //@Autowired bean
            # void beforeCreate(Tenant entity)
            # void afterCreate(Tenant entity)
            # String addMultipleTenantQuery(String query)
            # Event processCreate(Event event)
            # void afterSoftDelete(Tenant entity)
         }
     }

     package tenantEntity.repositories {
        interface TenantRepository {
            Tenant findFirstById(Long id)
            Tenant findFirstByName(String name)
            void deleteAllByIdIn(Set<Long> ids)
        }
     }

     package tenantEntity.models {
        class Tenant {
            - String code
            - String name
            - Long adminId
            - String adminName
            - String companyName
            + get/set()
        }
     }

     package umgr.services{
        class UserService {
            - MailService mailService
            + set()//@Autowired bean
            # void beforeCreate(User entity)
            # void afterCreate(User entity)
            # void beforeUpdate(User entity)
            # void afterUpdate(User old, User updated)
            # Event processCreate(Event event)
        }

        class MailService {
            # void sendCreationEmail(User userEntity)
        }
     }

     class CrudEndpoint {
        # CrudService<T,ID> service
        # EventBus eventBus
        + CrudEndpoint(CrudService service, EventBus eventBus)
        + void process(Event event)
     }

     class CrudService {
        # CustomJpaRepository<T,ID> repository
        - Class<T> typeParameterClass
        + Event process(Event event)
        # Event processCreate(Event event)
        # Event processUpdate(Event event)
        # Event processDelete(Event event)
        # Event processBatchDelete(Event event)
        # Event processActive(Event event)
        # Event processDeActive(Event event)
        # Event processGetOne(Event event)
        # Event processSearch(Event event)
        # String addMultipleTenantQuery(String query)
        + T create(T entity)
        + T update(ID id, T entity)
        + void delete(T entity)
        + void deleteById(ID id)
        + void softDelete(ID id)
        # void beforeCreate(T entity)
        # void afterCreate(T entity)
        # void beforeUpdate(T entity)
        # void afterUpdate(T entity)
        # void beforeDelete(T entity)
        # void afterDelete(T entity)
        # void beforeSoftDelete(T entity)
        # void afterSoftDelete(T entity)
        + void active(ID id)
        + void deactivate(ID id)
     }

     interface CrudRepository{

     }

     class IdEntity{
        - Long id
        + get/set()
     }

     class AbstractEntity{
        - Long created
        - Long updated
        - String createdBy
        - String updatedBy
        - Integer active
        + get/set()
     }
}

IdEntity <|-- Tenant
AbstractEntity <|-- IdEntity

CrudRepository <|-- TenantRepository
CrudService <|-- TenantService
CrudEndpoint <|-- TenantEndpoint

CrudService <|-- UserService

note as N1
    - T is symbol generic class
        extends AbstractEntity
    - ID is symbol generic class
        extends Serializable
    - if functions in child class
        same name of parent class,
        it overrides functions
        in parent class
end note

@enduml
