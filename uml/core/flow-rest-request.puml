@startuml
skinparam defaultFontName Verdana
title Common Flow Rest Request Over Api-Gateway to Core
hide footbox
autonumber "<b>[0]"

actor User
boundary UI_Page

control GW_JwtFilter
control GW_CrudEndpoint
control GW_CrudService
control ErrorKey
control GW_RequestUtils

control MessageHandler
control Core_AuthEndpoint
control Core_AuthService
control Core_UserService

control Core_CrudEndpoint
control Core_CrudService
control Core_CrudRepository

User -> UI_Page: action in UI
activate User
activate UI_Page

UI_Page -> GW_JwtFilter: $http request
activate GW_JwtFilter
note left
    GET, POST, PUT,
    POST, DELETE
    with params:
        @RequestParam or
        @RequestBody or
        @PathVariable
    and token in header "Authorization"

end note

GW_JwtFilter -> GW_RequestUtils: validateToken(Event event)
activate GW_RequestUtils
note left
    Event event {
        method: "validate_permission"
        payload: token
        routingKey: "routingKey.Auth"
    }
end note

GW_RequestUtils -> Core_AuthEndpoint: RequestUtils.amqp(event)
activate Core_AuthEndpoint

Core_AuthEndpoint -> Core_AuthService: validatePermission(event)
activate Core_AuthService

Core_AuthService -> Core_AuthService: validatePermission(Event event)
alt case Invalid Token
    Core_AuthService -> Core_AuthService: build Event with\nErrorInfo
    note right
        ErrorInfo errorInfo {
            errorKey: "error.userAndPermission.invalidToken"
        }
        Event event {
            event.statusCode = ResultStatus.ERROR;
            event.payload = ObjectMapperUtil.toJsonString(errorInfo);
        }
    end note
    Core_AuthService --> Core_AuthEndpoint: return event
    Core_AuthEndpoint --> GW_RequestUtils: return event
    GW_RequestUtils --> GW_JwtFilter: return event
    GW_JwtFilter --> UI_Page: throw new InvalidCredentialsException();
    UI_Page --> User: show error
end

Core_AuthService -> Core_UserService: getCurrentUer(String token)
activate Core_UserService
note left
    User userEntity {
        ...
        String email,
        Long tenantId,
        Set<String> authorities,
        ...
    }
    Event event {
        event.statusCode = ResultStatus.SUCCESS;
        event.payload = ObjectMapperUtil.toJsonString(userEntity);
    }
end note

Core_UserService --> Core_AuthService: return userEntity
deactivate Core_UserService

Core_AuthService --> Core_AuthEndpoint: return event\n(with userEntity info)
deactivate Core_AuthService

Core_AuthEndpoint --> GW_RequestUtils: return event\n(with userEntity info)
deactivate Core_AuthEndpoint

GW_RequestUtils --> GW_JwtFilter: return event\n(with userEntity info)
deactivate GW_RequestUtils

GW_JwtFilter -> GW_JwtFilter: initSecurityInfo(User userEntity, String token)
note left
    init Set<GrantedAuthority> authorities
    init UserPrincipal principal
    init Authentication authentication
    SecurityContextHolder.getContext().setAuthentication(authentication);
end note

GW_JwtFilter -> GW_CrudEndpoint: filterChain.doFilter\n(servletRequest,servletResponse)
deactivate GW_JwtFilter
activate GW_CrudEndpoint

GW_CrudEndpoint -> GW_CrudEndpoint: validate authorization
note left
    @PreAuthorize("hasAnyAuthority('...', ...)") or
    @PostAuthorize("...")
end note

alt case Invalid Authorization
    GW_CrudEndpoint --> UI_Page: throw new InvalidCredentialsException();
    UI_Page --> User: show error
end

GW_CrudEndpoint -> GW_CrudService: call function in service
activate GW_CrudService
note left
    functions:
        get
        create
        update
        delete
        batchDelete
        active
        deActive
        search
        ...
end note

GW_CrudService -> GW_RequestUtils: RequestUtils.amqp(event)
activate GW_RequestUtils
note left
    Event event {
        method: "get_one", "create", "delete", "update", "search", ...
        payload: ObjectMapperUtil.toJsonString(...)
        token: SecurityUtils.getCurrentUserJWT()
    }
end note

GW_RequestUtils -> MessageHandler: RequestUtils.amqp(event)
activate MessageHandler

MessageHandler -> Core_UserService: getCurrentUer(String token)
activate Core_UserService

Core_UserService -> MessageHandler: return Object userEntity
deactivate Core_UserService

MessageHandler -> MessageHandler: initSecurityInfo(User userEntity, String token)
note left
    init Set<GrantedAuthority> authorities
    init UserPrincipal principal
    init Authentication authentication
    SecurityContextHolder.getContext().setAuthentication(authentication);
end note

MessageHandler -> Core_CrudEndpoint: RequestUtils.amqp(event)
deactivate MessageHandler
activate Core_CrudEndpoint

Core_CrudEndpoint -> Core_CrudService: process(Event event)
activate Core_CrudService

Core_CrudService -> Core_CrudRepository: process(Event event)
activate Core_CrudRepository

Core_CrudRepository --> Core_CrudService: get data from Database
deactivate Core_CrudRepository

Core_CrudService --> Core_CrudEndpoint: return event
deactivate Core_CrudService
note left
    public Event process(Event event){
            switch (event.method){
                case ...:
                    //do business
                    break;
            }
            return event;
end note

Core_CrudEndpoint --> GW_RequestUtils: return event
deactivate Core_CrudEndpoint

GW_RequestUtils --> GW_CrudService: return event
deactivate GW_RequestUtils

alt case event.statusCode == ResultStatus.ERROR
    GW_CrudService -> ErrorKey: processError(Event event)
    activate ErrorKey
    ErrorKey --> UI_Page: throw new BadRequestAlertException()
    note right
        ErrorInfo errorInfo = ObjectMapperUtil.objectMapper(event.payload, ErrorInfo.class);
        String key = errorInfo.getErrorKey();
        switch (key){
            case ...:
                throw new BadRequestAlertException("", <<reason>>, key);
                break;
        }
    end note
    deactivate ErrorKey
    UI_Page --> User: show error
end

GW_CrudService --> GW_CrudEndpoint: return Object or List Object
deactivate GW_CrudService

GW_CrudEndpoint --> UI_Page: return JSON data
deactivate GW_CrudEndpoint

UI_Page --> User: show data success
deactivate UI_Page
deactivate User

@enduml
