@startuml
skinparam defaultFontName Tahoma
title <u>User Module</u>

package vn.vnpt.oneiot.core.jpa {
     package umgr.endpoints {
        class UserEndpoint{
            + UserEndpoint(UserService service, EventBus eventBus)
            + void process(Event event)
        }
     }

     package umgr.repositories {
        interface UserRepository {
            User findOneByEmailIgnoreCase(String email)
            User findOneWithRolesByEmail(String email)
            List<User> findAllByActiveInAndUpdatedLessThan(int[] active, long deleteTime)
            User findFirstById(Long id)
            List<User> findAllByTenantId(Long tenantId)
        }
     }

     package umgr.models {
        class User {
            - String firstName
            - String lastName
            - Long tenantId
            - ...
            + get/set()
        }
     }

     package umgr.services{
        class UserService {
            - MailService mailService
            - RoleService roleService
            - TenantService tenantService
            - EntityManager entityManager
            + set()//@Autowired bean
            + Event process(Event event)
            - Event handlerNoneEmail(Event event)
            - Event handlerNotMatchConfirmPassword(Event event)
            - Event handlerInvalidToken(Event event)
            - Event processCurrentUser(Event event)
            - Event processResetPassword(Event event)
            - Event processForgotPasswordInit (Event event)
            - Event processForgotPasswordFinish (Event event)
            - Event processChangePassword(Event event)
            - Event processValidateTokenMail(Event event)
            # Event processCreate(Event event)
            + User authenticate(String email, String password)
            + User findByEmail(String email)
            + User current(String token)
            # void beforeCreate(User entity)
            # void afterCreate(User entity)
            # void beforeUpdate(User entity)
            # void afterUpdate(User old, User updated)
        }

        class RoleService {
            - UserService userService
            - EntityManager entityManager
            + Event process(Event event)
            + Role getDefaultUserRole()
            + Role getTenantAdminRole()
            + String addMultipleTenantQuery(String query)
            # Event processCreate(Event event)
            # Event processUpdate(Event event)
            - Event handlerDuplicateName(Event event)
            # Event processDelete(Event event)
            - boolean checkRoleExistUser(Long roleId)
            # void beforeCreate(Role entity)
        }

        class MailService {
            # void sendCreationEmail(User userEntity)
        }
     }

     class CrudEndpoint {
        # CrudService<T,ID> service
        # EventBus eventBus
        + CrudEndpoint(CrudService service, EventBus eventBus)
        + void process(Event event)
     }

     class CrudService {
        # CustomJpaRepository<T,ID> repository
        - Class<T> typeParameterClass
        + Event process(Event event)
        # Event processCreate(Event event)
        # Event processUpdate(Event event)
        # Event processDelete(Event event)
        # Event processBatchDelete(Event event)
        # Event processActive(Event event)
        # Event processDeActive(Event event)
        # Event processGetOne(Event event)
        # Event processSearch(Event event)
        # String addMultipleTenantQuery(String query)
        + T create(T entity)
        + T update(ID id, T entity)
        + void delete(T entity)
        + void deleteById(ID id)
        + void softDelete(ID id)
        # void beforeCreate(T entity)
        # void afterCreate(T entity)
        # void beforeUpdate(T entity)
        # void afterUpdate(T entity)
        # void beforeDelete(T entity)
        # void afterDelete(T entity)
        # void beforeSoftDelete(T entity)
        # void afterSoftDelete(T entity)
        + void active(ID id)
        + void deactivate(ID id)
     }

     interface CrudRepository{

     }

     class IdEntity{
        - Long id
        + get/set()
     }

     class AbstractEntity{
        - Long created
        - Long updated
        - String createdBy
        - String updatedBy
        - Integer active
        + get/set()
     }
}

AbstractEntity <|- IdEntity

IdEntity <|-- User


CrudRepository <|-- UserRepository

CrudService <|-- UserService

CrudEndpoint <|-- UserEndpoint

note as N1
    - T is symbol generic class
        extends AbstractEntity
    - ID is symbol generic class
        extends Serializable
    - if functions in child class
        same name of parent class,
        it overrides functions
        in parent class
end note

@enduml
