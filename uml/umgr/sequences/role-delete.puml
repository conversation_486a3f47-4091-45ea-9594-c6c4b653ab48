@startuml
skinparam defaultFontName Verdana
title Delete Role
hide footbox
autonumber "<b>[0]"

actor User
boundary HomeRolePage
control ApiGateway

control RoleEndpoint
control RoleService
control RoleRepository

User -> HomeRolePage: fill info\nand click create
activate User
activate HomeRolePage

HomeRolePage -> ApiGateway: $http request
activate ApiGateway
note left
    method: DELETE
    url: /api/roleEntities/{id}
end note

ApiGateway -> RoleEndpoint: RequestUtils.amqp(event)
activate RoleEndpoint
note left
    IdInfo idInfo = new IdInfo(id)
    Event event{
        method: delete
        payload: ObjectMapperUtil.toStringJson(idInfo)
        routingKey: "routingKey.User"
    }
end note

RoleEndpoint -> RoleService: process(Event event)
activate RoleService

RoleService -> RoleService: processDelete(Event event)
note left
    @Override
    protected Event processDelete(Event event){
        //check roleEntity created by SYSTEM
        ...
        // check roleEntity in used
        ...
    }
end note

alt case userEntity created by SYSTEM
    RoleService --> RoleEndpoint: return event
    note left
        Build Event contains ErrorInfo
            errorInfo.setErrorKey(ErrorKey.CommonErrorKey.REMOVE_SYSTEM_ENTITY);
            event.statusCode = Constants.ResultStatus.ERROR;
    end note

    RoleEndpoint --> ApiGateway: processError(Event event)
    note left
        ErrorInfo errorInfo = ObjectMapperUtil.objectMapper(event.payload, ErrorInfo.class);
        String key = errorInfo.getErrorKey();
        switch (key){
            case key:
                throw new BadRequestAlertException("" , "Remove entity created by System", key);
                break;
        }
    end note

    ApiGateway --> HomeRolePage: throw new BadRequestAlertException("" , "Remove entity created by System", key);

    HomeRolePage --> User: show log error
end

alt case Role In Used
    RoleService --> RoleEndpoint: return event
    note left
        Build Event contains ErrorInfo
            errorInfo.setErrorKey(ErrorKey.CommonErrorKey.EXIST_USER_USE_ROLE);
            event.statusCode = Constants.ResultStatus.ERROR;
    end note

    RoleEndpoint --> ApiGateway: processError(Event event)
    note left
        ErrorInfo errorInfo = ObjectMapperUtil.objectMapper(event.payload, ErrorInfo.class);
        String key = errorInfo.getErrorKey();
        switch (key){
            case key:
                throw new BadRequestAlertException("" , "Exist User use this Role", key);
                break;
        }
    end note

    ApiGateway --> HomeRolePage: throw new BadRequestAlertException("" , "Exist User use this Role", key);

    HomeRolePage --> User: show log error
end

RoleService -> RoleRepository: delete(Role entity)
activate RoleRepository

RoleRepository -> RoleRepository: delete(Role entity)

RoleRepository --> RoleService: return void
deactivate RoleRepository

RoleService --> RoleEndpoint: return event
deactivate RoleService

note left
    Event event {
        errorCode: Constants.ResultStatus.SUCCESS
    }
end note

RoleEndpoint --> ApiGateway: return event
deactivate RoleEndpoint

ApiGateway --> HomeRolePage: return HTTP success message code
deactivate ApiGateway

HomeRolePage --> User: show message success
deactivate HomeRolePage
deactivate User

@enduml
