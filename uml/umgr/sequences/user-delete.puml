@startuml
skinparam defaultFontName Verdana
title Delete User
hide footbox
autonumber "<b>[0]"

actor User
boundary HomeUserPage
control ApiGateway

control UserEndpoint
control UserService
control UserRepository

User -> HomeUserPage: fill info\nand click create
activate User
activate HomeUserPage

HomeUserPage -> ApiGateway: $http request
activate ApiGateway
note left
    method: DELETE
    url: /api/userEntities/{id}
end note

ApiGateway -> UserEndpoint: RequestUtils.amqp(event)
activate UserEndpoint
note left
    IdInfo idInfo = new IdInfo(id)
    Event event{
        method: delete
        payload: ObjectMapperUtil.toStringJson(idInfo)
        routingKey: "routingKey.User"
    }
end note

UserEndpoint -> UserService: process(Event event)
activate UserService

UserService -> UserService: processDelete(Event event)
note left
    protected Event processDelete(Event event){
        //check userEntity created by SYSTEM
        ...
    }
end note

alt case userEntity created by SYSTEM
    UserService --> UserEndpoint: return event
    note left
        Build Event contains ErrorInfo
            errorInfo.setErrorKey(ErrorKey.CommonErrorKey.REMOVE_SYSTEM_ENTITY);
            event.statusCode = Constants.ResultStatus.ERROR;
    end note

    UserEndpoint --> ApiGateway: processError(Event event)
    note left
        ErrorInfo errorInfo = ObjectMapperUtil.objectMapper(event.payload, ErrorInfo.class);
        String key = errorInfo.getErrorKey();
        switch (key){
            case key:
                throw new BadRequestAlertException("" , "Remove entity created by System", key);
                break;
        }
    end note

    ApiGateway --> HomeUserPage: throw new BadRequestAlertException("" , "Remove entity created by System", key);

    HomeUserPage --> User: show log error
end

UserService -> UserService: beforeSoftDelete(User entity)
note left
    // @Override CrudService function beforeSoftDelete
    entity.setActive(Constants.EntityStatus.DELETED);
end note

UserService -> UserRepository: save(User entity)
activate UserRepository

UserRepository -> UserRepository: save(User entity)

UserRepository --> UserService: return userEntity
deactivate UserRepository

UserService --> UserEndpoint: return event
deactivate UserService

note left
    Event event {
        errorCode: Constants.ResultStatus.SUCCESS
    }
end note

UserEndpoint --> ApiGateway: return event
deactivate UserEndpoint

ApiGateway --> HomeUserPage: return HTTP success message code
deactivate ApiGateway

HomeUserPage --> User: show message success
deactivate HomeUserPage
deactivate User

@enduml
