@startuml
skinparam defaultFontName Verdana
title Create Role
hide footbox
autonumber "<b>[0]"

actor User
boundary CreateRolePage
control ApiGateway

control RoleEndpoint
control RoleService
control RoleRepository

User -> CreateRolePage: fill info\nand click create
activate User
activate CreateRolePage

CreateRolePage -> ApiGateway: $http request
activate ApiGateway
note left
    method: POST
    url: /api/roleEntities
    bodyRequest{
        name:...,
        description: ...,
        privileges: [...]
    }
end note

ApiGateway -> RoleEndpoint: RequestUtils.amqp(event)
activate RoleEndpoint
note left
    Event event{
        method: create
        payload: ObjectMapperUtil.toStringJson(roleEntity)
        routingKey: "routingKey.Role"
    }
end note

RoleEndpoint -> RoleService: process(Event event)
activate RoleService

RoleService -> RoleService: beforeCreate(Role entity)
note left
    @Override
    protected void beforeCreate(Role entity){
        //set tenantId for entity
        super.beforeCreate(entity);
        entity.setTenantId(SecurityUtils.getTenantId());
    }
end note

RoleService -> RoleService: processCreate(Event event)
note left
    @Override
    protected Event processCreate(Event event){
        //check duplicates name
        ...
        return super.processCreate(event);
    }
end note

alt case Duplicates Name
    RoleService --> RoleEndpoint: return event
    note left
        Build Event contains ErrorInfo
            errorInfo.setErrorKey(ErrorKey.RoleErrorKey.DUPLICATE_ROLE_NAME);
            event.statusCode = Constants.ResultStatus.ERROR;
    end note

    RoleEndpoint --> ApiGateway: processError(Event event)
    note left
        ErrorInfo errorInfo = ObjectMapperUtil.objectMapper(event.payload, ErrorInfo.class);
        String key = errorInfo.getErrorKey();
        switch (key){
            case key:
                throw new BadRequestAlertException("", "Duplicates Role Name", key);
                break;
        }
    end note

    ApiGateway --> CreateRolePage: throw new BadRequestAlertException("", "Duplicates Role Name", key);

    CreateRolePage --> User: show log error
end

RoleService -> RoleRepository: save(Role entity)
activate RoleRepository

RoleRepository -> RoleRepository: save(Role entity)

RoleRepository --> RoleService: return roleEntity
deactivate RoleRepository

RoleService --> RoleEndpoint: return event
deactivate RoleService
note left
    Event event {
        payload: ObjectMapperUtil.toStringJson(roleEntity),
        errorCode: Constants.ResultStatus.SUCCESS
    }
end note

RoleEndpoint --> ApiGateway: return event
deactivate RoleEndpoint

ApiGateway --> CreateRolePage: return JSON roleEntity
deactivate ApiGateway

CreateRolePage --> User: show message success
deactivate CreateRolePage
deactivate User

@enduml
