@startuml
skinparam defaultFontName Verdana
title Update User
hide footbox
autonumber "<b>[0]"

actor User
boundary UpdateUserPage
control ApiGateway

control UserEndpoint
control UserService
control UserRepository

User -> UpdateUserPage: fill info\nand click update
activate User
activate UpdateUserPage

UpdateUserPage -> ApiGateway: $http request
activate ApiGateway
note left
    method: PUT
    url: /api/userEntities/{id}
    bodyRequest{
        id: ...,
        firstName: ...,
        lastName: ...,
        companyName: ...,
        listRole: [...]
        ...
    }
end note

ApiGateway -> UserEndpoint: RequestUtils.amqp(event)
activate UserEndpoint
note left
    Event event{
        method: update
        payload: ObjectMapperUtil.toStringJson(userEntity)
        routingKey: "routingKey.User"
    }
end note

UserEndpoint -> UserService: process(Event event)
activate UserService

UserService -> UserService: processUpdate(Event event)

UserService -> UserService: beforeUpdate(User entity)
note left
    // @Override CrudService function beforeUpdate
    //set password again
    //check default roleEntity ROLE_USER and add if not exist
end note

UserService -> UserRepository: save(User entity)
activate UserRepository

UserRepository -> UserRepository: save(User entity)

UserRepository --> UserService: return userEntity
deactivate UserRepository

UserService -> UserService: afterUpdate(User entity)
note left
    // @Override CrudService function afterUpdate
    // change status tenantEntity follow status of userEntity
    Tenant tenantEntity = tenantRepository.findFirstById(entity.getTenantId());
    if(tenantEntity != null){
        tenantEntity.setActive(entity.getActive());
        tenantRepository.save(tenantEntity);
    }
end note

UserService --> UserEndpoint: return event
deactivate UserService

note left
    Event event {
        payload: ObjectMapperUtil.toStringJson(userEntity),
        errorCode: Constants.ResultStatus.SUCCESS
    }
end note

UserEndpoint --> ApiGateway: return event
deactivate UserEndpoint

ApiGateway --> UpdateUserPage: return JSON userEntity
deactivate ApiGateway

UpdateUserPage --> User: show message success
deactivate UpdateUserPage
deactivate User

@enduml
