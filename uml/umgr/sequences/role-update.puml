@startuml
skinparam defaultFontName Verdana
title Update Role
hide footbox
autonumber "<b>[0]"

actor User
boundary UpdateRolePage
control ApiGateway

control RoleEndpoint
control RoleService
control RoleRepository

User -> UpdateRolePage: fill info\nand click update
activate User
activate UpdateRolePage

UpdateRolePage -> ApiGateway: $http request
activate ApiGateway
note left
    method: PUT
    url: /api/roleEntities/{id}
    bodyRequest{
        id: ...,
        name: ...,
        description: ...,
        privileges: [...]
    }
end note

ApiGateway -> RoleEndpoint: RequestUtils.amqp(event)
activate RoleEndpoint
note left
    Event event{
        method: update
        payload: ObjectMapperUtil.toStringJson(roleEntity)
        routingKey: "routingKey.Role"
    }
end note

RoleEndpoint -> RoleService: process(Event event)
activate RoleService

RoleService -> RoleService: processUpdate(Event event)
note left
    @Override
    protected Event processUpdate(Event event){
        //check duplicates name
        ...
        return super.processUpdate(event);
    }
end note

alt case Duplicates Name
    RoleService --> RoleEndpoint: return event
    note left
        Build Event contains ErrorInfo
            errorInfo.setErrorKey(ErrorKey.RoleErrorKey.DUPLICATE_ROLE_NAME);
            event.statusCode = Constants.ResultStatus.ERROR;
    end note

    RoleEndpoint --> ApiGateway: processError(Event event)
    note left
        ErrorInfo errorInfo = ObjectMapperUtil.objectMapper(event.payload, ErrorInfo.class);
        String key = errorInfo.getErrorKey();
        switch (key){
            case key:
                throw new BadRequestAlertException("", "Duplicates Role Name", key);
                break;
        }
    end note

    ApiGateway --> UpdateRolePage: throw new BadRequestAlertException("", "Duplicates Role Name", key);

    UpdateRolePage --> User: show log error
end

RoleService -> RoleRepository: save(Role entity)
activate RoleRepository

RoleRepository -> RoleRepository: save(Role entity)

RoleRepository --> RoleService: return roleEntity
deactivate RoleRepository

RoleService --> RoleEndpoint: return event
deactivate RoleService
note left
    Event event {
        payload: ObjectMapperUtil.toStringJson(roleEntity),
        errorCode: Constants.ResultStatus.SUCCESS
    }
end note

RoleEndpoint --> ApiGateway: return event
deactivate RoleEndpoint

ApiGateway --> UpdateRolePage: return JSON roleEntity
deactivate ApiGateway

UpdateRolePage --> User: show message success
deactivate UpdateRolePage
deactivate User

@enduml
