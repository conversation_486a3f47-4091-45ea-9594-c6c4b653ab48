@startuml
skinparam defaultFontName Verdana
title Create User
hide footbox
autonumber "<b>[0]"

actor User
boundary CreateUserPage
control ApiGateway

control UserEndpoint
control UserService
control UserRepository
control MailService

User -> CreateUserPage: fill info\nand click create
activate User
activate CreateUserPage

CreateUserPage -> ApiGateway: $http request
activate ApiGateway
note left
    method: POST
    url: /api/userEntities
    bodyRequest{
        firstName:...,
        lastName: ...,
        companyName: ...,
        listRole: [...]
        ...
    }
end note

ApiGateway -> UserEndpoint: RequestUtils.amqp(event)
activate UserEndpoint
note left
    Event event{
        method: create
        payload: ObjectMapperUtil.toStringJson(userEntity)
        routingKey: "routingKey.User"
    }
end note

UserEndpoint -> UserService: process(Event event)
activate UserService

UserService -> UserService: processCreate(Event event)
note left
    @Override
    protected Event processCreate(Event event){
        //check duplicates email
        ...
        return super.processCreate(event);
    }
end note

alt case Duplicates Email
    UserService --> UserEndpoint: return event
    note left
        Build Event contains ErrorInfo
            errorInfo.setErrorKey(ErrorKey.UserErrorKey.DUPLICATE_EMAIL);
            event.statusCode = Constants.ResultStatus.ERROR;
    end note

    UserEndpoint --> ApiGateway: processError(Event event)
    note left
        ErrorInfo errorInfo = ObjectMapperUtil.objectMapper(event.payload, ErrorInfo.class);
        String key = errorInfo.getErrorKey();
        switch (key){
            case key:
                throw new BadRequestAlertException("", "Duplicates Email", key);
                break;
        }
    end note

    ApiGateway --> CreateUserPage: throw new BadRequestAlertException("", "Duplicates Email", key);

    CreateUserPage --> User: show log error
end

UserService -> UserService: beforeCreate(User entity)
note left
    // @Override CrudService function beforeCreate
    //set password
    String password = Common.randomString(8);
    entity.setEncryptedPassword(password);
    //set status active
    entity.setActive(Constants.EntityStatus.REGISTER);
    //set tenantId
    entity.setTenantId(SecurityUtils.getTenantId());
end note

UserService -> UserRepository: save(User entity)
activate UserRepository

UserRepository -> UserRepository: save(User entity)

UserRepository --> UserService: return userEntity
deactivate UserRepository

UserService -> UserService: afterCreate(User entity)
note left
    // @Override CrudService function afterCreate
    // create roleEntity for userEntity
    // send mail
end note

UserService -> MailService: sendCreationEmail(User entity)
activate MailService

MailService --> UserService
deactivate MailService

UserService --> UserEndpoint: return event
deactivate UserService

note left
    Event event {
        payload: ObjectMapperUtil.toStringJson(userEntity),
        errorCode: Constants.ResultStatus.SUCCESS
    }
end note

UserEndpoint --> ApiGateway: return event
deactivate UserEndpoint

ApiGateway --> CreateUserPage: return JSON userEntity
deactivate ApiGateway

CreateUserPage --> User: show message success
deactivate CreateUserPage
deactivate User

@enduml
