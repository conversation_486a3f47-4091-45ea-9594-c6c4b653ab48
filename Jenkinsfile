pipeline {
    agent {
        label 'IOT_BUILD_NODE'
    }

    environment {
        SERVICE_NAME = "iot-core-authentication-authorization"
    }

    stages {
        stage('Build Code Artifact') {
            steps {
                withCredentials([
                    usernamePassword(credentialsId: 'STC_IOT_NEXUS_ACCESS',
                    usernameVariable: 'username',
                    passwordVariable: 'password')
                ]) {
                sh "mvn clean package -U -DskipTests -s mvn-setting.xml -DSERVER_USERNAME=${username} -DSERVER_PASSWORD=${password}"
                }
            }
        }
        stage('Deploy to Nexus Dev/Staging') {
            when {
                expression { return (env.TAG_NAME==null) }
            }
            steps {
                withCredentials([
                    usernamePassword(credentialsId: 'STC_IOT_NEXUS_ACCESS',
                    usernameVariable: 'username',
                    passwordVariable: 'password')
                ]) {
                    sh "docker build -t rdrepo.vnpt-technology.vn:1117/stc_iot/${SERVICE_NAME}:local-${GIT_COMMIT} ."
                    sh "docker login --username ${username} --password ${password} rdrepo.vnpt-technology.vn:1117"
                  	sh "docker push rdrepo.vnpt-technology.vn:1117/stc_iot/${SERVICE_NAME}:local-${GIT_COMMIT}"
                }
            }
        }

        stage('Deploy to Nexus Production') {
            when {
                expression { return (env.TAG_NAME!=null) }
            }
            steps {
                withCredentials([
                    usernamePassword(credentialsId: 'STC_IOT_NEXUS_ACCESS',
                    usernameVariable: 'username',
                    passwordVariable: 'password')
                ]) {
                    sh "docker build -t rdrepo.vnpt-technology.vn:1117/stc_iot/${SERVICE_NAME}:local-${GIT_COMMIT} ."
                    sh "docker login --username ${username} --password ${password} rdrepo.vnpt-technology.vn:1117"
                  	sh "docker push rdrepo.vnpt-technology.vn:1117/stc_iot/${SERVICE_NAME}:local-${GIT_COMMIT}"
                }
            }
        }
    }
}