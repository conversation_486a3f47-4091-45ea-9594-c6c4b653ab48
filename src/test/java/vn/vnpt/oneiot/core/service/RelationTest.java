package vn.vnpt.oneiot.core.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import vn.vnpt.oneiot.core.generic.Constants;
import vn.vnpt.oneiot.core.mongo.entity.IdentityEntity;
import vn.vnpt.oneiot.core.mongo.entity.RelationEntity;
import vn.vnpt.oneiot.core.mongo.services.RelationService;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class RelationTest {

    @Autowired
    RelationService relationService;

    @Test
    public void create() {
        IdentityEntity origin = new IdentityEntity();
        IdentityEntity taget = new IdentityEntity();
        origin.setId("originTestID");
        origin.setName("originTestName");
        origin.setType(Constants.IDENTITY_TYPE.DEVICE_IDENTITY_TYPE);
        taget.setId("tagetTestID");
        taget.setName("tagetTestName");
        taget.setType(Constants.IDENTITY_TYPE.DEVICE_IDENTITY_TYPE);
        relationService.createRelationByIdentity(origin, taget, Constants.RELATION_TYPE.PARENT);
    }

    @Test
    public void Search(){
        List<RelationEntity> lists =  relationService.findAllByOriginatorIdAndRelationTypeAndActive("originTestID1", Constants.RELATION_TYPE.PARENT, 1);
        for (RelationEntity list : lists){
            System.out.println(list.getTargetName());
        }
    }

}
