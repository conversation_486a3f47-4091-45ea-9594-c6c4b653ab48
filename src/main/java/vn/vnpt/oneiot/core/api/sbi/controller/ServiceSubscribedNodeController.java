package vn.vnpt.oneiot.core.api.sbi.controller;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import vn.vnpt.oneiot.base.utils.DateUtils;
import vn.vnpt.oneiot.common.constants.MimeMediaType;
import vn.vnpt.oneiot.common.constants.ResourceType;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.common.entities.ServiceSubscribedAppRuleEntity;
import vn.vnpt.oneiot.common.entities.ServiceSubscribedNodeEntity;
import vn.vnpt.oneiot.common.entities.TransactionEntity;
import vn.vnpt.oneiot.common.entitymapper.EntityMapperFactory;
import vn.vnpt.oneiot.common.exceptions.BadRequestException;
import vn.vnpt.oneiot.common.exceptions.ConflictException;
import vn.vnpt.oneiot.common.exceptions.ResourceNotFoundException;
import vn.vnpt.oneiot.common.resource.RequestPrimitive;
import vn.vnpt.oneiot.common.resource.ResponsePrimitive;
import vn.vnpt.oneiot.common.resource.ServiceSubscribedNode;
import vn.vnpt.oneiot.common.utils.ControllerUtil;
import vn.vnpt.oneiot.common.utils.DateConverter;
import vn.vnpt.oneiot.common.utils.ResourceUtils;
import vn.vnpt.oneiot.core.mongo.repositories.ServiceSubscribedAppRuleRepository;
import vn.vnpt.oneiot.core.mongo.services.ServiceSubscribedNodeService;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Controller
@Transactional
public class ServiceSubscribedNodeController extends ResourceRequestController {
    static final Logger logger = LoggerFactory.getLogger(ServiceSubscribedNodeController.class);
    private ServiceSubscribedNodeService serviceSubscribedNodeService;
    private ServiceSubscribedAppRuleRepository serviceSubscribedAppRuleRepository;

    @Autowired
    public void setServiceSubscribedAppRuleRepository(ServiceSubscribedAppRuleRepository serviceSubscribedAppRuleRepository) {
        this.serviceSubscribedAppRuleRepository = serviceSubscribedAppRuleRepository;
    }

    @Autowired
    public void setServiceSubscribedNodeService(ServiceSubscribedNodeService serviceSubscribedNodeService) {
        this.serviceSubscribedNodeService = serviceSubscribedNodeService;
    }

    @Override
    public ResponsePrimitive doCreate(RequestPrimitive request) {
        ResponsePrimitive responsePrimitive = new ResponsePrimitive(request);
        /** Check content */
        if (request.getContent() == null) {
            throw new BadRequestException("A content is required for ServiceSubscribedNode creation");
        }

        ServiceSubscribedNode svsn = null;
        try {
            svsn = new Gson().fromJson(request.getContent(), ServiceSubscribedNode.class);
        } catch (JsonSyntaxException e) {
            logger.debug("ClassCastException: Incorrect resource type in object conversion");
            throw new BadRequestException("Incorrect resource representation in content", e);
        }
        if (svsn == null) {
            throw new BadRequestException("Error in provided content");
        }
        ServiceSubscribedNodeEntity svsnEntity = new ServiceSubscribedNodeEntity();
        ControllerUtil.CreateUtil.fillEntityFromRegularResource(svsn, svsnEntity);

        /** MANDATORY attribute in CREATE request */
        if (ControllerUtil.isPermittedCreated(svsn, "nodeID", svsn.getNodeID())) {
            svsnEntity.setNodeID(svsn.getNodeID());
        } else {
            throw new BadRequestException("Node Id is Mandatory");
        }

        /** OPTIONAL attribute in CREATE request */
        if (ControllerUtil.isPermittedCreated(svsn, "cseid", svsn.getCSEID())) {
            svsnEntity.setCseid(svsn.getCSEID());
        }
        if (ControllerUtil.isPermittedCreated(svsn, "deviceIdentifier", svsn.getDeviceIdentifier())) {
            svsnEntity.setDeviceIdentifier(svsn.getDeviceIdentifier());
        }
        if (ControllerUtil.isPermittedCreated(svsn, "ruleLinks", svsn.getRuleLinks())) {
            svsnEntity.setRuleLinks(svsn.getRuleLinks());
            /** Update service subscribed app rule linked in ruleLinks **/
            List<ServiceSubscribedAppRuleEntity> asarEntitys = new ArrayList<>();
            for (String ruleId : svsnEntity.getRuleLinks()) {
                Optional<ServiceSubscribedAppRuleEntity> opAsarEntity = serviceSubscribedAppRuleRepository.findById(ruleId);
                if (opAsarEntity.isPresent()) {
                    ServiceSubscribedAppRuleEntity asarEntity = opAsarEntity.get();
                    if (asarEntity.getServiceSubscribedNodeLinks() == null) asarEntity.setServiceSubscribedNodeLinks(new ArrayList<>());
                    asarEntity.getServiceSubscribedNodeLinks().add(svsnEntity.getResourceID());
                    asarEntitys.add(asarEntity);
                } else {
                    throw new BadRequestException("Rule is linked is not exist " + ruleId);
                }
            }
            serviceSubscribedAppRuleRepository.saveAll(asarEntitys);
        }

        svsnEntity.setResourceID(ResourceUtils.generateResourceId(ResourceType.SERVICE_SUBSCRIBED_NODE));
        if (svsn.getName() != null && !svsn.getName().isEmpty()) {
            svsnEntity.setName(svsn.getName());
            if (serviceSubscribedNodeService.getOneByParentIdAndName(request.getTo(), svsn.getName()) != null) {
                throw new ConflictException("The same resource name is already existed");
            }
        } else {
            svsnEntity.setName(svsnEntity.getResourceID());
        }

        svsnEntity.setHierarchicalURI(request.getHuriTarget() + "/" + svsnEntity.getName());
        svsnEntity.setParentID(request.getTo());
        svsnEntity.setResourceType(ResourceType.SERVICE_SUBSCRIBED_NODE);
        svsnEntity = serviceSubscribedNodeService.create(svsnEntity);
        responsePrimitive.setResponseStatusCode(ResponseStatusCode.CREATED);
        responsePrimitive.setResourceEntity(new Gson().toJson(svsnEntity));
        responsePrimitive.setResourceType(ResourceType.SERVICE_SUBSCRIBED_NODE);
        setLocationAndCreationContent(request, responsePrimitive, svsnEntity);
        orchestrationService.saveTransaction(responsePrimitive, svsnEntity, TransactionEntity.EVENT_CREATED);
        return responsePrimitive;
    }

    @Override
    public ResponsePrimitive doRetrieve(RequestPrimitive request) {
        ResponsePrimitive response = new ResponsePrimitive(request);
        ServiceSubscribedNodeEntity svsnEntity = serviceSubscribedNodeService.get(request.getTo());
        if (svsnEntity == null) {
            throw new ResourceNotFoundException();
        }
        ServiceSubscribedNode svsn = EntityMapperFactory.getServiceSubscribedNodeMapper().mapEntityToResource(svsnEntity, request);
        response.setContent(new Gson().toJson(svsn));
        response.setResourceType(ResourceType.SERVICE_SUBSCRIBED_NODE);
        response.setResponseStatusCode(ResponseStatusCode.OK);
        return response;
    }

    @Override
    public ResponsePrimitive doUpdate(RequestPrimitive request) {
        ResponsePrimitive response = new ResponsePrimitive(request);

        // Retrieve the resource from DB
        ServiceSubscribedNodeEntity svsnEntity = serviceSubscribedNodeService.get(request.getTo());

        // Check resource existence
        if (svsnEntity == null){
            throw new ResourceNotFoundException("Resource " + request.getTo() + " not found.");
        }

        // Check if content is present
        if (request.getContent() == null){
            throw new BadRequestException("A content is required for ServiceSubscribedNode update");
        }

        // Create the java object from the resource representation
        ServiceSubscribedNode svsn = null;
        ServiceSubscribedNode modifiedAttributes = new ServiceSubscribedNode();
        try{
            svsn = new Gson().fromJson(request.getContent(), ServiceSubscribedNode.class);
        } catch (ClassCastException e){
            logger.debug("ClassCastException: Incorrect resource type in object conversion.",e);
            throw new BadRequestException("Incorrect resource representation in content", e);
        }
        if (svsn == null) {
            throw new BadRequestException("Error in provided content");
        }

        // NP attributes
        // @resourceName 		NP
        // resourceType 		NP
        // resourceID 			NP
        // parentID 			NP
        // creationTime 		NP
        // lastModifiedTime 	NP
        ControllerUtil.UpdateUtil.fillEntityFromRegularResource(svsn, svsnEntity, modifiedAttributes);

        /** NOT PRESENT attribute in UPDATE request */
        ControllerUtil.validateNP(svsn, "nodeID");
        ControllerUtil.validateNP(svsn, "cseid");
        ControllerUtil.validateNP(svsn, "deviceIdentifier");


        /** OPTIONAL attribute in UPDATE request */
        if (ControllerUtil.isPermittedUpdated(svsn, "ruleLinks")) {
            svsnEntity.setRuleLinks(svsn.getRuleLinks());
            modifiedAttributes.setRuleLinks(svsn.getRuleLinks());
            if (svsnEntity.getRuleLinks() != null) {
                /** Update service subscribed app rule linked in ruleLinks **/
                List<ServiceSubscribedAppRuleEntity> asarEntitys = new ArrayList<>();
                for (String ruleId : svsnEntity.getRuleLinks()) {
                    Optional<ServiceSubscribedAppRuleEntity> opAsarEntity = serviceSubscribedAppRuleRepository.findById(ruleId);
                    if (opAsarEntity.isPresent()) {
                        ServiceSubscribedAppRuleEntity asarEntity = opAsarEntity.get();
                        if (asarEntity.getServiceSubscribedNodeLinks() == null) asarEntity.setServiceSubscribedNodeLinks(new ArrayList<>());
                        if (!asarEntity.getServiceSubscribedNodeLinks().contains(svsnEntity.getResourceID())) {
                            asarEntity.getServiceSubscribedNodeLinks().add(svsnEntity.getResourceID());
                            asarEntitys.add(asarEntity);
                        }
                    } else {
                        throw new BadRequestException("Rule is linked is not exist " + ruleId);
                    }
                }
                serviceSubscribedAppRuleRepository.saveAll(asarEntitys);
            }
        }

        svsnEntity = serviceSubscribedNodeService.update(svsnEntity);
        modifiedAttributes.setLastModifiedTime(DateConverter.convertUTCLongtoUTCString(svsnEntity.getLastModifiedTime()));;
        response.setContent(new Gson().toJson(modifiedAttributes));
        response.setResourceEntity(new Gson().toJson(svsnEntity));
        response.setResourceType(ResourceType.SERVICE_SUBSCRIBED_NODE);
        response.setResponseStatusCode(ResponseStatusCode.UPDATED);
        orchestrationService.saveTransaction(response, svsnEntity, TransactionEntity.EVENT_UPDATE);
        return response;
    }

    @Override
    public ResponsePrimitive doDelete(RequestPrimitive request) {
        ResponsePrimitive response = new ResponsePrimitive(request);
        ServiceSubscribedNodeEntity svsnEntity = serviceSubscribedNodeService.get(request.getTo());
        if (svsnEntity == null) {
            throw new ResourceNotFoundException();
        }
        serviceSubscribedNodeService.delete(svsnEntity.getResourceID());
        response.setResourceEntity(new Gson().toJson(svsnEntity));
        response.setResponseStatusCode(ResponseStatusCode.DELETED);
        orchestrationService.saveTransaction(response, svsnEntity, TransactionEntity.EVENT_DELETE);
        return response;
    }
}
