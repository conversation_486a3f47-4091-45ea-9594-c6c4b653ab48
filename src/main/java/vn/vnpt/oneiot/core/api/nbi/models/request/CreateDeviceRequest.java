package vn.vnpt.oneiot.core.api.nbi.models.request;

import java.util.List;

public class CreateDeviceRequest {
    private String mainAppId;
    private String tenantId;
    private String userId;
    private String appId;
    private List<DeviceRequest> deviceList;

    public CreateDeviceRequest() {
    }

    public CreateDeviceRequest(String tenantId, String userId, List<DeviceRequest> deviceList) {
        this.tenantId = tenantId;
        this.userId = userId;
        this.deviceList = deviceList;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public List<DeviceRequest> getDeviceList() {
        return deviceList;
    }

    public void setDeviceList(List<DeviceRequest> deviceList) {
        this.deviceList = deviceList;
    }

    public String getMainAppId() {
        return mainAppId;
    }

    public void setMainAppId(String mainAppId) {
        this.mainAppId = mainAppId;
    }
}
