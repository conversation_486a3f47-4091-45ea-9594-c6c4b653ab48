package vn.vnpt.oneiot.core.api.nbi.models.response;

import vn.vnpt.oneiot.core.api.nbi.models.DeviceBaseResponse;

/**
 * Created by HIEUDT on 12/16/2019.
 */
public class DeviceCreateResponse extends DeviceBaseResponse {
    public String accessToken;
    public String refreshToken;
    public String category;
    public Long expiry;
    public String gatewayId;

    public DeviceCreateResponse(String deviceName, String deviceId, Integer deviceType, String accessToken,
                                String refreshToken, Long expiry, String category, String gatewayId) {
        super(deviceName, deviceId, deviceType);
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.expiry = expiry;
        this.category = category;
        this.gatewayId = gatewayId;
    }

    public String getCategory() {
        return category;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public Long getExpiry() {
        return expiry;
    }

    public void setExpiry(Long expiry) {
        this.expiry = expiry;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getGatewayId() {
        return gatewayId;
    }

    public void setGatewayId(String gatewayId) {
        this.gatewayId = gatewayId;
    }
}
