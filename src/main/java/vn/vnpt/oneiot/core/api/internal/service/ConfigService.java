package vn.vnpt.oneiot.core.api.internal.service;

import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.event.EventBus;
import vn.vnpt.oneiot.base.redis.ObjectCache;
import vn.vnpt.oneiot.common.constants.Constants;
import vn.vnpt.oneiot.common.constants.Operation;
import vn.vnpt.oneiot.common.constants.ResourceType;
import vn.vnpt.oneiot.common.entities.TokenEntity;
import vn.vnpt.oneiot.common.resource.RequestPrimitive;
import vn.vnpt.oneiot.core.constants.TransactionState;
import vn.vnpt.oneiot.core.mongo.services.RelationService;
import vn.vnpt.oneiot.core.redis.dto.Transaction;

import java.math.BigInteger;
import java.util.UUID;

import static vn.vnpt.oneiot.base.constants.AMQPConstant.*;
import static vn.vnpt.oneiot.core.constants.Constants.CNT_CONFIG_PLATFORM;
import static vn.vnpt.oneiot.core.constants.Constants.ServiceKey;
import static vn.vnpt.oneiot.core.generic.Constants.Method.CONFIG_PLATFORM;

@Service
public class ConfigService {
    private static Logger logger = LoggerFactory.getLogger(ConfigService.class);

    @Value("${transactionTimeout}")
    private int transactionTimeout;


    @Autowired
    ObjectCache systemCache;

    @Autowired
    RelationService relationService;

    @Autowired
    EventBus eventBus;

//    Gson mapper;

    private static ConfigService configService;

    public ConfigService() {
        configService = this;
    }

    public static ConfigService shareInstance() {
        return configService;
    }

//    public ConfigService() {
//        mapper = new Gson();
//    }

    public Event processConfigPlatform(Event event) {
        return event;
    }

    public void sendTokenPlatformConfig(Event event, String targetId, String targetName, String configData, TokenEntity accessToken, TokenEntity refreshToken) {
        logger.info("send platform config for targetId: {}", targetId);
        String originatorId = targetId;
        Event eventReq = new Event();
        MessageProperties messageProperties = new MessageProperties();
        eventReq.id = CONFIG_PLATFORM + "_" + UUID.randomUUID();
        eventReq.type = EVENTTYPE_REQUEST;
        eventReq.method = CONFIG_PLATFORM;
        RequestPrimitive requestPrimitive = createPlatformConfigReqPrim(targetId, targetName, originatorId, configData);
        Transaction transaction = new Transaction();
        transaction.setReplyToRoutingKey(event.routingKeyReplyTo);
        transaction.setRequestPrimitive(requestPrimitive);
        transaction.setState(TransactionState.BEGIN);
        transaction.setType(1);
        transaction.setEventId(event.id);
        transaction.setHolderId(targetId);
        transaction.setAccessToken(accessToken);
        transaction.setRefreshToken(refreshToken);
        String transactionKey = getTransactionKey(requestPrimitive.getRequestIdentifier());
        logger.info("transactionKey send---" + transactionKey);
        systemCache.putMilisecond(transactionKey, transaction, transactionTimeout, Transaction.class);
        eventReq.payload = new Gson().toJson(requestPrimitive);
        messageProperties.setReplyTo(ROUTING_KEY_INTERNAL_AUTHORIZATION);
        eventBus.publish(getExchangeFromRoutingKey(ROUTING_KEY_INTERNAL_ORCHESTRATION), ROUTING_KEY_INTERNAL_ORCHESTRATION, eventReq, messageProperties);
    }

    public void sendRelationPlatformConfig(String targetId, String targetName, String configData) {
        logger.info("send platform config for targetId: {}", targetId);
        String originatorId = targetId;
        Event eventReq = new Event();
        MessageProperties messageProperties = new MessageProperties();
        eventReq.id = CONFIG_PLATFORM + "_" + UUID.randomUUID().toString();
        eventReq.type = EVENTTYPE_REQUEST;
        eventReq.method = CONFIG_PLATFORM;
        RequestPrimitive requestPrimitive = createPlatformConfigReqPrim(targetId, targetName, originatorId, configData);
        eventReq.payload = new Gson().toJson(requestPrimitive);
        messageProperties.setReplyTo(ROUTING_KEY_INTERNAL_AUTHORIZATION);
        eventBus.publish(getExchangeFromRoutingKey(ROUTING_KEY_INTERNAL_ORCHESTRATION), ROUTING_KEY_INTERNAL_ORCHESTRATION, eventReq, messageProperties);
    }


    private RequestPrimitive createPlatformConfigReqPrim(String targetId, String targetName, String originatorId, String configData) {
        RequestPrimitive request = new RequestPrimitive();
        request.setOperation(Operation.CREATE);
        request.setTo("/" + Constants.CSE_ID + "/" + Constants.CSE_NAME + "/" + targetName + "/" + CNT_CONFIG_PLATFORM);
        request.setFrom("/in-cse");
        request.setRequestIdentifier(UUID.randomUUID().toString());
        request.setResourceType(ResourceType.CONTENT_INSTANCE);
        request.setContent(configData);
        request.setReceivedTimestamp(BigInteger.valueOf(System.currentTimeMillis()));
        return request;
    }

    public static String getTransactionKey(String id) {
        if (!StringUtils.isEmpty(id))
            return ServiceKey + id;
        else return null;
    }

    public void publicMess(String replyTo, Event eventResp) {
        eventBus.publish(getExchangeFromRoutingKey(replyTo), replyTo, eventResp);
    }
}
