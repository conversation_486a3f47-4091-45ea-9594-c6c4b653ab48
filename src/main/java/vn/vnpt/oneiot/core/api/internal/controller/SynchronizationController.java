package vn.vnpt.oneiot.core.api.internal.controller;

import com.google.gson.Gson;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import vn.vnpt.oneiot.base.dto.SynchronizeRequest;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.event.EventBus;
import vn.vnpt.oneiot.base.mongo.entity.SynchronizeEntity;
import vn.vnpt.oneiot.base.mongo.repository.SynchronizeRepository;
import vn.vnpt.oneiot.common.constants.*;
import vn.vnpt.oneiot.common.entities.*;
import vn.vnpt.oneiot.common.resource.RegularResource;
import vn.vnpt.oneiot.common.utils.ControllerUtil;
import vn.vnpt.oneiot.core.CSEInitialize;
import vn.vnpt.oneiot.core.constants.UpdateFlagConstants;
import vn.vnpt.oneiot.core.mongo.entity.IdentityEntity;
import vn.vnpt.oneiot.core.mongo.entity.MappingAcpIdsResourceEntity;
import vn.vnpt.oneiot.core.mongo.repositories.*;
import vn.vnpt.oneiot.core.mongo.services.AccessControlPolicyService;
import vn.vnpt.oneiot.core.mongo.services.IdentityService;
import vn.vnpt.oneiot.core.utils.ResourceUtils;

import static vn.vnpt.oneiot.base.constants.AMQPConstant.*;
import static vn.vnpt.oneiot.common.constants.Constants.CSE_ID_URI;
import static vn.vnpt.oneiot.core.constants.UpdateFlagConstants.*;
import static vn.vnpt.oneiot.core.generic.Constants.IDENTITY_TYPE.GROUP_IDENTITY_TYPE;

@Controller
public class SynchronizationController {

    static final Logger logger = LoggerFactory.getLogger(SynchronizationController.class);

    @Autowired
    EventBus eventBus;
    @Autowired
    SynchronizeRepository synchronizeRepository;
    @Autowired
    MappingAcpIdsResourceRepository mappingAcpIdsResourceRepository;
    @Autowired
    AccessControlPolicyService accessControlPolicyService;
    @Autowired
    TokenRepository tokenRepository;
    @Autowired
    IdentityRepository identityRepository;
    @Autowired
    IdentityService identityService;

    public static int getUpdateFlag(int operation, int resourceType, String resourceEntity) {

        int flag = 0;
        if (operation != Operation.CREATE.intValue() &&
                operation != Operation.UPDATE.intValue() &&
                operation != Operation.DELETE.intValue())
            return 0;
        if (operation == Operation.CREATE.intValue() ||
                operation == Operation.DELETE.intValue()) {
            flag = flag | AUTHENTICATION_AUTHORIZATION_FLAG;

        }

        if (resourceType == ResourceType.AE) {
            if (operation != Operation.DELETE.intValue()) {
                AeEntity ae = new Gson().fromJson(resourceEntity, AeEntity.class);
                if (ae.getPointOfAccess() != null)
                    flag = flag | UpdateFlagConstants.SUBSCRIPTION_NOTIFICATION_FLAG;
            } else {
                flag = flag | UpdateFlagConstants.SUBSCRIPTION_NOTIFICATION_FLAG;
            }
        } else if (resourceType == ResourceType.REMOTE_CSE) {
            if (operation != Operation.DELETE.intValue()) {
                RemoteCSEEntity remoteCSE = new Gson().fromJson(resourceEntity, RemoteCSEEntity.class);
                if (remoteCSE.getPointOfAccess() != null)
                    flag = flag | UpdateFlagConstants.SUBSCRIPTION_NOTIFICATION_FLAG;
            } else {
                flag = flag | UpdateFlagConstants.SUBSCRIPTION_NOTIFICATION_FLAG;
            }
        } else if (resourceType == ResourceType.CONTENT_INSTANCE || resourceType == ResourceType.TIMESERIESINSTANCE) {
            flag = flag | SUBSCRIPTION_NOTIFICATION_FLAG;
        }
        return flag;
    }


    public void doSynchronize(int updateFlag, SynchronizeRequest synchronizeRequest) {
        Event synEvent = new Event();
        MessageProperties messageProperties = new MessageProperties();
        synEvent.id = "synchronization" + new Gson().fromJson(synchronizeRequest.resourceEntity, ResourceEntity.class).getResourceID();
        synEvent.type = EVENTTYPE_REQUEST;
        synEvent.method = SYNCHRONIZE_ONEM2M;
        synEvent.payload = new Gson().toJson(synchronizeRequest);
        messageProperties.setReplyTo(ROUTING_KEY_INTERNAL_AUTHORIZATION);
        if ((updateFlag & AUTHENTICATION_AUTHORIZATION_FLAG) > 0) {
            eventBus.publish(getExchangeFromRoutingKey(ROUTING_KEY_INTERNAL_AUTHORIZATION), ROUTING_KEY_INTERNAL_AUTHORIZATION, synEvent, messageProperties);
        }
        if ((updateFlag & SUBSCRIPTION_NOTIFICATION_FLAG) > 0) {
            eventBus.publish(getExchangeFromRoutingKey(ROUTING_KEY_INTERNAL_SUBSCRIPTION_NOTIFICATION), ROUTING_KEY_INTERNAL_SUBSCRIPTION_NOTIFICATION, synEvent, messageProperties);
        }
        if ((updateFlag & REGISTRATION_GROUP_FLAG) > 0) {
            eventBus.publish(getExchangeFromRoutingKey(ROUTING_KEY_INTERNAL_REGISTRATION_GROUP), ROUTING_KEY_INTERNAL_REGISTRATION_GROUP, synEvent, messageProperties);
        }
        if ((updateFlag & DEVICE_MANAGEMENT_FLAG) > 0) {
            eventBus.publish(getExchangeFromRoutingKey(ROUTING_KEY_INTERNAL_DEVICEMGMT), ROUTING_KEY_INTERNAL_DEVICEMGMT, synEvent, messageProperties);
        }
        if ((updateFlag & DATA_MANAGEMENT_FLAG) > 0) {
            eventBus.publish(getExchangeFromRoutingKey(ROUTING_KEY_INTERNAL_DATAMGMT), ROUTING_KEY_INTERNAL_DATAMGMT, synEvent, messageProperties);
        }
        if ((updateFlag & CHARGING_ACCOUNTING_FLAG) > 0) {
            // TODO: current have not data to synchronize this module, this for future
        }
        if ((updateFlag & APPLICATION_MANAGEMENT_FLAG) > 0) {
            // TODO: current have not data to synchronize this module, this for future
        }
    }
    @WithSpan
    public Event doSynchornizeRequest(Event event) {
        SynchronizeRequest synchronizeRequest = new Gson().fromJson(event.payload, SynchronizeRequest.class);
        if (synchronizeRequest.operation == Operation.CREATE.intValue()) {
            ConcreteRegularResourceEntity resource = new Gson().fromJson(synchronizeRequest.resourceEntity, ConcreteRegularResourceEntity.class);
            // Active identity
            if (ResourceType.AE == resource.getResourceType().intValue() ||
            ResourceType.REMOTE_CSE == resource.getResourceType().intValue() ||
            ResourceType.AE_ANNC == resource.getResourceType().intValue()) {
                identityService.updateActive(resource.getResourceID(), vn.vnpt.oneiot.base.constants.Constants.EntityStatus.ACTIVE);
            }
            else if (ResourceType.GROUP == resource.getResourceType().intValue()) {
                // TODO: Tao identity cho group trong bang IDENTITY cho group
                GroupEntity groupEntity = new Gson().fromJson(synchronizeRequest.resourceEntity, GroupEntity.class);
                IdentityEntity identityCreator;
                if (groupEntity.getParentID().equals(CSE_ID_URI)) {
                    identityCreator= identityRepository.findFirstById(groupEntity.getCreator());
                } else {
                    identityCreator = identityRepository.findFirstById(groupEntity.getParentID());
                }
                IdentityEntity identityEntity = new IdentityEntity();
                identityEntity.setId(groupEntity.getResourceID());
                identityEntity.setName(groupEntity.getGroupName());
                identityEntity.setMemberIds(groupEntity.getMemberIDs());
                identityEntity.setType(GROUP_IDENTITY_TYPE);
                if (identityCreator !=null){
                    identityEntity.setMainAppId(identityCreator.getMainAppId());
                    identityEntity.setTenantId(identityCreator.getTenantId());
                }
                identityService.create(identityEntity);
            }

            if (ResourceType.CSE_BASE == resource.getResourceType().intValue()) {
                logger.info("CSE base have already created");
            }
            else if (CSEInitialize.isCertificateRelease() && ResourceUtils.supportAccessControlPolicyIds(resource.getResourceType().intValue())) {
                MappingAcpIdsResourceEntity mappingAcpIdsResourceEntity = mappingAcpIdsResourceRepository.findFirstByResourceIdIs(resource.getResourceID());
                if (mappingAcpIdsResourceEntity == null) {
                    mappingAcpIdsResourceEntity = new MappingAcpIdsResourceEntity();
                    mappingAcpIdsResourceEntity.setResourceId(resource.getResourceID());
                    mappingAcpIdsResourceEntity.setHuri(resource.getHierarchicalURI());
                }
                if (resource.getAccessControlPolicyIds() != null) {
                    mappingAcpIdsResourceEntity.setAccessControlPolicyIds(resource.getAccessControlPolicyIds());
                }
                if (ResourceType.AE == resource.getResourceType().intValue() ||
                    ResourceType.REMOTE_CSE == resource.getResourceType().intValue() ||
                    ResourceType.AE_ANNC == resource.getResourceType().intValue()) {
                    // TODO: tao access control policy theo relation entity
                    // Tao acp cho cac AE, RemoteCSE, AEAnnc da duoc cap token (duoc khai bao bang api)
                    if (tokenRepository.findFirstByHolder(resource.getResourceID()) != null) {
                        AccessControlPolicyEntity acpEntity = accessControlPolicyService.findByTargetName(resource.getResourceID());
                        if (acpEntity == null) {
                            acpEntity = new AccessControlPolicyEntity();
                            acpEntity.setResourceID(vn.vnpt.oneiot.common.utils.ResourceUtils.generateResourceId(ResourceType.ACCESS_CONTROL_POLICY));
                            acpEntity.setResourceType(ResourceType.ACCESS_CONTROL_POLICY);
                            acpEntity.setName(ShortName.ACP +
                                    vn.vnpt.oneiot.common.constants.Constants.PREFIX_SEPERATOR +
                                    resource.getResourceID());
                            acpEntity.setParentID(CSE_ID_URI);
                            acpEntity.setHierarchicalURI(Constants.CSE_NAME + "/" + acpEntity.getName());
                            acpEntity = accessControlPolicyService.create(acpEntity);
                        }
                        if (acpEntity != null) {
                            mappingAcpIdsResourceEntity.getAccessControlPolicyIds().add(acpEntity.getResourceID());
                        }
                    }

                    /** Add admin acp **/
                    if (CSEInitialize.acpAdminId == null) {
                        logger.info("Authorization module hasn't initialized");
                        event.statusCode = ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue();
                        return event;
                    }

                    mappingAcpIdsResourceEntity.getAccessControlPolicyIds().add(CSEInitialize.acpAdminId);
                }

                if (mappingAcpIdsResourceEntity.getAccessControlPolicyIds().size() > 0) {
                    mappingAcpIdsResourceRepository.save(mappingAcpIdsResourceEntity);
                } else {
                    MappingAcpIdsResourceEntity parentMappingAcpIdsResourceEntity = mappingAcpIdsResourceRepository.findFirstByResourceIdIs(resource.getParentID());
                    if (parentMappingAcpIdsResourceEntity != null && parentMappingAcpIdsResourceEntity.getAccessControlPolicyIds().size() > 0) {
                        mappingAcpIdsResourceEntity.setAccessControlPolicyIds(parentMappingAcpIdsResourceEntity.getAccessControlPolicyIds());
                        mappingAcpIdsResourceRepository.save(mappingAcpIdsResourceEntity);
                    }
                }
            }
        } else if (synchronizeRequest.operation == Operation.DELETE.intValue()) {
            ConcreteRegularResourceEntity resource = new Gson().fromJson(synchronizeRequest.resourceEntity, ConcreteRegularResourceEntity.class);
            if (CSEInitialize.isCertificateRelease()) {
                mappingAcpIdsResourceRepository.deleteAllByHuriIsOrHuriStartingWith(resource.getHierarchicalURI(), resource.getHierarchicalURI() + "/");
                accessControlPolicyService.deleteAllByHierarchicalURIStartsWith(resource.getHierarchicalURI() + "/");
            }
            if (resource.getResourceType().intValue() == ResourceType.GROUP) {
                // TODO: Delete trong bang IDENTITY cho group
                GroupEntity groupEntity = new Gson().fromJson(synchronizeRequest.resourceEntity, GroupEntity.class);
                identityService.delete(groupEntity.getResourceID());

            } else if (ResourceType.AE == resource.getResourceType().intValue() ||
                    ResourceType.REMOTE_CSE == resource.getResourceType().intValue() ||
                    ResourceType.AE_ANNC == resource.getResourceType().intValue()) {
                identityService.updateActive(resource.getResourceID(), vn.vnpt.oneiot.base.constants.Constants.EntityStatus.IN_ACTIVE);
            }
        } else if (synchronizeRequest.operation == Operation.UPDATE.intValue()) {
            RegularResource regularResource = new Gson().fromJson(synchronizeRequest.modifiedResource, RegularResource.class);
            if (ControllerUtil.isPermittedUpdated(regularResource, "accessControlPolicyIDs")) {
                if (ResourceUtils.supportAccessControlPolicyIds(synchronizeRequest.resourceType)) {
                    ConcreteRegularResourceEntity resource = new Gson().fromJson(synchronizeRequest.resourceEntity, ConcreteRegularResourceEntity.class);
                    MappingAcpIdsResourceEntity mappingAcpIdsResourceEntity = mappingAcpIdsResourceRepository.findFirstByResourceIdIs(resource.getResourceID());
                    if (mappingAcpIdsResourceEntity == null) {
                        mappingAcpIdsResourceEntity = new MappingAcpIdsResourceEntity();
                        mappingAcpIdsResourceEntity.setResourceId(resource.getResourceID());
                        mappingAcpIdsResourceEntity.setHuri(resource.getHierarchicalURI());
                    }
                    mappingAcpIdsResourceEntity.setAccessControlPolicyIds(regularResource.getAccessControlPolicyIDs());
                    mappingAcpIdsResourceRepository.save(mappingAcpIdsResourceEntity);
                }
            }
            if (regularResource.getResourceType().intValue() == ResourceType.GROUP) {
                // TODO: Update lai memberIs trong bang IDENTITY cho group
                GroupEntity groupEntity = new Gson().fromJson(synchronizeRequest.resourceEntity, GroupEntity.class);
                IdentityEntity identity = identityRepository.findFirstById(groupEntity.getResourceID());
                if (identity !=null){
                    identity.setMemberIds(groupEntity.getMemberIDs());
                    identityService.update(groupEntity.getResourceID(),identity);
                }
            }
        }

        // Send response to service which request synchronization
        event.type = EVENTTYPE_RESPONSE;
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    public void doSynchronizeResponse(Event event, MessageProperties messageProperties) {
        SynchronizeEntity synchronizeEntity = synchronizeRepository.findSynchronizeEntityByIdIs(event.id);
        if (synchronizeEntity != null) {
            String serviceName = messageProperties.getReplyTo();
            if (serviceName.contains(AUTHORIZATION_SERVICE)) {
                synchronizeEntity.updateFlag = synchronizeEntity.updateFlag ^ AUTHENTICATION_AUTHORIZATION_FLAG;
            } else if (serviceName.contains(REGISTRATION_GROUP_SERVICE)) {
                synchronizeEntity.updateFlag = synchronizeEntity.updateFlag ^ REGISTRATION_GROUP_FLAG;
            } else if (serviceName.contains(SUBSCRIPTION_NOTIFICATION_SERVICE)) {
                synchronizeEntity.updateFlag = synchronizeEntity.updateFlag ^ SUBSCRIPTION_NOTIFICATION_FLAG;
            } else if (serviceName.contains(DATA_MANAGEMENT_SERVICE)) {
                synchronizeEntity.updateFlag = synchronizeEntity.updateFlag ^ DATA_MANAGEMENT_FLAG;
            } else if (serviceName.contains(DEVICE_MANAGEMENT_SERVICE)) {
                synchronizeEntity.updateFlag = synchronizeEntity.updateFlag ^ DEVICE_MANAGEMENT_FLAG;
            } else if (serviceName.contains(HTTP_API_SERVICE)) {
                synchronizeEntity.updateFlag = synchronizeEntity.updateFlag ^ HTTP_API_FLAG;
            } else if (serviceName.contains(MQTT_ADAPTER_SERVICE)) {
                synchronizeEntity.updateFlag = synchronizeEntity.updateFlag ^ MQTT_ADAPTER_FLAG;
            } else if (serviceName.contains(CHARGING_SERVICE)) {
                synchronizeEntity.updateFlag = synchronizeEntity.updateFlag ^ CHARGING_ACCOUNTING_FLAG;
            } else if (serviceName.contains(APPLICATION_MANAGEMENT_SERVICE)) {
                synchronizeEntity.updateFlag = synchronizeEntity.updateFlag ^ APPLICATION_MANAGEMENT_FLAG;
            } else if (serviceName.contains(ORCHESTRATION_SERVICE)) {
                synchronizeEntity.updateFlag = synchronizeEntity.updateFlag ^ ORCHESTRATION_FLAG;
            }

            if ((synchronizeEntity.updateFlag ^ ALL_FLAG) == 0) {
                synchronizeRepository.delete(synchronizeEntity);
            } else {
                synchronizeRepository.save(synchronizeEntity);
            }
        }
    }
}
