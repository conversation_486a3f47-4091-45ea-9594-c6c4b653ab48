package vn.vnpt.oneiot.core.api.nbi.models;

/**
 * Created by huyvv
 * Date: 03/02/2020
 * Time: 4:26 PM
 * for all issues, contact me: <EMAIL>
 **/
public class ResetPasswordInfo {
    private String forgotPasswordToken;
    private String email;
    private String newPassword;
    private String confirmPassword;
    private Integer active;

    public String getForgotPasswordToken() {
        return forgotPasswordToken;
    }

    public void setForgotPasswordToken(String forgotPasswordToken) {
        this.forgotPasswordToken = forgotPasswordToken;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getNewPassword() {
        return newPassword;
    }

    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }

    public String getConfirmPassword() {
        return confirmPassword;
    }

    public void setConfirmPassword(String confirmPassword) {
        this.confirmPassword = confirmPassword;
    }

    public Integer getActive() {
        return active;
    }

    public void setActive(Integer active) {
        this.active = active;
    }
}
