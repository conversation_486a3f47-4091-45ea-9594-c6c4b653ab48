package vn.vnpt.oneiot.core.api.nbi.models.response;

import java.util.ArrayList;
import java.util.List;

public class GetListTopoResponse extends ResponseBase{
    private Long total;
    private List<SubDeviceResponse> subList;

    public GetListTopoResponse() {
        this.total = 0L;
        this.subList = new ArrayList<>();
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public List<SubDeviceResponse> getSubList() {
        return subList;
    }

    public void setSubList(List<SubDeviceResponse> subList) {
        this.subList = subList;
    }
}
