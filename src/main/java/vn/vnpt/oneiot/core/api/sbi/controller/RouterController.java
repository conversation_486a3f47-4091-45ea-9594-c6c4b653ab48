package vn.vnpt.oneiot.core.api.sbi.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import vn.vnpt.oneiot.common.constants.*;
import vn.vnpt.oneiot.common.exceptions.*;
import vn.vnpt.oneiot.common.resource.RequestPrimitive;
import vn.vnpt.oneiot.common.resource.ResponsePrimitive;

import static vn.vnpt.oneiot.common.utils.Patterns.*;

@Controller
public class RouterController {
    private static Logger logger = LoggerFactory.getLogger(RouterController.class);

    private SbiAccessControlPolicyController sbiAccessControlPolicyController;
    private M2MServiceSubscriptionProfileController m2MServiceSubscriptionProfileController;
    private ServiceSubscribedNodeController serviceSubscribedNodeController;
    private ServiceSubscribedAppRuleController serviceSubscribedAppRuleController;

    @Autowired
    public void setSbiAccessControlPolicyController(SbiAccessControlPolicyController sbiAccessControlPolicyController) {
        this.sbiAccessControlPolicyController = sbiAccessControlPolicyController;
    }

    @Autowired
    public void setM2MServiceSubscriptionProfileController(M2MServiceSubscriptionProfileController m2MServiceSubscriptionProfileController) {
        this.m2MServiceSubscriptionProfileController = m2MServiceSubscriptionProfileController;
    }

    @Autowired
    public void setServiceSubscribedNodeController(ServiceSubscribedNodeController serviceSubscribedNodeController) {
        this.serviceSubscribedNodeController = serviceSubscribedNodeController;
    }

    @Autowired
    public void setServiceSubscribedAppRuleController(ServiceSubscribedAppRuleController serviceSubscribedAppRuleController) {
        this.serviceSubscribedAppRuleController = serviceSubscribedAppRuleController;
    }

    public ResponsePrimitive doRequest(RequestPrimitive requestPrimitive) {
        logger.info("Received request in Router: " + requestPrimitive.toString());
        ResponsePrimitive responsePrimitive = new ResponsePrimitive(requestPrimitive);
        try {

            ResourceRequestController controller = null;
            // Determine the appropriate resource controller
            // In case of a CREATE, the resource type determine the controller
            if (requestPrimitive.getOperation().equals(Operation.CREATE)) {
                controller = getResourceControllerFromRT(requestPrimitive.getResourceType().intValue());
            } else {
//                controller = getResourceControllerFromRT(requestPrimitive.getResourceType().intValue());
                controller = getResourceControllerFromUri(requestPrimitive.getTo());
            }

            if (controller != null) {
                logger.info("ResourceController to be used [" + controller.getClass().getSimpleName() + "]");
                // Perform the request in the specific controller
                responsePrimitive = controller.doRequest(requestPrimitive);
                if (requestPrimitive.getResultContent() != null && requestPrimitive.getResultContent().equals(ResultContent.NOTHING)) {
                    responsePrimitive.setContent(null);
                }
            } else {
                throw new BadRequestException("Malformed URI " + requestPrimitive.getTo());
            }
        } catch (PlatformException ex) {
            responsePrimitive.setResponseStatusCode(ex.getErrorStatusCode());
            responsePrimitive.setContent(ex.getMessage());
            responsePrimitive.setContentType(MimeMediaType.TEXT_PLAIN);
        } catch (Exception e) {
            logger.error("Router internal error", e);
            responsePrimitive.setResponseStatusCode(ResponseStatusCode.INTERNAL_SERVER_ERROR);
            responsePrimitive.setContentType(MimeMediaType.TEXT_PLAIN);
            responsePrimitive.setContent("Router internal error");

        }
        responsePrimitive.setEventId(requestPrimitive.getEventId());

        return responsePrimitive;
    }

    public ResourceRequestController getResourceControllerFromRT(int resourceType) {
        if (resourceType == ResourceType.ACCESS_CONTROL_POLICY) {
            return sbiAccessControlPolicyController;
        }
        if (resourceType == ResourceType.M2M_SERVICE_SUBSCRIPTION_PROFILE) {
            return m2MServiceSubscriptionProfileController;
        }
        if (resourceType == ResourceType.SERVICE_SUBSCRIBED_NODE) {
            return serviceSubscribedNodeController;
        }
        if (resourceType == ResourceType.SERVICE_SUBSCRIBED_APP_RULE) {
            return serviceSubscribedAppRuleController;
        }
        return null;
    }

    public ResourceRequestController getResourceControllerFromUri(String uri) {
        if (uri.startsWith(SP_RELATIVE_PREFIX)) uri = uri.substring(SP_RELATIVE_PREFIX.length());
        else if (uri.startsWith(ABS_PREFIX)) uri = uri.substring(ABS_PREFIX.length());
        if (matchResource(ACP_PATTERN, uri)) {
            return sbiAccessControlPolicyController;
        }
        if (matchResource(M2M_SERVICE_SUBSCRIPTION_PROFILE_PATTERN, uri)) {
            return m2MServiceSubscriptionProfileController;
        }
        if (matchResource(SERVICE_SUBSCRIBED_NODE_PATTERN, uri)) {
            return serviceSubscribedNodeController;
        }
        if (matchResource(SERVICE_SUBSCRIBED_APP_RULE_PATTERN, uri)) {
            return serviceSubscribedAppRuleController;
        }
        return null;
    }

}
