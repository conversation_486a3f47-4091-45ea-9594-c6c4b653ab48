package vn.vnpt.oneiot.core.api.nbi.models.response;

import vn.vnpt.oneiot.core.api.nbi.models.Device;

import java.util.List;

public class BulkRegisterResponse extends ResponseBase{
    private int totalRecord;
    private List<Device> invalidDevices;

    public int getTotalRecord() {
        return totalRecord;
    }

    public void setTotalRecord(int totalRecord) {
        this.totalRecord = totalRecord;
    }

    public List<Device> getInvalidDevices() {
        return invalidDevices;
    }

    public void setInvalidDevices(List<Device> invalidDevices) {
        this.invalidDevices = invalidDevices;
    }
}
