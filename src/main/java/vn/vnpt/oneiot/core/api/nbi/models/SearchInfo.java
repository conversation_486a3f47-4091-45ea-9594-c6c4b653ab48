package vn.vnpt.oneiot.core.api.nbi.models;

import org.springframework.data.domain.Pageable;

/**
 * Created by huyvv
 * Date: 17/01/2020
 * Time: 3:45 PM
 * for all issues, contact me: <EMAIL>
 **/
public class SearchInfo {
    private String query;
    private String resultField;
    private int pageNumber;
    private int pageSize;
    private String orders;

    public SearchInfo(){

    }

    public String getResultField() {
        return resultField;
    }

    public void setResultField(String resultField) {
        this.resultField = resultField;
    }

    public SearchInfo(String query, Pageable pageable){
        this.query = query;
        this.pageNumber = pageable.getPageNumber();
        this.pageSize = pageable.getPageSize();
        if(pageable != null && pageable.getSort() != null) {
            this.orders = pageable.getSort().toString();
        }else this.orders = "";
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public int getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getOrders() {
        return orders;
    }

    public void setOrders(String orders) {
        this.orders = orders;
    }
}
