package vn.vnpt.oneiot.core.api.nbi.controller;

import com.google.common.base.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.event.EventBus;
import vn.vnpt.oneiot.core.generic.Constants;
import vn.vnpt.oneiot.core.mongo.services.TokenService;

/**
 * Author: kiennh
 * Date: 11/20/2020
 * Contact: <EMAIL>
 */
@Component
public class TokenController {
    @SuppressWarnings("unused")
    private static Logger logger = LoggerFactory.getLogger(TokenController.class);

    private TokenService tokenService;
    private EventBus eventBus;

    @Autowired
    public TokenController(TokenService service, EventBus eventBus) {
        this.tokenService = service;
        this.eventBus = eventBus;
    }

    public void process(Event event) {
        logger.info("TokenController receive method: #{}", event.method);
        if (!Strings.isNullOrEmpty(event.method)) {
            switch (event.method) {
                case Constants.Method.REFRESH_TOKEN_DEVICE:
                    tokenService.refreshToken(event);

            }
        }
    }
}
