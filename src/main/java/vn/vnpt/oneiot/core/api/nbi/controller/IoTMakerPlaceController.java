package vn.vnpt.oneiot.core.api.nbi.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.core.mongo.entity.Application;
import vn.vnpt.oneiot.core.mongo.services.ApplicationService;


@Component
public class IoTMakerPlaceController extends MgCrudAdminController<Application, String>{

    private static Logger logger = LoggerFactory.getLogger(IoTMakerPlaceController.class);

    @Autowired
    public IoTMakerPlaceController(ApplicationService service) {
        super(service);
    }

    public Event process(Event event) {
        logger.info("IoTMakerPlaceController receive method: #{}", event.method);
        return super.process(event);
    }
}
