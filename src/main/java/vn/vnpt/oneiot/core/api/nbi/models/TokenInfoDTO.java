package vn.vnpt.oneiot.core.api.nbi.models;

import io.swagger.models.auth.In;

/**
 * Author: kiendt
 * Date: 4/16/2020
 * Contact: <EMAIL>
 */
public class TokenInfoDTO {

    public String userId;
    public String username;
    public String tenantId;
    public Integer orgType;
    public Integer orgSubType; // Loai user (UserType), Loai app (IDENTITY_SUB_TYPE), Loai device (IDENTITY_SUB_TYPE)
    public Integer targetType;
    public Integer targetSubType;
    public String targetId;
    public String targetName;
    public String holderName;
    public String holderId;

    public Long expireTime;
    public Long notBefore;
    public String issuer;
    public String subject;
    public String audience;
    public String mainAppId;
    public String appDomainId;
    public String appDomainName;
    public String tenantName;
    public String category;

    public TokenInfoDTO(String userId, String tenantId, Integer orgType, String holderName, String holderId) {
        this.userId = userId;
        this.tenantId = tenantId;
        this.orgType = orgType;
        this.holderName = holderName;
        this.holderId = holderId;
    }

    public TokenInfoDTO() {
    }

    @Override
    public String toString() {
        return "TokenInfo{" +
                "userId=" + userId +
                "username=" + username +
                ", tenantId='" + tenantId + '\'' +
                ", type='" + orgType + '\'' +
                ", subType='" + orgSubType + '\'' +
                ", holderName='" + holderName + '\'' +
                ", holderId='" + holderId + '\'' +
                '}';
    }
}
