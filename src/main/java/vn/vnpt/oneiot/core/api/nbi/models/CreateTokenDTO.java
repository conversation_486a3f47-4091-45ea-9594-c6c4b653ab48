package vn.vnpt.oneiot.core.api.nbi.models;

/**
 * Author: kiendt
 * Date: 4/20/2020
 * Contact: <EMAIL>
 */
public class CreateTokenDTO {
    public Integer type;
    public String accessToken;
    public String refreshToken;
    public Long expiry;
    public String holderId;
    public String urlRedirect;

    public CreateTokenDTO() {
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public Long getExpiry() {
        return expiry;
    }

    public void setExpiry(Long expiry) {
        this.expiry = expiry;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getHolderId() {
        return holderId;
    }

    public void setHolderId(String holderId) {
        this.holderId = holderId;
    }

    public String getUrlRedirect() {
        return urlRedirect;
    }

    public void setUrlRedirect(String urlRedirect) {
        this.urlRedirect = urlRedirect;
    }

    @Override
    public String toString() {
        return "CreateTokenDTO{" +
                "type=" + type +
                ", accessToken='" + accessToken + '\'' +
                ", refreshToken='" + refreshToken + '\'' +
                ", expiry='" + expiry + '\'' +
                ", holderId='" + holderId + '\'' +
                '}';
    }
}
