package vn.vnpt.oneiot.core.api.nbi.models.response;

import java.util.ArrayList;
import java.util.List;

public class AddTopoResponse extends ResponseBase{
    private Long total;
    private List<SubDevice> subList;

    public AddTopoResponse() {
        this.total = 0L;
        this.subList = new ArrayList<>();
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public List<SubDevice> getSubList() {
        return subList;
    }

    public void setSubList(List<SubDevice> subList) {
        this.subList = subList;
    }
    public static class SubDevice {
        private String deviceName;
        private String deviceId;

        public String getDeviceName() {
            return deviceName;
        }

        public void setDeviceName(String deviceName) {
            this.deviceName = deviceName;
        }

        public String getDeviceId() {
            return deviceId;
        }

        public void setDeviceId(String deviceId) {
            this.deviceId = deviceId;
        }
    }
}

