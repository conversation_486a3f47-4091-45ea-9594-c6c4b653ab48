package vn.vnpt.oneiot.core.api.nbi.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import vn.vnpt.oneiot.base.constants.Constants;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.core.api.nbi.models.PageInfo;
import vn.vnpt.oneiot.core.api.nbi.models.SearchInfo;
import vn.vnpt.oneiot.core.api.nbi.models.StringIdInfo;
import vn.vnpt.oneiot.core.mongo.generic.MgNoneCrudService;

import java.util.ArrayList;
import java.util.List;

public class MgNoneCrudController <T, ID> {
    private static Logger logger = LoggerFactory.getLogger(MgNoneCrudController.class);

    protected MgNoneCrudService<T, ID> service;

    public MgNoneCrudController(MgNoneCrudService mgNoneCrudService) {
        service = mgNoneCrudService;
    }

    public Event process(Event event){
        switch (event.method){
            case Constants.Method.CREATE:
                return processCreate(event);
            case Constants.Method.SEARCH:
                return processSearch(event);
            case Constants.Method.DELETE:
                return processDelete(event);
            case Constants.Method.BATCH_DELETE:
                return processBatchDelete(event);
            case Constants.Method.GET_ONE:
                return processGetOne(event);
            case Constants.Method.COUNT:
                return processCount(event);
            default:
                event.statusCode = ResponseStatusCode.BAD_REQUEST.intValue();
                return event;
        }
    }

    /**
     * functions event processCreate, processUpdate...
     **/
    protected Event processCreate(Event event){
        T entity = ObjectMapperUtil.objectMapper(event.payload, service.getTypeEntityClass());
        event.payload = ObjectMapperUtil.toJsonString(service.create(entity));
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processGetOne(Event event){
        StringIdInfo idInfo = ObjectMapperUtil.objectMapper(event.payload, StringIdInfo.class);
        event.payload = ObjectMapperUtil.toJsonString(service.get((ID) idInfo.getId()));
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processSearch(Event event){
        SearchInfo searchInfo = ObjectMapperUtil.objectMapper(event.payload, SearchInfo.class);
        String orders = searchInfo.getOrders();
        Pageable pageable;
        if(orders == null || "".equals(orders)){
            pageable = PageRequest.of(searchInfo.getPageNumber(), searchInfo.getPageSize());
        }else {
            pageable = PageRequest.of
                    (searchInfo.getPageNumber(), searchInfo.getPageSize(), vn.vnpt.oneiot.base.utils.StringUtils.toSort(orders));
        }

        Page<T> page = service.search(searchInfo.getQuery(), pageable);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(page.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(page.getContent()));

        event.payload = ObjectMapperUtil.toJsonString(pageInfo);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processCount(Event event){
        event.payload = String.valueOf(service.count(event.payload));
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    public String addMultipleTenantQuery(String query){
        //do something to custom search query
        return query;
    }

    protected Event processDelete(Event event){
        StringIdInfo idInfo = ObjectMapperUtil.objectMapper(event.payload, StringIdInfo.class);
        service.softDelete((ID) idInfo.getId());
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processBatchDelete(Event event) {
        List<String> ids = ObjectMapperUtil.listMapper(event.payload, String.class);
        List<String> fail = new ArrayList<>();
        for(String id : ids){
            try{
                service.softDelete((ID) id);
            }catch (Exception ex){
                logger.error(ex.getMessage(), ex);
                fail.add(id);
            }
        }
        event.payload = ObjectMapperUtil.toJsonString(fail);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

}
