package vn.vnpt.oneiot.core.api.nbi.endpoints;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.vnpt.oneiot.base.constants.Constants;
import vn.vnpt.oneiot.base.event.AMQPSubscribes;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.common.entities.AccessControlRuleEntity;
import vn.vnpt.oneiot.common.entities.TokenEntity;
import vn.vnpt.oneiot.common.exceptions.PlatformException;
import vn.vnpt.oneiot.core.api.internal.endpoints.BaseEndpoint;
import vn.vnpt.oneiot.core.api.nbi.controller.*;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.event.EventBus;
import vn.vnpt.oneiot.core.mongo.services.*;

import static vn.vnpt.oneiot.base.constants.AMQPConstant.*;
import static vn.vnpt.oneiot.core.generic.Constants.*;

@Component
public class AuthorizationAPIEndpoint extends BaseEndpoint {
    @SuppressWarnings("unused")
    private static Logger logger = LoggerFactory.getLogger(AuthorizationAPIEndpoint.class);
    CountryController countryController;
    PrivilegeController privilegeController;
    RelationController relationController;
    RoleController roleController;
    TenantController tenantController;
    UserController userController;
    EventBus eventBus;
    TokenController tokenController;
    ApplicationController applicationController;
    ApplicationDomainController applicationDomainController;
    DeviceController deviceController;
    GroupController groupController;
    IoTMakerPlaceController ioTMakerPlaceController;

    @Autowired
    public void setApplicationController(ApplicationController applicationController) {
        this.applicationController = applicationController;
    }

    @Autowired
    public void setApplicationDomainController(ApplicationDomainController applicationDomainController) {
        this.applicationDomainController = applicationDomainController;
    }

    @Autowired
    public void setDeviceController(DeviceController deviceController) {
        this.deviceController = deviceController;
    }

    @Autowired
    public void setGroupController(GroupController groupController) {
        this.groupController = groupController;
    }

    @Autowired
    public AuthorizationAPIEndpoint(CountryService countryService,ApplicationService applicationService,
                                    PrivilegeService privilegeService, RelationService relationService, DynamicRoleService roleService,
                                    TenantService tenantService, UserService userService, TokenController tokenController, EventBus eventBus) {
        countryController = new CountryController(countryService);
        privilegeController = new PrivilegeController(privilegeService);
        relationController = new RelationController(relationService);
        roleController = new RoleController(roleService);
        tenantController = new TenantController(tenantService);
        userController = new UserController(userService);
        ioTMakerPlaceController = new IoTMakerPlaceController(applicationService);
        this.eventBus = eventBus;
        this.tokenController = tokenController;
    }

    @AMQPSubscribes(queue = QUEUE_API_AUTHORIZATION, exchange = AMQP_EXCHANGE_API, routingKey = ROUTING_KEY_API_AUTHORIZATION)
    public void processAPI(Event event, MessageProperties messageProperties) {
        try {
            super.beforeProcess(event);
            logger.debug("receive event: #{}", event.toString());
            String routingTable = messageProperties.getReceivedRoutingKey().substring(ROUTING_KEY_API_AUTHORIZATION.length() - 1);
            logger.debug("routingTable:" + routingTable);
            switch (routingTable) {
                case ROUTING_KEY_COUNTRY:
                    event = countryController.process(event);
                    break;
                case ROUTING_KEY_PRIVILEGE:
                    event = privilegeController.process(event);
                    break;
                case ROUTING_KEY_RELATION:
                    event = relationController.process(event);
                    break;
                case ROUTING_KEY_ROLE:
                    event = roleController.process(event);
                    break;
                case ROUTING_KEY_TENANT:
                    event = tenantController.process(event);
                    break;
                case ROUTING_KEY_USER:
                    event = userController.process(event);
                    break;
                case ROUTING_KEY_AUTH:
                    tokenController.process(event);
                    return;
                case ROUTING_KEY_APPLICATON_DOMAIN:
                    applicationDomainController.process(event);
                    break;
                case ROUTING_KEY_APPLICATION:
                    applicationController.process(event);
                    break;
                case ROUTING_KEY_DEVICE:
                    deviceController.process(event);
                    break;
                case ROUTING_KEY_GROUP:
                    groupController.process(event);
                    break;
                case ROUTING_KEY_MARKET_PLACE:
                    ioTMakerPlaceController.process(event);
                    break;
                default:
                    break;
            }
        } catch (PlatformException e) {
            event.statusCode = e.getErrorStatusCode().intValue();
            event.errorText = e.getMessage();
            event.payload = null;
            e.printStackTrace();
        } catch (Exception ex) {
            event.statusCode = ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue();
            event.errorText = ex.getMessage();
            event.payload = null;
            ex.printStackTrace();
        }
        sendResponse(event, messageProperties, ROUTING_KEY_API_AUTHORIZATION);
    }
}
