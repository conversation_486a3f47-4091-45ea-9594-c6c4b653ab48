package vn.vnpt.oneiot.core.api.nbi.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.event.EventBus;
import vn.vnpt.oneiot.core.mongo.entity.MgAbstractEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCrudAdminService;

public abstract class MgCrudAdminController<T extends MgAbstractEntity,ID > {
    private static Logger logger = LoggerFactory.getLogger(CrudController.class);

    protected MgCrudAdminService<T,ID> service;

    public MgCrudAdminController(MgCrudAdminService service) {
        this.service = service;
    }

    public Event process(Event event){
        return service.process(event);
    }
}
