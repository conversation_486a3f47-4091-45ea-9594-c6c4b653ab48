/*******************************************************************************
 * Copyright (c) 2013-2016 LAAS-CNRS (www.laas.fr)
 * 7 Colonel <PERSON> 31077 Toulouse - France
 *
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Initial Contributors:
 *     <PERSON><PERSON><PERSON> : Project manager, technical co-manager
 *     <PERSON><PERSON> : Technical co-manager
 *     <PERSON><PERSON> : Technical co-manager
 *     <PERSON><PERSON><PERSON> : Strategy expert
 *     <PERSON> : Developer
 *     <PERSON> : Developer
 *
 * New contributors :
 *******************************************************************************/
package vn.vnpt.oneiot.core.api.sbi.controller;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import vn.vnpt.oneiot.base.utils.DateUtils;
import vn.vnpt.oneiot.common.constants.MimeMediaType;
import vn.vnpt.oneiot.common.constants.ResourceType;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.common.entities.AccessControlPolicyEntity;
import vn.vnpt.oneiot.common.entities.AccessControlRuleEntity;
import vn.vnpt.oneiot.common.entities.TransactionEntity;
import vn.vnpt.oneiot.common.entitymapper.EntityMapperFactory;
import vn.vnpt.oneiot.common.exceptions.BadRequestException;
import vn.vnpt.oneiot.common.exceptions.ConflictException;
import vn.vnpt.oneiot.common.exceptions.PlatformException;
import vn.vnpt.oneiot.common.exceptions.ResourceNotFoundException;
import vn.vnpt.oneiot.common.resource.AccessControlPolicy;
import vn.vnpt.oneiot.common.resource.RequestPrimitive;
import vn.vnpt.oneiot.common.resource.ResponsePrimitive;
import vn.vnpt.oneiot.common.utils.*;
import vn.vnpt.oneiot.core.mongo.services.AccessControlPolicyService;

import java.util.List;
import static vn.vnpt.oneiot.common.utils.ControllerUtil.*;

/**
 * Controller for Access Control policy 
 *
 */

@Controller
@Transactional
public class SbiAccessControlPolicyController extends ResourceRequestController {

	protected static final Logger logger = LoggerFactory.getLogger(SbiAccessControlPolicyController.class);

	private AccessControlPolicyService accessControlPolicyService;

	@Autowired
	public void setAccessControlPolicyService(AccessControlPolicyService accessControlPolicyService) {
		this.accessControlPolicyService = accessControlPolicyService;
	}

	/*
	 * Generic create procedure 
	 * 
	 * 						Req
	 * @resourceName 		NP 
	 * resourceType 		NP 
	 * resourceID 			NP 
	 * parentID 			NP 
	 * expirationTime 		O 
	 * labels 				O 
	 * creationTime 		NP 
	 * lastModifiedTime 	NP 
	 * announceTo 			O
	 * announcedAttribute 	O 
	 * privileges 			M 
	 * selfPrivileges 		M
	 */

	@Override
	public ResponsePrimitive doCreate(RequestPrimitive request) {
		ResponsePrimitive response = new ResponsePrimitive(request);
		
		// Check if content is present
		if (request.getContent() == null){
			throw new BadRequestException("A content is requiered for AccessControlPolicy creation");
		}

		// Get the java object from the representation
		AccessControlPolicy acp = null;
		try{
			acp = new Gson().fromJson(request.getContent(), AccessControlPolicy.class);
		} catch (JsonSyntaxException e){
			logger.debug("ClassCastException: Incorrect resource type in object conversion.",e);
			throw new BadRequestException("Incorrect resource representation in content", e);
		}
		if (acp == null){
			throw new BadRequestException("Error in provided content");
		}

		AccessControlPolicyEntity acpEntity = new AccessControlPolicyEntity();
		// Check attributes
		// @resourceName 		NP 
		// Resource Type 		NP
		// resourceID 			NP
		// parentID 			NP
		// lastModifiedTime 	NP
		// creationTime 		NP
		// labels				O
		ControllerUtil.CreateUtil.fillEntityFromAnnounceableSubordinateResource(acp, acpEntity);


		/** MANDATORY attribute in CREATE request */
		// privileges 			M, empty set is allow
		if (!ControllerUtil.isPermittedCreatedWithEmptyValue(acp, "privileges", acp.getPrivileges())){
			throw new BadRequestException("Prilileges is Mandatory");
		}
		// selfPrivileges 		M
		if (!ControllerUtil.isPermittedCreated(acp, "selfPrivileges", acp.getSelfPrivileges())){
			throw new BadRequestException("SelfPrivileges is Mandatory");
		}

        acpEntity.setPrivileges(AcpUtils.getACREntityFromSetOfArcs(acp.getPrivileges()));
        acpEntity.setSelfPrivileges(AcpUtils.getACREntityFromSetOfArcs(acp.getSelfPrivileges()));

        if (acpEntity.getSelfPrivileges() == null || acpEntity.getSelfPrivileges().isEmpty()) {
            throw new BadRequestException("selfPrivileges must contain at least one rule");
        }
		
		String generatedId = ResourceUtils.generateResourceId(ResourceType.ACCESS_CONTROL_POLICY);
		// Creating the corresponding entity
		if (isPermittedCreated(acp, "name", acp.getName())){
			if (!Patterns.checkResourceName(acp.getName())){
				throw new BadRequestException("Name provided is incorrect. Must be:" + Patterns.ID_STRING);
			}
			acpEntity.setName(acp.getName());
			if (accessControlPolicyService.findByNameAndParentId(acp.getName(), request.getTo()) != null) {
				throw new ConflictException("The same resource name is already existed");
			}
		} else {
			acpEntity.setName(generatedId);
		}
		acpEntity.setHierarchicalURI(request.getHuriTarget() + "/" + acpEntity.getName());
		acpEntity.setResourceID(generatedId);
		acpEntity.setParentID(request.getTo());
		acpEntity.setResourceType(ResourceType.ACCESS_CONTROL_POLICY);

		// Create ACP in database
		acpEntity = accessControlPolicyService.cascadeCreate(acpEntity);
		
		// Create the response 
		response.setResponseStatusCode(ResponseStatusCode.CREATED);
		response.setResourceType(ResourceType.ACCESS_CONTROL_POLICY);
		response.setResourceEntity(new Gson().toJson(acpEntity));
		response.setEventId(request.getEventId());
		// Set the location of the resource
		setLocationAndCreationContent(request, response, acpEntity);
		orchestrationService.saveTransaction(response, acpEntity, TransactionEntity.EVENT_CREATED);

		return response;
	}

	/*
	 * Generic retrieve procedure
	 */
	@Override
	public ResponsePrimitive doRetrieve(RequestPrimitive request) {
		ResponsePrimitive response = new ResponsePrimitive(request);

		// Check existence of the resource
		AccessControlPolicyEntity acpEntity = accessControlPolicyService.get(request.getTo());
		if (acpEntity == null) {
			throw new ResourceNotFoundException("Resource " + request.getTo() + " not found.");
		}

		// Create the object used to create the representation of the resource
		AccessControlPolicy acpResource = EntityMapperFactory.getAcpMapper().mapEntityToResource(acpEntity, request);
		response.setContent(new Gson().toJson(acpResource));
		response.setResourceType(ResourceType.ACCESS_CONTROL_POLICY);
		response.setResponseStatusCode(ResponseStatusCode.OK);
		return response;
	}

	/*
	 * Generic update procedure 
	 * 
	 * 						Req
	 * @resourceName 		NP 
	 * resourceType 		NP 
	 * resourceID 			NP 
	 * parentID 			NP 
	 * expirationTime 		O 
	 * labels 				O 
	 * creationTime 		NP 
	 * lastModifiedTime 	NP 
	 * announceTo 			O
	 * announcedAttribute 	O 
	 * privileges 			O 
	 * selfPrivileges 		O
	 */
	@Override
	public ResponsePrimitive doUpdate(RequestPrimitive request) {
		ResponsePrimitive response = new ResponsePrimitive(request);
		
		// Retrieve the resource from DB
		AccessControlPolicyEntity acpEntity = accessControlPolicyService.get(request.getTo());

		// Check resource existence
		if (acpEntity == null){
			throw new ResourceNotFoundException("Resource " + request.getTo() + " not found.");
		}

		// Check if content is present
		if (request.getContent() == null){
			throw new BadRequestException("A content is requiered for ACP update");
		}

		// Create the java object from the resource representation
		AccessControlPolicy acp = null;
		try{
			acp = new Gson().fromJson(request.getContent(), AccessControlPolicy.class);
		} catch (ClassCastException e){
			logger.debug("ClassCastException: Incorrect resource type in object conversion.",e);
			throw new BadRequestException("Incorrect resource representation in content", e);
		}
		if (acp == null){
			throw new BadRequestException("Error in provided content");
		}
		
		AccessControlPolicy modifiedAttributes = new AccessControlPolicy();

		// NP attributes 
		// @resourceName 		NP 
		// resourceType 		NP 
		// resourceID 			NP 
		// parentID 			NP
		// creationTime 		NP 
		// lastModifiedTime 	NP 
		ControllerUtil.UpdateUtil.fillEntityFromAnnounceableSubordinateResource(acp, acpEntity, modifiedAttributes);
		// privileges 			O 
		if(ControllerUtil.isPermittedUpdateWithoutNull(acp, "privileges", acp.getPrivileges())){
			List<AccessControlRuleEntity> rules = AcpUtils.getACREntityFromSetOfArcs(acp.getPrivileges());
			acpEntity.getPrivileges().clear();
			acpEntity.getPrivileges().addAll(rules);
			modifiedAttributes.setPrivileges(acp.getPrivileges());
		} else if (acp.getPrivileges() == null && acp.checkFieldIsPresent("privileges")) {
			throw new PlatformException("Privileges is must present, not allowed to delete", ResponseStatusCode.BAD_REQUEST);
		}
		// selfPrivileges 		O
		if(ControllerUtil.isPermittedUpdateWithoutEmpty(acp, "selfPrivileges", acp.getSelfPrivileges())){
			List<AccessControlRuleEntity> rules = AcpUtils.getACREntityFromSetOfArcs(acp.getSelfPrivileges());
			acpEntity.getSelfPrivileges().clear();
			acpEntity.getSelfPrivileges().addAll(rules);
			modifiedAttributes.setSelfPrivileges(acp.getSelfPrivileges());
		} else if (ControllerUtil.isPermittedUpdated(acp, "selfPrivileges")) {
			throw new PlatformException("SelfPrivileges is must present, not allowed to delete or update to empty", ResponseStatusCode.BAD_REQUEST);
		}

		acpEntity = accessControlPolicyService.cascadeUpdate(acpEntity);
		modifiedAttributes.setLastModifiedTime(DateConverter.convertUTCLongtoUTCString(acpEntity.getLastModifiedTime()));
		response.setContent(new Gson().toJson(modifiedAttributes));
		response.setResourceEntity(new Gson().toJson(acpEntity));
		response.setResourceType(ResourceType.ACCESS_CONTROL_POLICY);
		response.setEventId(request.getEventId());
		response.setResponseStatusCode(ResponseStatusCode.UPDATED);
		orchestrationService.saveTransaction(response, acpEntity, TransactionEntity.EVENT_UPDATE);
		return response;
	}

	/*
	 * Generic Delete procedure
	 */
	@Override
	public ResponsePrimitive doDelete(RequestPrimitive request) {
		ResponsePrimitive response = new ResponsePrimitive(request);

		// Retrieve the resource from database
		AccessControlPolicyEntity acpEntity = accessControlPolicyService.get(request.getTo());

		// Check resource existence
		if (acpEntity == null){
			throw new ResourceNotFoundException("Resource not found");
		}
		// TODO: Update mapping acp id

		accessControlPolicyService.delete(request.getTo());
		response.setResponseStatusCode(ResponseStatusCode.DELETED);
		response.setResourceEntity(new Gson().toJson(acpEntity));
		response.setEventId(request.getEventId());
		orchestrationService.saveTransaction(response, acpEntity, TransactionEntity.EVENT_DELETE);
		return response;
	}

}
