package vn.vnpt.oneiot.core.api.nbi.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.vnpt.oneiot.core.jpa.entity.IdEntity;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.event.EventBus;
import vn.vnpt.oneiot.core.jpa.services.CrudService;

import java.io.Serializable;

/**
 * Created by huyvv
 * Date: 16/01/2020
 * Time: 11:00 AM
 * for all issues, contact me: <EMAIL>
 **/
public abstract class CrudController<T extends IdEntity, ID extends Serializable> {

    @SuppressWarnings("unused")
    private static Logger logger = LoggerFactory.getLogger(CrudController.class);

    protected CrudService<T,ID> service;
    protected EventBus eventBus;

    public CrudController(CrudService service, EventBus eventBus) {
        this.service = service;
        this.eventBus = eventBus;
    }

    public Event process(Event event){
        return service.process(event);
    }
}
