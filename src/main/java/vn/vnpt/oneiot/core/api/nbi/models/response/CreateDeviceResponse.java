package vn.vnpt.oneiot.core.api.nbi.models.response;

import java.util.List;

public class CreateDeviceResponse extends ResponseBase {
    private Long total;
    private List<DeviceCreateResponse> deviceList;

    public CreateDeviceResponse() {
    }

    public CreateDeviceResponse(Long total, List<DeviceCreateResponse> deviceList) {
        this.total = total;
        this.deviceList = deviceList;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public List<DeviceCreateResponse> getDeviceList() {
        return deviceList;
    }

    public void setDeviceList(List<DeviceCreateResponse> deviceList) {
        this.deviceList = deviceList;
    }
}
