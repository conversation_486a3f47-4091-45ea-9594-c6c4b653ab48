package vn.vnpt.oneiot.core.api.nbi.models.request;

public class GetAppListRequest {
    private Integer active;
    private Integer offset;
    private Integer limit;
    private Long fromDate;
    private Long toDate;
    private String mainAppId;
    private String holderId;

    public GetAppListRequest() {
    }

    public GetAppListRequest(Integer active, Integer offset, Integer limit, Long fromDate, Long toDate, String mainAppId, String holderId) {
        this.active = active;
        this.offset = offset;
        this.limit = limit;
        this.fromDate = fromDate;
        this.toDate = toDate;
        this.mainAppId = mainAppId;
        this.holderId = holderId;
    }

    public String getMainAppId() {
        return mainAppId;
    }

    public void setMainAppId(String mainAppId) {
        this.mainAppId = mainAppId;
    }

    public Integer getActive() {
        return active;
    }

    public void setActive(Integer active) {
        this.active = active;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Long getFromDate() {
        return fromDate;
    }

    public void setFromDate(Long fromDate) {
        this.fromDate = fromDate;
    }

    public Long getToDate() {
        return toDate;
    }

    public void setToDate(Long toDate) {
        this.toDate = toDate;
    }

    public String getHolderId() {
        return holderId;
    }

    public void setHolderId(String holderId) {
        this.holderId = holderId;
    }
}
