package vn.vnpt.oneiot.core.api.nbi.models;

import vn.vnpt.oneiot.core.generic.entity.AbstractEntity;

import java.util.List;

public class Group extends AbstractEntity {
    private String id;
    private String name;
    private String userId;
    private String nameAppDomain;
    private String description;
    private List<String> memberIds;
    private Integer errorCode;
    private String errorMsg;
    String tenantId;
    String mainAppId;

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getNameAppDomain() {
        return nameAppDomain;
    }

    public void setNameAppDomain(String nameAppDomain) {
        this.nameAppDomain = nameAppDomain;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getMemberIds() {
        return memberIds;
    }

    public void setMemberIds(List<String> memberIds) {
        this.memberIds = memberIds;
    }

    public String getMainAppId() {
        return mainAppId;
    }

    public void setMainAppId(String mainAppId) {
        this.mainAppId = mainAppId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}
