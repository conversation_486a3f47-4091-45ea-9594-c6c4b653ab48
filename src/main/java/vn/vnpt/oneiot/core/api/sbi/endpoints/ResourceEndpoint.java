package vn.vnpt.oneiot.core.api.sbi.endpoints;

import com.google.common.base.Strings;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.base.constants.AMQPConstant;
import vn.vnpt.oneiot.base.event.AMQPSubscribes;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.event.EventBus;
import vn.vnpt.oneiot.base.mongo.controller.TransactionService;
import vn.vnpt.oneiot.base.redis.ObjectCache;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.common.resource.RequestPrimitive;
import vn.vnpt.oneiot.common.resource.ResponsePrimitive;
import vn.vnpt.oneiot.core.api.internal.controller.SynchronizationController;
import vn.vnpt.oneiot.core.api.sbi.controller.RouterController;
import vn.vnpt.oneiot.core.generic.Constants;
import vn.vnpt.oneiot.core.generic.services.AuthService;
import vn.vnpt.oneiot.core.mongo.services.TokenService;

import static vn.vnpt.oneiot.base.constants.AMQPConstant.*;
import static vn.vnpt.oneiot.base.constants.AMQPConstant.getExchangeFromRoutingKey;

@Service
public class ResourceEndpoint {
    private Logger logger = LoggerFactory.getLogger(ResourceEndpoint.class);

    @Autowired
    ObjectCache systemCache;

    @Autowired
    EventBus eventBus;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private AuthService authService;

    @Autowired
    RouterController routerController;

//    @Autowired
//    OrchestrationService orchestrationService;

    @Autowired
    TransactionService transactionEntityRepository;

    @Autowired
    SynchronizationController synchronizationController;

    @AMQPSubscribes(queue = AMQPConstant.QUEUE_ONEM2M_AUTHORIZATION, exchange = AMQPConstant.AMQP_EXCHANGE_ONEM2M, routingKey = AMQPConstant.ROUTING_KEY_ONEM2M_PRIMITIVE_AUTHORIZATION)
    public void processPrimitive(Event event, MessageProperties properties) {
        logger.info(new Gson().toJson(event).toString());
        if (event.type == AMQPConstant.EVENTTYPE_REQUEST) {
            switch (event.method) {
                case Constants.Method.AUTHORIZE_BY_ONEM2M:
                    event = authService.processDynamicAuthorization(event);
                    sendResponse(event, properties, ROUTING_KEY_ONEM2M_PRIMITIVE_AUTHORIZATION);
                    break;
                case Constants.Method.CHECK_TOKEN:
                    event = tokenService.checkToken(event);
                    sendResponse(event, properties, ROUTING_KEY_ONEM2M_PRIMITIVE_AUTHORIZATION);
                    break;
                default:
                    Gson gson = new Gson();
                    RequestPrimitive requestPrimitive = gson.fromJson(event.payload, RequestPrimitive.class);
                    requestPrimitive.setEventId(event.id);
                    ResponsePrimitive responsePrimitive = routerController.doRequest(requestPrimitive);
                    event.payload = new Gson().toJson(responsePrimitive);
                    event.statusCode = ResponseStatusCode.OK.intValue();
                    event.type = AMQPConstant.EVENTTYPE_RESPONSE;
                    String routingKey = properties.getReplyTo();
                    properties.setReplyTo(AMQPConstant.ROUTING_KEY_ONEM2M_PRIMITIVE_AUTHORIZATION);
                    logger.info("Send response {} to " + routingKey, event);
                    eventBus.publish(AMQPConstant.getExchangeFromRoutingKey(routingKey), routingKey, event, properties);
//            orchestrationService.sendEvent(orchestrationService.createEventToSynchonized(responsePrimitive), ROUTING_KEY_ONEM2M_PRIMITIVE_ORCHESTRATION);
            }

        }
        if (event.type == AMQPConstant.EVENTTYPE_RESPONSE) {
            processResponse(event, properties);
        }
        if (event.type == AMQPConstant.EVENTTYPE_NOTIFY) {
            if (event.method.equals(AMQPConstant.ACK)) {
                logger.info("Commit transaction");
                ResponsePrimitive responsePrimitive = new Gson().fromJson(event.payload, ResponsePrimitive.class);
                if (!Strings.isNullOrEmpty(responsePrimitive.getTransactionId())) {
                    transactionEntityRepository.delete(responsePrimitive.getTransactionId());
                }
            }
        }

    }

    protected void sendResponse(Event event, MessageProperties messageProperties, String myReplyTo) {
        event.type = EVENTTYPE_RESPONSE;
        String replyTo = messageProperties.getReplyTo();
        messageProperties.setReplyTo(myReplyTo);
        if (org.apache.logging.log4j.util.Strings.isEmpty(replyTo)) {
            logger.error("Cannot not found reply to response message {}", event.toString());
        } else {
            eventBus.publish((String) messageProperties.getHeaders().getOrDefault(EventBus.RESP_EXCHANGE, getExchangeFromRoutingKey(replyTo)), replyTo, event, messageProperties);
        }
        logger.info("Send response to exchange #{} with routingkey #{}, #{}", getExchangeFromRoutingKey(replyTo), replyTo, event.toString());
    }

    private void processResponse(Event event, MessageProperties messageProperties) {
        if (org.apache.logging.log4j.util.Strings.isNotEmpty(event.method)) {
            switch (event.method) {
                case Constants.Method.PRIMITIVE:
                    Event ackEvent = new Event();
                    ackEvent.id = event.id;
                    ackEvent.type = EVENTTYPE_NOTIFY;
                    ackEvent.method = ACK;
                    eventBus.publish(getExchangeFromRoutingKey(messageProperties.getReplyTo()), messageProperties.getReplyTo(), ackEvent);
                    break;
                default:
            }
        }
    }
}
