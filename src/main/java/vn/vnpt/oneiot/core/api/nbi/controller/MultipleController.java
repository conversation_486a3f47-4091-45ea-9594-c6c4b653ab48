package vn.vnpt.oneiot.core.api.nbi.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.event.EventBus;
import vn.vnpt.oneiot.core.jpa.entity.MultipleIdEntity;
import vn.vnpt.oneiot.core.jpa.services.MultipleService;

import java.io.Serializable;

/**
 * Created by huyvv
 * Date: 10/02/2020
 * Time: 11:35 AM
 * for all issues, contact me: <EMAIL>
 **/
public abstract class MultipleController<T extends MultipleIdEntity, ID extends Serializable> {
    @SuppressWarnings("unused")
    private static Logger logger = LoggerFactory.getLogger(MultipleController.class);

    protected MultipleService<T, ID> service;
    protected EventBus eventBus;

    public MultipleController(MultipleService service, EventBus eventBus) {
        this.service = service;
        this.eventBus = eventBus;
    }

    public Event process(Event event) {
        return service.process(event);
    }
}
