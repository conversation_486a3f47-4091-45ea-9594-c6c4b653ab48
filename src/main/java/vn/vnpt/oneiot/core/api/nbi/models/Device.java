package vn.vnpt.oneiot.core.api.nbi.models;

public class Device {
    public Long created;
    public Long updated;
    public String createdBy;
    public String updatedBy;
    public Integer active;
    public String id;
    public String deviceName;
    public Integer deviceType;
    public String category;
    public String accessToken;
    public String refreshToken;
    public String tenantId;
    public String userId;
    public String description;
    public String appDomainName;
    public String appDomainId;
    public Long expiry;
    public String imsi;
    public String isdn;
    public String planName;
    public Long status;
    public String appId;
    public String ipeName;
    public String parentId;
    public String mainAppId;
    public String gatewayName;
    public Integer simStatus;
    public String note;
    public String deviceTypeStr;
}
