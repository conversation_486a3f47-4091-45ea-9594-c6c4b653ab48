package vn.vnpt.oneiot.core.api.nbi.models;

import java.util.Map;

/**
 * Created by huyvv
 * Date: 19/01/2020
 * Time: 9:03 PM
 * for all issues, contact me: <EMAIL>
 **/
public class LoginInfo {
    private String email;
    private String password;
    private Boolean rememberMe;
    private Map<String,Object> params;
    private String cookie;

    public LoginInfo() {
    }

    public LoginInfo(String email, String password, Boolean rememberMe) {
        this.email = email;
        this.password = password;
        this.rememberMe = rememberMe;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Boolean getRememberMe() {
        return rememberMe;
    }

    public void setRememberMe(Boolean rememberMe) {
        this.rememberMe = rememberMe;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    public String getCookie() {
        return cookie;
    }

    public void setCookie(String cookie) {
        this.cookie = cookie;
    }
}
