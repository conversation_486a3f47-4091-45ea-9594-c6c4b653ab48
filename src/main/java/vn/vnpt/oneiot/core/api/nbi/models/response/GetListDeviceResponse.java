package vn.vnpt.oneiot.core.api.nbi.models.response;

import java.util.List;

public class GetListDeviceResponse extends ResponseBase {
    private String appDomain;
    private String appDomainID;
    private Long total;
    private List<DeviceListResponse> deviceInfoList;

    public String getAppDomain() {
        return appDomain;
    }

    public void setAppDomain(String appDomain) {
        this.appDomain = appDomain;
    }

    public String getAppDomainID() {
        return appDomainID;
    }

    public void setAppDomainID(String appDomainID) {
        this.appDomainID = appDomainID;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public List<DeviceListResponse> getDeviceInfoList() {
        return deviceInfoList;
    }

    public void setDeviceInfoList(List<DeviceListResponse> deviceInfoList) {
        this.deviceInfoList = deviceInfoList;
    }
}
