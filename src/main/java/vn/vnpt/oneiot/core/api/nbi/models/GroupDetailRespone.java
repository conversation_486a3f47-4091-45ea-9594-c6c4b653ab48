package vn.vnpt.oneiot.core.api.nbi.models;

import java.util.List;

public class GroupDetailRespone {
    private String id;
    private String name;
    private String nameAppDomain;
    private List<Device> devices;
    private Integer total;
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameAppDomain() {
        return nameAppDomain;
    }

    public void setNameAppDomain(String nameAppDomain) {
        this.nameAppDomain = nameAppDomain;
    }

    public List<Device> getDevices() {
        return devices;
    }

    public void setDevices(List<Device> devices) {
        this.devices = devices;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }
}
