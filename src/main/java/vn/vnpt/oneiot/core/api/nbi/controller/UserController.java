package vn.vnpt.oneiot.core.api.nbi.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.core.mongo.entity.UserEntity;
import vn.vnpt.oneiot.core.mongo.services.UserService;

/**
 * Created by huyvv
 * Date: 16/01/2020
 * Time: 2:21 PM
 * for all issues, contact me: <EMAIL>
 **/
@Component
public class UserController extends MgCrudAdminController<UserEntity, String> {
    private static Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    public UserController(UserService service) {
        super(service);
    }

    @Override
    public Event process(Event event) {
        logger.info("UserController receive method: #{}", event.method);
        return super.process(event);
    }

//    public void processInternal(Event event){
//        logger.info("UserEndpoint receive method: #{}", event.method);
//        event = service.process(event);
//        eventBus.publish(Constants.TOPIC_EXCHANGE_INTERNAL, Constants.DYNAMIC_AUTHORIZATION_MODULE, event);
//    }
}
