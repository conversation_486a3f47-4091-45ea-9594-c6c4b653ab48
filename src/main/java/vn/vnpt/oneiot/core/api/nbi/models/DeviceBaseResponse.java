package vn.vnpt.oneiot.core.api.nbi.models;

/**
 * Created by HIEUDT on 12/16/2019.
 */
public class DeviceBaseResponse {
    private String deviceName;
    private String deviceId;
    private Integer deviceType;

    public DeviceBaseResponse() {
    }

    public DeviceBaseResponse(String deviceName, String deviceId, Integer deviceType) {
        this.deviceName = deviceName;
        this.deviceId = deviceId;
        this.deviceType = deviceType;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public Integer getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(Integer deviceType) {
        this.deviceType = deviceType;
    }
}
