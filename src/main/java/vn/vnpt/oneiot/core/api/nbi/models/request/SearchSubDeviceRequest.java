package vn.vnpt.oneiot.core.api.nbi.models.request;

public class SearchSubDeviceRequest {
    private int offset;
    private int limit;
    private String appDomainId;

    public SearchSubDeviceRequest() {
    }

    public SearchSubDeviceRequest(int offset, int limit) {
        this.offset = offset;
        this.limit = limit;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public String getAppDomainId() {
        return appDomainId;
    }

    public void setAppDomainId(String appDomainId) {
        this.appDomainId = appDomainId;
    }
}
