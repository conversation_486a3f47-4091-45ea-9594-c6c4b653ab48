package vn.vnpt.oneiot.core.api.nbi.models;

import vn.vnpt.oneiot.base.errors.BadRequestAlertException;

import java.net.URI;

/**
 * Created by huyvv
 * Date: 22/01/2020
 * Time: 12:10 PM
 * for all issues, contact me: <EMAIL>
 **/
public class ErrorInfo {
    private URI type;
    private String defaultMessage;
    private String entityName;
    private String errorKey;

    public ErrorInfo() {
    }

    public ErrorInfo(String errorKey) {
        this.errorKey = errorKey;
    }

    public ErrorInfo(URI type, String defaultMessage, String entityName, String errorKey) {
        this.type = type;
        this.defaultMessage = defaultMessage;
        this.entityName = entityName;
        this.errorKey = errorKey;
    }

    public BadRequestAlertException toException(){
        BadRequestAlertException exception =
                new BadRequestAlertException(this.type, this.defaultMessage, this.entityName, this.errorKey);
        return exception;
    }

    public URI getType() {
        return type;
    }

    public void setType(URI type) {
        this.type = type;
    }

    public String getDefaultMessage() {
        return defaultMessage;
    }

    public void setDefaultMessage(String defaultMessage) {
        this.defaultMessage = defaultMessage;
    }

    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    public String getErrorKey() {
        return errorKey;
    }

    public void setErrorKey(String errorKey) {
        this.errorKey = errorKey;
    }
}
