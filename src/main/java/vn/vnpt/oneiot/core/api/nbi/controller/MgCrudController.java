package vn.vnpt.oneiot.core.api.nbi.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.vnpt.oneiot.base.constants.AMQPConstant;
import vn.vnpt.oneiot.base.constants.Constants;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.common.entities.ResourceEntity;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.core.api.nbi.models.StringIdInfo;
import vn.vnpt.oneiot.core.mongo.generic.MgCrudService;

public class MgCrudController <T extends ResourceEntity, ID> extends MgNoneCrudController<T, ID>{

    private static Logger logger = LoggerFactory.getLogger(MgCrudController.class);

    protected MgCrudService<T, ID> service;

    public MgCrudController (MgCrudService mgCrudService){
        super(mgCrudService);
        this.service = mgCrudService;
    }

    @Override
    public Event process(Event event){
        event.type = AMQPConstant.EVENTTYPE_RESPONSE;
        switch (event.method){
            case Constants.Method.UPDATE:
                return processUpdate(event);
            case Constants.Method.ACTIVE:
                return processActive(event);
            case Constants.Method.DE_ACTIVE:
                return processDeActive(event);
            default:
                return super.process(event);
        }
    }

    /**
     * functions event processCreate, processUpdate...
     **/

    protected Event processUpdate(Event event){
        T entity = ObjectMapperUtil.objectMapper(event.payload, service.getTypeEntityClass());
        event.payload = ObjectMapperUtil.toJsonString(service.update((ID) entity.getResourceID(), entity));
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processActive(Event event){
        StringIdInfo idInfo = ObjectMapperUtil.objectMapper(event.payload, StringIdInfo.class);
        service.activate((ID) idInfo.getId());
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processDeActive(Event event){
        StringIdInfo idInfo = ObjectMapperUtil.objectMapper(event.payload, StringIdInfo.class);
        service.deactivate((ID) idInfo.getId());
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }
}
