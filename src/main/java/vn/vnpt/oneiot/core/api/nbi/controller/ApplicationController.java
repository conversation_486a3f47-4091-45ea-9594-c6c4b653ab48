package vn.vnpt.oneiot.core.api.nbi.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.core.mongo.controller.IdentityController;
import vn.vnpt.oneiot.core.mongo.services.IdentityService;

import static vn.vnpt.oneiot.core.generic.Constants.IDENTITY_TYPE.APP_IDENTITY_TYPE;

@Service
public class ApplicationController extends IdentityController {
    @Autowired
    public ApplicationController(IdentityService service) {
        super(service);
    }

    @Override
    public Event process(Event event){
        return ((IdentityService) service).process(event, APP_IDENTITY_TYPE);
    }
}
