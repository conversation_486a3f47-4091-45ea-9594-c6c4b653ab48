package vn.vnpt.oneiot.core.api.nbi.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.core.mongo.entity.CountryEntity;
import vn.vnpt.oneiot.core.mongo.services.CountryService;

/**
 * Created by huyvv
 * Date: 04/02/2020
 * Time: 3:40 PM
 * for all issues, contact me: <EMAIL>
 **/
@Component
public class CountryController extends MgCrudAdminController<CountryEntity, String> {
    @SuppressWarnings("unused")
    private static Logger logger = LoggerFactory.getLogger(CountryController.class);

    @Autowired
    public CountryController(CountryService service) {
        super(service);
    }

    public Event process(Event event) {
        logger.info("CountryController receive method: #{}", event.method);
        return super.process(event);
    }
}
