package vn.vnpt.oneiot.core.api.internal.endpoints;

import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.event.EventBus;
import vn.vnpt.oneiot.core.configuration.UserPrincipal;
import vn.vnpt.oneiot.core.mongo.entity.PrivilegeEntity;
import vn.vnpt.oneiot.core.mongo.entity.UserEntity;
import vn.vnpt.oneiot.core.mongo.services.DynamicRoleService;
import vn.vnpt.oneiot.core.mongo.services.PrivilegeService;
import vn.vnpt.oneiot.core.mongo.services.UserService;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static vn.vnpt.oneiot.base.constants.AMQPConstant.EVENTTYPE_RESPONSE;
import static vn.vnpt.oneiot.base.constants.AMQPConstant.getExchangeFromRoutingKey;
import static vn.vnpt.oneiot.core.generic.Constants.Method.LOGIN;

/**
 * Author: kiendt
 * Date: 4/22/2020
 * Contact: <EMAIL>
 */
public abstract class BaseEndpoint {

    protected final Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    UserService userService;

    @Autowired
    DynamicRoleService dynamicRoleService;
    @Autowired
    PrivilegeService privilegeService;

    @Autowired
    EventBus eventBus;

    protected void initSecurityInfo(String token) {
        UserEntity userEntity = userService.current(token);
        Set<GrantedAuthority> authorities = new HashSet<>();
        for (String scope : userEntity.getAuthorities()) {
            authorities.add(new SimpleGrantedAuthority(scope));
        }
        UserPrincipal principal = new UserPrincipal(userEntity.getEmail(), "", authorities, userEntity.getTenantId(), userEntity.getId());
        Authentication authentication = new UsernamePasswordAuthenticationToken(principal, token, authorities);
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }

    protected void beforeProcess(Event event) {
        if (event.token != null && !"undefined".equals(event.token) && !"".equals(event.token) && !LOGIN.equals(event.method)
                && !"anonymousUser".equals(event.token)
        ) {
            initSecurityInfo(event.token);
        }
    }

    protected void sendResponse(Event event, MessageProperties messageProperties, String myReplyTo) {
        event.type = EVENTTYPE_RESPONSE;
        String replyTo = messageProperties.getReplyTo();
        messageProperties.setReplyTo(myReplyTo);
        if (Strings.isEmpty(replyTo)) {
            logger.error("Cannot not found reply to response message {}", event.toString());
        } else {
            eventBus.publish((String) messageProperties.getHeaders().getOrDefault(EventBus.RESP_EXCHANGE, getExchangeFromRoutingKey(replyTo)), replyTo, event, messageProperties);
        }
        logger.info("Send response to exchange #{} with routingkey #{}, #{}", getExchangeFromRoutingKey(replyTo), replyTo, event.toString());
    }

}
