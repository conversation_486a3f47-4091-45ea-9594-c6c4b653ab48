package vn.vnpt.oneiot.core.api.nbi.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.common.entities.RoleEntity;
import vn.vnpt.oneiot.core.mongo.services.DynamicRoleService;

/**
 * Created by huyvv
 * Date: 20/01/2020
 * Time: 5:01 PM
 * for all issues, contact me: <EMAIL>
 **/
@Component
public class RoleController extends MgCrudController<RoleEntity, String> {
    private static Logger logger = LoggerFactory.getLogger(RoleController.class);

    @Autowired
    public RoleController(DynamicRoleService service) {
        super(service);
    }

    public Event process(Event event){
        logger.info("RoleController receive method: #{}", event.method);
        return service.process(event);
    }
}
