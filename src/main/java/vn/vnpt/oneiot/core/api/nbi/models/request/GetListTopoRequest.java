package vn.vnpt.oneiot.core.api.nbi.models.request;

public class GetListTopoRequest {
    private String gatewayId;
    private Integer offset;
    private Integer limit;
    private Long fromDate;
    private Long toDate;

    public GetListTopoRequest(String gatewayId,Integer offset, Integer limit, Long fromDate, Long toDate) {
        this.gatewayId = gatewayId;
        this.offset = offset;
        this.limit = limit;
        this.fromDate = fromDate;
        this.toDate = toDate;
    }

    public GetListTopoRequest() {
    }

    public String getGatewayId() {
        return gatewayId;
    }

    public void setGatewayId(String gatewayId) {
        this.gatewayId = gatewayId;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Long getFromDate() {
        return fromDate;
    }

    public void setFromDate(Long fromDate) {
        this.fromDate = fromDate;
    }

    public Long getToDate() {
        return toDate;
    }

    public void setToDate(Long toDate) {
        this.toDate = toDate;
    }
}
