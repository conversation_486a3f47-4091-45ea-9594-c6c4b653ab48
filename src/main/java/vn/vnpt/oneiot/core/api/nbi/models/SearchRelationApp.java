package vn.vnpt.oneiot.core.api.nbi.models;

import org.springframework.data.domain.Pageable;

public class SearchRelationApp {
    private String searchInfo;
    private String appId;
    private int type;
    private String mainAppId;

    public SearchRelationApp(String searchInfo, String appId, int type) {
        this.searchInfo = searchInfo;
        this.appId = appId;
        this.type = type;
    }

    public String getSearchInfo() {
        return searchInfo;
    }

    public void setSearchInfo(String searchInfo) {
        this.searchInfo = searchInfo;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getMainAppId() {
        return mainAppId;
    }

    public void setMainAppId(String mainAppId) {
        this.mainAppId = mainAppId;
    }
}
