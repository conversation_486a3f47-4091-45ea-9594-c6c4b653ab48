package vn.vnpt.oneiot.core.api.internal.endpoints;

import org.apache.logging.log4j.util.Strings;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.vnpt.oneiot.base.constants.Constants;
import vn.vnpt.oneiot.base.event.AMQPSubscribes;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.common.exceptions.PlatformException;
import vn.vnpt.oneiot.core.api.internal.controller.SynchronizationController;
import vn.vnpt.oneiot.core.api.internal.service.ConfigService;
import vn.vnpt.oneiot.core.api.nbi.models.ErrorInfo;
import vn.vnpt.oneiot.core.generic.services.AuthService;
import vn.vnpt.oneiot.core.mongo.services.TokenService;
import vn.vnpt.oneiot.core.utils.ErrorUtils;

import static vn.vnpt.oneiot.base.constants.AMQPConstant.*;
import static vn.vnpt.oneiot.core.generic.Constants.*;
import static vn.vnpt.oneiot.core.generic.Constants.Method.CONFIG_PLATFORM;
import static vn.vnpt.oneiot.core.generic.Constants.Method.GEN_AUTH_CODE;

/**
 * Author: kiendt
 * Date: 4/17/2020
 * Contact: <EMAIL>
 */

@Component
public class AuthorizationInternalEndpoint extends BaseEndpoint {


    @Autowired
    private TokenService tokenService;

    @Autowired
    private AuthService authService;

    @Autowired
    private ConfigService configService;

    @Autowired
    SynchronizationController synchronizationController;

    /**
     * process event from internal (other core module call core-auth-module)
     */
    @AMQPSubscribes(queue = QUEUE_INTERNAL_AUTHORIZATION, exchange = AMQP_EXCHANGE_INTERNAL, routingKey = ROUTING_KEY_INTERNAL_AUTHORIZATION)
    @SuppressWarnings("unused")
    public void processInternal(Event event, MessageProperties messageProperties) {
        logger.info("receive event: #{}", event.toString());
        if (event.type == EVENTTYPE_REQUEST) {
            processRequest(event, messageProperties);
        } else if (event.type == EVENTTYPE_RESPONSE) {
            processResponse(event, messageProperties);
        } else {
            processTypeNotSupport(event, messageProperties);
        }

    }

    private void processTypeNotSupport(Event event, MessageProperties messageProperties) {
        logger.error("Type not support: #{}", event.toString());
        ErrorUtils.handleErrorResponse(ResponseStatusCode.NOT_IMPLEMENTED.intValue(), event, "Event Type Not Support");
        sendResponse(event, messageProperties, ROUTING_KEY_INTERNAL_AUTHORIZATION);
    }

    private void processResponse(Event event, MessageProperties messageProperties) {
        if (Strings.isNotEmpty(event.method)) {
            switch (event.method) {
                case Method.REFRESH_TOKEN_DEVICE:
                    tokenService.refreshTokenResp(event);
                    break;
                case Method.PRIMITIVE:
                    Event ackEvent = new Event();
                    ackEvent.id = event.id;
                    ackEvent.type = EVENTTYPE_NOTIFY;
                    ackEvent.method = ACK;
                    eventBus.publish(getExchangeFromRoutingKey(messageProperties.getReplyTo()), messageProperties.getReplyTo(), ackEvent);
                    break;
                default:
            }
        }
//        event = configService.processConfigPlatform(event);
//        event.type = EVENTTYPE_NOTIFY;
//        event.method = "ACK";
//        eventBus.publish(getExchangeFromRoutingKey(ROUTING_KEY_ONEM2M_PRIMITIVE_ORCHESTRATION), ROUTING_KEY_ONEM2M_PRIMITIVE_ORCHESTRATION, event);
    }

    private void processRequest(Event event, MessageProperties messageProperties) {
        try {
            super.beforeProcess(event);
            if (Strings.isNotEmpty(event.method)) {
                switch (event.method) {
                    case Method.GET_TOKEN:
                        event = tokenService.getToken(event);
                        break;
                    case Method.REFRESH_TOKEN:
                        event.routingKeyReplyTo = messageProperties.getReplyTo();
                        event = tokenService.refreshToken(event);
                        if (event == null) {
                            return;
                        }
                        break;
                    case Method.SELF_REFRESH_TOKEN:
                        event=tokenService.selfRefreshToken(event);
                        if (event == null) {
                            return;
                        }
                        break;
                    case Method.VALIDATE_PERMISSION:
                        event = authService.validatePermission(event);
                        break;
                    case Method.AUTHORIZE_BY_PLATFORM:
                        event = authService.processAuthorByPlatform(event);
                        break;
                    case Method.LOGIN:
                        event = tokenService.createTokenForUser(event);
                        break;
                    case Method.LOGOUT:
                        event = tokenService.deleteTokenForUser(event);
                        break;
                    case Method.GET_TOKEN_INFO:
                        event = tokenService.getDetailTokenFromStrToken(event);
                        break;
                    case Method.VALIDATE_TOKEN:
                        event = tokenService.validateToken(event);
                        break;
                    case SYNCHRONIZE_ONEM2M:
                        event = synchronizationController.doSynchornizeRequest(event);
                        break;
                    case CONFIG_PLATFORM:
                        event = configService.processConfigPlatform(event);
                        break;
                    case GEN_AUTH_CODE:
                        event = tokenService.genAuthCode(event);
                        break;
                    case Method.AUTH2_LOGIN:
                        event = tokenService.createTokenAuth2(event);
                        break;
                    case Method.AUTH2_VALIDATE_TOKEN:
                        event = tokenService.validateTokenAuth2(event);
                        break;
                    default:
                        event = handleNoSupportMethod(event);
                        break;
                }
            }
        } catch (PlatformException e) {
            event.statusCode = e.getErrorStatusCode().intValue();
            event.errorText = e.getMessage();
            event.payload = null;
        } catch (Exception ex) {
            event.statusCode = ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue();
            event.errorText = ex.getMessage();
            event.payload = null;
        }
        sendResponse(event, messageProperties, ROUTING_KEY_INTERNAL_AUTHORIZATION);
    }

    private Event handleNoSupportMethod(Event event) {
        logger.error("Method not support {}", event.method);
        return ErrorUtils.handleErrorResponse(ResponseStatusCode.NOT_IMPLEMENTED.intValue(), event, "MethodNotSupport");
    }
}
