package vn.vnpt.oneiot.core.api.nbi.models;

import vn.vnpt.oneiot.core.api.nbi.models.response.ResponseBase;

import java.util.List;

public class AddGroupRespone extends ResponseBase {
    private List<Device> devices;
    private List<ApplicationDomain> domains;

    public List<Device> getDevices() {
        return devices;
    }

    public void setDevices(List<Device> devices) {
        this.devices = devices;
    }

    public List<ApplicationDomain> getDomains() {
        return domains;
    }

    public void setDomains(List<ApplicationDomain> domains) {
        this.domains = domains;
    }
}
