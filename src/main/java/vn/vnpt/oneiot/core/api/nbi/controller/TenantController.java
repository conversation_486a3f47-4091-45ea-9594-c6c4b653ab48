package vn.vnpt.oneiot.core.api.nbi.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.core.CSEInitialize;
import vn.vnpt.oneiot.core.mongo.entity.TenantEntity;
import vn.vnpt.oneiot.core.mongo.services.M2MServiceSubscriptionProfileService;
import vn.vnpt.oneiot.core.mongo.services.TenantService;

/**
 * Created by huyvv
 * Date: 05/02/2020
 * Time: 11:06 PM
 * for all issues, contact me: <EMAIL>
 **/
@Component
public class TenantController extends MgCrudAdminController<TenantEntity, String> {
    @SuppressWarnings("unused")
    private static Logger logger = LoggerFactory.getLogger(TenantController.class);

    @Autowired
    public TenantController(TenantService service) {
        super(service);
    }

    public Event process(Event event) {
        logger.info("TenantController receive method: #{}", event.method);
        return super.process(event);
    }
}
