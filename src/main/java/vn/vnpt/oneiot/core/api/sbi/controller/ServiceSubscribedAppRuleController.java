package vn.vnpt.oneiot.core.api.sbi.controller;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import org.bson.BsonRegularExpression;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import vn.vnpt.oneiot.base.utils.DateUtils;
import vn.vnpt.oneiot.common.constants.MimeMediaType;
import vn.vnpt.oneiot.common.constants.ResourceType;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.common.entities.ServiceSubscribedAppRuleEntity;
import vn.vnpt.oneiot.common.entities.TransactionEntity;
import vn.vnpt.oneiot.common.entitymapper.EntityMapperFactory;
import vn.vnpt.oneiot.common.exceptions.BadRequestException;
import vn.vnpt.oneiot.common.exceptions.ConflictException;
import vn.vnpt.oneiot.common.exceptions.ResourceNotFoundException;
import vn.vnpt.oneiot.common.resource.RequestPrimitive;
import vn.vnpt.oneiot.common.resource.ResponsePrimitive;
import vn.vnpt.oneiot.common.resource.ServiceSubscribedAppRule;
import vn.vnpt.oneiot.common.utils.ControllerUtil;
import vn.vnpt.oneiot.common.utils.DateConverter;
import vn.vnpt.oneiot.common.utils.ResourceUtils;
import vn.vnpt.oneiot.core.mongo.services.ServiceSubscribedAppRuleService;

import java.util.ArrayList;

@Controller
@Transactional
public class ServiceSubscribedAppRuleController extends ResourceRequestController {

    static final Logger logger = LoggerFactory.getLogger(ServiceSubscribedAppRuleController.class);
    private ServiceSubscribedAppRuleService serviceSubscribedAppRuleService;

    @Autowired
    public void setServiceSubscribedAppRuleService(ServiceSubscribedAppRuleService serviceSubscribedAppRuleService) {
        this.serviceSubscribedAppRuleService = serviceSubscribedAppRuleService;
    }

    @Override
    public ResponsePrimitive doCreate(RequestPrimitive request) {
        ResponsePrimitive responsePrimitive = new ResponsePrimitive(request);
        /** Check content */
        if (request.getContent() == null) {
            throw new BadRequestException("A content is required for ServiceSubscribedAppRule creation");
        }

        ServiceSubscribedAppRule asar = null;
        try {
            asar = new Gson().fromJson(request.getContent(), ServiceSubscribedAppRule.class);
        } catch (JsonSyntaxException e) {
            logger.debug("ClassCastException: Incorrect resource type in object conversion");
            throw new BadRequestException("Incorrect resource representation in content", e);
        }
        if (asar == null) {
            throw new BadRequestException("Error in provided content");
        }
        ServiceSubscribedAppRuleEntity asarEntity = new ServiceSubscribedAppRuleEntity();
        ControllerUtil.CreateUtil.fillEntityFromRegularResource(asar, asarEntity);

        /** OPTIONAL attribute in CREATE request */
        if (ControllerUtil.isPermittedCreated(asar, "applicableCredIDs", asar.getApplicableCredIDs())) {
            String credIds = "";
            for(String credId: asar.getApplicableCredIDs()) {
                credId = credId.replace("*", ".*");
                credIds = credIds + "^" + credId + "$|";
            }
            asarEntity.setApplicableCredIDs(new BsonRegularExpression(credIds.substring(0, credIds.length() - 1)));
        } else {
            asarEntity.setApplicableCredIDs(new BsonRegularExpression(".*"));
        }
        if (ControllerUtil.isPermittedCreated(asar, "allowedAppIDs", asar.getAllowedAppIDs())) {
            String allowAppIds = "";
            for(String appId: asar.getAllowedAppIDs()) {
                appId = appId.replace("*", ".*");
                allowAppIds = allowAppIds + "^" + appId + "$|";
            }
            asarEntity.setAllowedAppIDs(new BsonRegularExpression(allowAppIds.substring(0, allowAppIds.length() - 1)));
        }
        if (ControllerUtil.isPermittedCreated(asar, "allowedAEs", asar.getAllowedAEs())) {
            String allowedAEs = "";
            for(String ae:asar.getAllowedAEs()) {
                ae = ae.replace("*", ".*");
                allowedAEs = allowedAEs + "^" + ae + "$|";
            }
            asarEntity.setAllowedAEs(new BsonRegularExpression(allowedAEs.substring(0, allowedAEs.length() - 1)));
            asarEntity.setAllowedAEsString(asarEntity.getAllowedAEs().getPattern());
        }



        asarEntity.setResourceID(ResourceUtils.generateResourceId(ResourceType.SERVICE_SUBSCRIBED_APP_RULE));
        if (asar.getName() != null && !asar.getName().isEmpty()) {
            asarEntity.setName(asar.getName());
            if (serviceSubscribedAppRuleService.getOneByParentIdAndName(request.getTo(), asar.getName()) != null) {
                throw new ConflictException("The same resource name is already existed");
            }
        } else {
            asarEntity.setName(asarEntity.getResourceID());
        }


        asarEntity.setHierarchicalURI(request.getHuriTarget() + "/" + asarEntity.getName());
        asarEntity.setParentID(request.getTo());
        asarEntity.setResourceType(ResourceType.SERVICE_SUBSCRIBED_APP_RULE);

        asarEntity = serviceSubscribedAppRuleService.create(asarEntity);
        responsePrimitive.setResponseStatusCode(ResponseStatusCode.CREATED);
        responsePrimitive.setResourceEntity(new Gson().toJson(asarEntity));
        responsePrimitive.setResourceType(ResourceType.SERVICE_SUBSCRIBED_APP_RULE);
        setLocationAndCreationContent(request, responsePrimitive, asarEntity);
        orchestrationService.saveTransaction(responsePrimitive, asarEntity, TransactionEntity.EVENT_CREATED);
        return responsePrimitive;
    }

    @Override
    public ResponsePrimitive doRetrieve(RequestPrimitive request) {
        ResponsePrimitive response = new ResponsePrimitive(request);
        ServiceSubscribedAppRuleEntity asarEntity = serviceSubscribedAppRuleService.get(request.getTo());
        if (asarEntity == null) {
            throw new ResourceNotFoundException();
        }
        ServiceSubscribedAppRule asar = EntityMapperFactory.getServiceSubscribedAppRuleMapper().mapEntityToResource(asarEntity, request);
        response.setContent(new Gson().toJson(asar));
        response.setResourceType(ResourceType.SERVICE_SUBSCRIBED_APP_RULE);
        response.setResponseStatusCode(ResponseStatusCode.OK);
        return response;
    }

    @Override
    public ResponsePrimitive doUpdate(RequestPrimitive request) {
        ResponsePrimitive response = new ResponsePrimitive(request);

        // Retrieve the resource from DB
        ServiceSubscribedAppRuleEntity asarEntity = serviceSubscribedAppRuleService.get(request.getTo());

        // Check resource existence
        if (asarEntity == null){
            throw new ResourceNotFoundException("Resource " + request.getTo() + " not found.");
        }

        // Check if content is present
        if (request.getContent() == null){
            throw new BadRequestException("A content is required for ServiceSubscribedAppRule update");
        }

        // Create the java object from the resource representation
        ServiceSubscribedAppRule asar = null;
        ServiceSubscribedAppRule modifiedAttributes = new ServiceSubscribedAppRule();
        try{
            asar = new Gson().fromJson(request.getContent(), ServiceSubscribedAppRule.class);
        } catch (ClassCastException e){
            logger.debug("ClassCastException: Incorrect resource type in object conversion.",e);
            throw new BadRequestException("Incorrect resource representation in content", e);
        }
        if (asar == null) {
            throw new BadRequestException("Error in provided content");
        }

        // NP attributes
        // @resourceName 		NP
        // resourceType 		NP
        // resourceID 			NP
        // parentID 			NP
        // creationTime 		NP
        // lastModifiedTime 	NP
        ControllerUtil.UpdateUtil.fillEntityFromRegularResource(asar, asarEntity, modifiedAttributes);

        /** OPTIONAL attribute in UPDATE request */
        if (asar.getApplicableCredIDs() == null) {
            /** applicableCredIDs is must present, so set to default accept all */
            asarEntity.setApplicableCredIDs(new BsonRegularExpression(".*"));
            modifiedAttributes.setApplicableCredIDs(asar.getApplicableCredIDs());
        } else if (!asar.getApplicableCredIDs().isEmpty()){
            String credIds = "";
            for (String credId : asar.getApplicableCredIDs()) {
                credIds = credIds + credId + "|";
            }
            asarEntity.setApplicableCredIDs(new BsonRegularExpression(credIds.substring(0, credIds.length() - 1)));
            modifiedAttributes.setApplicableCredIDs(asar.getApplicableCredIDs());
        }

        if (asar.getAllowedAppIDs() == null) {
            asarEntity.setAllowedAppIDs(null);
            modifiedAttributes.setAllowedAppIDs(asar.getAllowedAppIDs());
        } else if (!asar.getAllowedAppIDs().isEmpty()) {
            String allowAppIds = "";
            for(String appId: asar.getApplicableCredIDs()) {
                allowAppIds = allowAppIds + appId + "|";
            }
            asarEntity.setAllowedAppIDs(new BsonRegularExpression(allowAppIds.substring(0, allowAppIds.length() - 1)));
            modifiedAttributes.setAllowedAppIDs(asar.getAllowedAppIDs());
        }
        if (asar.getAllowedAEs() == null) {
            asarEntity.setAllowedAEs(null);
            asarEntity.setAllowedAEsString(null);
            modifiedAttributes.setAllowedAEs(asar.getAllowedAEs());
        } else if(!asar.getAllowedAEs().isEmpty()) {
            String allowedAEs = "";
            for(String ae:asar.getAllowedAEs()) {
                allowedAEs = allowedAEs + ae + "|";
            }
            asarEntity.setAllowedAEs(new BsonRegularExpression(allowedAEs.substring(0, allowedAEs.length() - 1)));
            asarEntity.setAllowedAEsString(asarEntity.getAllowedAEs().getPattern());
            modifiedAttributes.setAllowedAEs(asar.getAllowedAEs());
        }
        asarEntity = serviceSubscribedAppRuleService.update(asarEntity);
        modifiedAttributes.setLastModifiedTime(DateConverter.convertUTCLongtoUTCString(asarEntity.getLastModifiedTime()));;
        response.setContent(new Gson().toJson(modifiedAttributes));
        response.setResourceEntity(new Gson().toJson(asarEntity));
        response.setResourceType(ResourceType.SERVICE_SUBSCRIBED_APP_RULE);
        response.setResponseStatusCode(ResponseStatusCode.UPDATED);
        orchestrationService.saveTransaction(response, asarEntity, TransactionEntity.EVENT_UPDATE);
        return response;
    }

    @Override
    public ResponsePrimitive doDelete(RequestPrimitive request) {
        ResponsePrimitive response = new ResponsePrimitive(request);
        ServiceSubscribedAppRuleEntity asarEntity = serviceSubscribedAppRuleService.get(request.getTo());
        if (asarEntity == null) {
            throw new ResourceNotFoundException();
        }
        serviceSubscribedAppRuleService.delete(asarEntity.getResourceID());
        response.setResourceEntity(new Gson().toJson(asarEntity));
        response.setResponseStatusCode(ResponseStatusCode.DELETED);
        orchestrationService.saveTransaction(response, asarEntity, TransactionEntity.EVENT_DELETE);
        return response;
    }
}
