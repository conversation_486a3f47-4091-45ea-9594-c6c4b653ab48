package vn.vnpt.oneiot.core.api.nbi.models.request;

public class GetDeviceListRequest {
    private String mainAppId;
    private String deviceType;
    private Integer offset;
    private Integer limit;
    private Long fromDate;
    private Long toDate;
    private String holderId;

    public GetDeviceListRequest() {
    }

    public GetDeviceListRequest(String mainAppId, String deviceType, Integer offset, Integer limit, Long fromDate, Long toDate, String holderId) {
        this.mainAppId = mainAppId;
        this.deviceType = deviceType;
        this.offset = offset;
        this.limit = limit;
        this.fromDate = fromDate;
        this.toDate = toDate;
        this.holderId = holderId;
    }

    public String getMainAppId() {
        return mainAppId;
    }

    public void setMainAppId(String mainAppId) {
        this.mainAppId = mainAppId;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Long getFromDate() {
        return fromDate;
    }

    public void setFromDate(Long fromDate) {
        this.fromDate = fromDate;
    }

    public Long getToDate() {
        return toDate;
    }

    public void setToDate(Long toDate) {
        this.toDate = toDate;
    }

    public String getHolderId() {
        return holderId;
    }

    public void setHolderId(String holderId) {
        this.holderId = holderId;
    }
}
