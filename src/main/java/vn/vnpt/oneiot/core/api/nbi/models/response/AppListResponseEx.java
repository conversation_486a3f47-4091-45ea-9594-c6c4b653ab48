package vn.vnpt.oneiot.core.api.nbi.models.response;

public class AppListResponseEx {
    private String appId;
    private String tenantId;
    private String appName;
    private Integer appType;
    private String appDomainId;
    private Integer active;
    private Long created;
    private Long updated;
    private String appDomainName;

    public AppListResponseEx() {
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public Integer getAppType() {
        return appType;
    }

    public void setAppType(Integer appType) {
        this.appType = appType;
    }

    public String getAppDomainId() {
        return appDomainId;
    }

    public void setAppDomainId(String appDomainId) {
        this.appDomainId = appDomainId;
    }

    public Integer getActive() {
        return active;
    }

    public void setActive(Integer active) {
        this.active = active;
    }

    public Long getCreated() {
        return created;
    }

    public void setCreated(Long created) {
        this.created = created;
    }

    public Long getUpdated() {
        return updated;
    }

    public void setUpdated(Long updated) {
        this.updated = updated;
    }

    public String getAppDomainName() {
        return appDomainName;
    }

    public void setAppDomainName(String appDomainName) {
        this.appDomainName = appDomainName;
    }
}
