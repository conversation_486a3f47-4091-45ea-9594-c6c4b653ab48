package vn.vnpt.oneiot.core.api.sbi.controller;

import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import vn.vnpt.oneiot.base.event.EventBus;
import vn.vnpt.oneiot.common.constants.MimeMediaType;
import vn.vnpt.oneiot.common.constants.Operation;
import vn.vnpt.oneiot.common.constants.ResultContent;
import vn.vnpt.oneiot.common.entities.ResourceEntity;
import vn.vnpt.oneiot.common.entitymapper.EntityMapper;
import vn.vnpt.oneiot.common.entitymapper.EntityMapperFactory;
import vn.vnpt.oneiot.common.exceptions.PlatformException;
import vn.vnpt.oneiot.common.resource.RequestPrimitive;
import vn.vnpt.oneiot.common.resource.Resource;
import vn.vnpt.oneiot.common.resource.ResponsePrimitive;
import vn.vnpt.oneiot.core.orchestration.OrchestrationService;

/**
 * Controller class contains generic and abstract Create, Retrieve, Update, Delete and Execute
 * methods to handle generic request primitive.
 */

public abstract class ResourceRequestController {
    protected static Logger logger = LoggerFactory.getLogger(ResourceRequestController.class);

    @Autowired
    EventBus eventBus;

    @Autowired
    OrchestrationService orchestrationService;


    public ResponsePrimitive doRequest(RequestPrimitive request) throws PlatformException {
        logger.info("Process resource request: " + request.toString());
        ResponsePrimitive response = new ResponsePrimitive(request);

        if (request.getOperation().equals(Operation.CREATE)) {
            response = doCreate(request);
        } else if (request.getOperation().equals(Operation.RETRIEVE)) {
            response = doRetrieve(request);
        } else if (request.getOperation().equals(Operation.UPDATE)) {
            response = doUpdate(request);
        } else if (request.getOperation().equals(Operation.DELETE)) {
            response = doDelete(request);
        } else if (request.getOperation().equals(Operation.INTERNAL_NOTIFY)) {
            response = doNotify(request);
        }

        return response;
    }

    /**
     * Handle create operation
     */
    public abstract ResponsePrimitive doCreate(RequestPrimitive request);

    /**
     * Handle retrieve operation
     */
    public abstract ResponsePrimitive doRetrieve(RequestPrimitive request);

    /**
     * Handle update operation
     */
    public abstract ResponsePrimitive doUpdate(RequestPrimitive request);

    /**
     * Handle delete operation
     */
    public abstract ResponsePrimitive doDelete(RequestPrimitive request);

    /**
     * Handle notify operation
     */
    public ResponsePrimitive doNotify(RequestPrimitive request) {
        return null;
    }


    protected void setLocationAndCreationContent(RequestPrimitive request,
                                                 ResponsePrimitive response, ResourceEntity entity) {
        EntityMapper mapper = EntityMapperFactory.getMapperFromResourceType(entity.getResourceType().intValue());
        setLocationAndCreationContent(request, response, entity, mapper);
    }

    protected void setLocationAndCreationContent(RequestPrimitive request,
                                                 ResponsePrimitive response, ResourceEntity entity, EntityMapper mapper) {
        if (request.getResultContent() != null) {
            if (request.getResultContent().equals(ResultContent.HIERARCHICAL_ADRESS)
                    || request.getResultContent().equals(ResultContent.HIERARCHICAL_AND_ATTRIBUTES)) {
                response.setLocation(entity.getHierarchicalURI());
            } else {
                response.setLocation(entity.getResourceID());
            }
            if (request.getResultContent().equals(ResultContent.HIERARCHICAL_AND_ATTRIBUTES)
                    || request.getResultContent().equals(ResultContent.ATTRIBUTES)) {
                Resource res = mapper.mapEntityToResource(entity, ResultContent.ATTRIBUTES, 0, 0);
                response.setContent(new Gson().toJson(res));
            }
        } else {
            response.setContent(new Gson().toJson(mapper.mapEntityToResource(entity, ResultContent.ATTRIBUTES, 0, 0)));
            response.setLocation(entity.getResourceID());
        }
    }

}
