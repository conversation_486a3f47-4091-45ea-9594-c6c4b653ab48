package vn.vnpt.oneiot.core.api.sbi.controller;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import vn.vnpt.oneiot.base.utils.DateUtils;
import vn.vnpt.oneiot.common.constants.MimeMediaType;
import vn.vnpt.oneiot.common.constants.ResourceType;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.common.entities.M2MServiceSubscriptionProfileEntity;
import vn.vnpt.oneiot.common.entities.TransactionEntity;
import vn.vnpt.oneiot.common.entitymapper.EntityMapperFactory;
import vn.vnpt.oneiot.common.exceptions.BadRequestException;
import vn.vnpt.oneiot.common.exceptions.ConflictException;
import vn.vnpt.oneiot.common.exceptions.ResourceNotFoundException;
import vn.vnpt.oneiot.common.resource.M2MServiceSubscriptionProfile;
import vn.vnpt.oneiot.common.resource.RequestPrimitive;
import vn.vnpt.oneiot.common.resource.ResponsePrimitive;
import vn.vnpt.oneiot.common.utils.ControllerUtil;
import vn.vnpt.oneiot.common.utils.DateConverter;
import vn.vnpt.oneiot.common.utils.ResourceUtils;
import vn.vnpt.oneiot.core.mongo.services.M2MServiceSubscriptionProfileService;

@Controller
@Transactional
public class M2MServiceSubscriptionProfileController extends ResourceRequestController {

    static final Logger logger = LoggerFactory.getLogger(M2MServiceSubscriptionProfileController.class);
    private M2MServiceSubscriptionProfileService m2MServiceSubscriptionProfileService;

    @Autowired
    public void setM2MServiceSubscriptionProfileService(M2MServiceSubscriptionProfileService m2MServiceSubscriptionProfileService) {
        this.m2MServiceSubscriptionProfileService = m2MServiceSubscriptionProfileService;
    }

    @Override
    public ResponsePrimitive doCreate(RequestPrimitive request) {

        ResponsePrimitive responsePrimitive = new ResponsePrimitive(request);
        /** Check content */
        if (request.getContent() == null) {
            throw new BadRequestException("A content is required for M2MServiceSubscriptionProfile creation");
        }

        M2MServiceSubscriptionProfile mssp = null;
        try {
            mssp = new Gson().fromJson(request.getContent(), M2MServiceSubscriptionProfile.class);
        } catch (JsonSyntaxException e) {
            logger.debug("ClassCastException: Incorrect resource type in object conversion");
            throw new BadRequestException("Incorrect resource representation in content", e);
        }
        if (mssp == null) {
            throw new BadRequestException("Error in provided content");
        }
        M2MServiceSubscriptionProfileEntity msspEntity = new M2MServiceSubscriptionProfileEntity();
        ControllerUtil.CreateUtil.fillEntityFromRegularResource(mssp, msspEntity);
        msspEntity.setResourceID(ResourceUtils.generateResourceId(ResourceType.M2M_SERVICE_SUBSCRIPTION_PROFILE));
        if (ControllerUtil.isPermittedCreated(mssp, "name", mssp.getName())) {
            msspEntity.setName(mssp.getName());
            if (m2MServiceSubscriptionProfileService.getOneByParentIdAndName(request.getTo(), mssp.getName()) != null) {
                throw new ConflictException("The same resource name is already existed");
            }
        } else {
            msspEntity.setName(msspEntity.getResourceID());
        }

        msspEntity.setHierarchicalURI(request.getHuriTarget() + "/" + msspEntity.getName());
        msspEntity.setParentID(request.getTo());
        msspEntity.setResourceType(ResourceType.M2M_SERVICE_SUBSCRIPTION_PROFILE);
        msspEntity = m2MServiceSubscriptionProfileService.create(msspEntity);
        responsePrimitive.setResponseStatusCode(ResponseStatusCode.CREATED);
        responsePrimitive.setResourceEntity(new Gson().toJson(msspEntity));
        responsePrimitive.setResourceType(ResourceType.M2M_SERVICE_SUBSCRIPTION_PROFILE);
        setLocationAndCreationContent(request, responsePrimitive, msspEntity);
        orchestrationService.saveTransaction(responsePrimitive, msspEntity, TransactionEntity.EVENT_CREATED);
        return responsePrimitive;
    }

    @Override
    public ResponsePrimitive doRetrieve(RequestPrimitive request) {
        ResponsePrimitive response = new ResponsePrimitive(request);
        M2MServiceSubscriptionProfileEntity msspEntity = m2MServiceSubscriptionProfileService.get(request.getTo());
        if (msspEntity == null) {
            throw new ResourceNotFoundException();
        }
        M2MServiceSubscriptionProfile mssp = EntityMapperFactory.getM2MServiceSubscriptionProfileMapper().mapEntityToResource(msspEntity, request);
        response.setContent(new Gson().toJson(mssp));
        response.setResourceType(ResourceType.M2M_SERVICE_SUBSCRIPTION_PROFILE);
        response.setResponseStatusCode(ResponseStatusCode.OK);
        return response;
    }

    @Override
    public ResponsePrimitive doUpdate(RequestPrimitive request) {
        ResponsePrimitive response = new ResponsePrimitive(request);

        // Retrieve the resource from DB
        M2MServiceSubscriptionProfileEntity msspEntity = m2MServiceSubscriptionProfileService.get(request.getTo());

        // Check resource existence
        if (msspEntity == null){
            throw new ResourceNotFoundException("Resource " + request.getTo() + " not found.");
        }

        // Check if content is present
        if (request.getContent() == null){
            throw new BadRequestException("A content is requiered for M2MServiceSubscriptionProfile update");
        }

        // Create the java object from the resource representation
        M2MServiceSubscriptionProfile mssp = null;
        M2MServiceSubscriptionProfile modifiedAttributes = new M2MServiceSubscriptionProfile();
        try{
            mssp = new Gson().fromJson(request.getContent(), M2MServiceSubscriptionProfile.class);
        } catch (ClassCastException e){
            logger.debug("ClassCastException: Incorrect resource type in object conversion.",e);
            throw new BadRequestException("Incorrect resource representation in content", e);
        }
        if (mssp == null) {
            throw new BadRequestException("Error in provided content");
        }

        // NP attributes
        // @resourceName 		NP
        // resourceType 		NP
        // resourceID 			NP
        // parentID 			NP
        // creationTime 		NP
        // lastModifiedTime 	NP
        ControllerUtil.UpdateUtil.fillEntityFromRegularResource(mssp, msspEntity, modifiedAttributes);
        msspEntity = m2MServiceSubscriptionProfileService.update(msspEntity);
        modifiedAttributes.setLastModifiedTime(DateConverter.convertUTCLongtoUTCString(msspEntity.getLastModifiedTime()));;
        response.setContent(new Gson().toJson(modifiedAttributes));
        response.setResourceEntity(new Gson().toJson(msspEntity));
        response.setResourceType(ResourceType.M2M_SERVICE_SUBSCRIPTION_PROFILE);
        response.setResponseStatusCode(ResponseStatusCode.UPDATED);
        orchestrationService.saveTransaction(response, msspEntity, TransactionEntity.EVENT_UPDATE);
        return response;
    }

    @Override
    public ResponsePrimitive doDelete(RequestPrimitive request) {
        ResponsePrimitive response = new ResponsePrimitive(request);
        M2MServiceSubscriptionProfileEntity msspEntity = m2MServiceSubscriptionProfileService.get(request.getTo());
        if (msspEntity == null) {
            throw new ResourceNotFoundException();
        }
        m2MServiceSubscriptionProfileService.delete(msspEntity.getResourceID());
        response.setResourceEntity(new Gson().toJson(msspEntity));
        response.setResponseStatusCode(ResponseStatusCode.DELETED);
        orchestrationService.saveTransaction(response, msspEntity, TransactionEntity.EVENT_DELETE);
        return response;
    }
}
