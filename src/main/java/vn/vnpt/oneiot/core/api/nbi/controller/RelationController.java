package vn.vnpt.oneiot.core.api.nbi.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.core.mongo.entity.RelationEntity;
import vn.vnpt.oneiot.core.mongo.services.RelationService;

/**
 * Created by huyvv
 * Date: 20/01/2020
 * Time: 6:08 PM
 * for all issues, contact me: <EMAIL>
 **/
@Component
public class RelationController extends MgCrudAdminController<RelationEntity, String> {
    private static Logger logger = LoggerFactory.getLogger(RelationController.class);

    public class Method {
        public static final String SEARCH_DEVICE_TO_ADD_INTO_GROUP_OF_APP = "searchDeviceToAddIntoGroupOfApp";
    }

    public class SearchDeviceToAddGroupRequest {
        public String groupName;
        public String appId;
        public String deviceName;
    }

    @Autowired
    public RelationController(RelationService service) {
        super(service);
    }

    public Event process(Event event) {
        logger.info("RelationController receive method: #{}", event.method);
        return super.process(event);
    }
}
