package vn.vnpt.oneiot.core.api.nbi.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.core.mongo.services.PrivilegeService;
import vn.vnpt.oneiot.core.mongo.entity.PrivilegeEntity;

/**
 * Created by huyvv
 * Date: 20/01/2020
 * Time: 6:08 PM
 * for all issues, contact me: <EMAIL>
 **/
@Component
public class PrivilegeController extends MgCrudAdminController<PrivilegeEntity, String> {
    private static Logger logger = LoggerFactory.getLogger(PrivilegeController.class);

    @Autowired
    public PrivilegeController(PrivilegeService service) {
        super(service);
    }

    public Event process(Event event) {
        logger.info("PrivilegeController receive method: #{}", event.method);
        return super.process(event);
    }
}
