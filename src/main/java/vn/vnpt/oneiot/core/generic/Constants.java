package vn.vnpt.oneiot.core.generic;

import vn.vnpt.oneiot.base.constants.AMQPConstant;

import static vn.vnpt.oneiot.base.constants.AMQPConstant.ROUTING_KEY_API_REGISTRATION_GROUP_SINGLE;

/**
 * Created by huyvv
 * Date: 17/01/2020
 * Time: 5:05 PM
 * for all issues, contact me: <EMAIL>
 **/
public interface Constants {
    interface Method {
        String CURRENT_USER = "current_user";
        String RESET_PASSWORD = "reset_password";
        String FORGOT_PASSWORD_INIT = "forgot_password_init";
        String FORGOT_PASSWORD_FINISH = "forgot_password_finish";
        String CHANGE_PASSWORD = "change_password";
        String VALIDATE_TOKEN_MAIL = "validate_token_mail";
        String RE_SEND_EMAIL = "reSendEmail";

        String VALIDATE_PERMISSION = "validate_permission";
        String LOGIN = "login";
        String AUTH2_LOGIN = "auth2_login";
        String LOGOUT = "logout";
        String GEN_AUTH_CODE = "genAuthCode";
        String AUTHORIZE_BY_ONEM2M = AMQPConstant.AUTHORIZE_BY_ONEM2M;
        String AUTHORIZE_BY_PLATFOROM = AMQPConstant.AUTHORIZE_BY_PLATFORM;
        String DYNAMIC_LOGIN = "dynamic_login";
        String AUTHORIZE_BY_PLATFORM = AMQPConstant.AUTHORIZE_BY_PLATFORM;


        String DELETE_DEVICES_BY_TENANT_IDS = "delete_devices_by_tenant_ids";
        String DELETE_APPS_BY_TENANT_IDS = "batchDeleteByTenant";
        String GET_TENANT_BY_CODE = "getTenantByCode";
        String GET_TENANT_NAME = "tenantName";
        String GET_TENANT_BY_IOT_MARKET_PLACE_UUID = "tenantIOTMarketPlaceUUID";

        String HANDLING_RELATION_ENTITY = "handling_relation_entity";
        String CHECK_RELATION = "checkRelation";
        String UPDATE_NAME = "updateName";
        String GET_LIST_RELATION_BY_LIST_NAME_AND_TYPE = "getListRelationByListNameAndType";

        // method for token
        String GET_TOKEN = "GET_TOKEN";
        String REFRESH_TOKEN_DEVICE = "REFRESH_TOKEN_DEVICE"; // Nhan response khi refresh token cho device
        String REFRESH_TOKEN = "REFRESH_TOKEN";
        String SELF_REFRESH_TOKEN="SELF_REFRESH_TOKEN";
        String GET_TOKEN_INFO = "GET_TOKEN_INFO";
        String SYNCHRONIZED_DEVICE_TOKEN = "SYNCHRONIZED_DEVICE_TOKEN";
        String CHECK_TOKEN = "CHECK_TOKEN";
        String VALIDATE_TOKEN = "VALIDATE_TOKEN";
        String AUTH2_VALIDATE_TOKEN = "AUTH2_VALIDATE_TOKEN";

        String CONFIG_PLATFORM = "CONFIG_PLATFORM";
        String PRIMITIVE = "PRIMITIVE";
        String CREATE_DEVICE = "createDevice";
        String UPDATE_DEVICE = "updateDevice";
        String GET_DEVICE_BY_ID = "getDeviceById";
        String GET_DEVICE_LIST = "getDeviceList";
        String DELETE_DEVICE = "deleteDevice";
        String SEARCH_DEVICE = "searchDevice";
        String GET_DEVICE_INFO = "getDeviceInfo";
        String REGISTER_DEVICE = "registerDevice";
        String GET_LIST_DEVICE_INFO = "getListDeviceInfo";
        String GET_DEVICE_BY_NAME = "getDeviceByName";
        String REFRESH_DEVICE_TOKEN = "refreshToken";
        String REFRESH_DEVICE_EX_TOKEN = "refreshTokenEx";
        String DEVICE_AUTHEN = "deviceAuthen";
        String DEVICE_CONNECT = "deviceConnect";
        String GET_LIST_DEVICE_BY_IDS = "getListDeviceByIds";
        String GET_LIST_DEVICE_BY_NAMES = "getListDeviceByNames";
        String UPDATE_LIST_DEVICE = "updateListDevice";
        String UPDATE_GROUP_NAME_OF_DEVICE = "updateGroupNameOfDevice";
        String GET_DEVICE_BY_IPE_NAME = "getDeviceByIpeName";
        String ASSIGN_SIM = "assignSim";
        String UN_ASSIGN_SIM = "unAssignSim";
        String ASSIGN_LIST_SIM = "assignListSim";
        String UN_ASSIGN_LIST_SIM = "unAssignListSim";
        String CHECK_LIST_DEVICE_ID = "checkListDeviceId";
        String CHECK_LIST_SIM_ASSIGN = "checkListSimAssign";
        String CREATE_APP_DOMAIN = "createAppDomain";
        String DELETE_APP_DOMAIN = "deleteAppDomain";
        String GET_APP_DOMAIN_DETAILS = "getDetailsAppDomain";
        String GET_APP_DOMAIN_LIST = "getListAppDomain";
        String GET_LIST_GATEWAY_BY_APPDOMAIN = "getListGatewayByAppDomain";
        String ADD_TOPO_GATEWAY = "addTopoGateway";
        String GET_LIST_TOPO = "getListTopo";
        String GET_STATUS_AE = "GET_STATUS_AE";
        String DELETE_TOPO = "deleleTopo";
        String BULK_REGISTER_DEVICE = "bulkRegisterDevice";
        String SEARCH_TOPO = "searchTopo";
        String GET_ALL_CATEGORIES = "getAllCategories";
        String SEARCH_SUB_DEVICE_TO_BIND = "searchSubDeviceToBind";
        String GET_DEVICE_BY_DEVICEID_AND_USERID = "getDeviceByDeviceIdAndUserId";
        String GET_DEVICE_DETAIL_INFO = "getDeviceDetailInfo";
        String COUNT_DEVICE_BY_TENANT = "countDeviceByTenant";
        String COUNT_USER_BY_TENANT = "countUserByTenant";
    }

    interface UserType {
        int PLATFROM_ADMIN = 1;
        int PLATFORM_USER = 2;
        int TENANT_ADMIN = 3;
        int TENANT_USER = 4;
    }

    interface IDENTITY_TYPE {
        int APP_IDENTITY_TYPE = 1;
        int DEVICE_IDENTITY_TYPE = 2;
        int GROUP_IDENTITY_TYPE = 3;
        int APP_DOMAIN_IDENTITY_TYPE = 4;
    }

    interface APP_DOMAIN {
        interface AUTHORITY_TYPE {
            int REGISTERED = 1;
            int NON_REGISTRERED = 0;
        }
    }

    interface IDENTITY_SUB_TYPE {
        int GENERAL_DEVICE_TYPE = 1;
        int GATE_WAY_TYPE = 2;
        int SUB_DEVICE_TYPE = 3;
        int MAIN_APP_TYPE = 0;
        int USER_APP_TYPE = 1;
    }

    interface RELATION_TYPE {
        int OWNER = 1;
        int COMMON = 2;
        int ADMIN = 3;
        int PARENT = 4;
    }

    interface APP {
        String CREATE_APP = "createApp";
        String DELETE_APP = "deleteApp";
        String GET_APP_DETAILS = "getAppDetails";
        String GET_APP_LIST = "getAppList";
		String DELETE_BY_APP_ID = "deleteByAppId";
		String GET_LIST_RELATION_APP = "getListRelationApp";
        String ADD_ENTITY_TO_APP = "addEntityToApp";
        String DELETE_RELATION_APP ="deleteRelationApp";
        String GET_RELATION_BY_NAME = "getRelationByName";
        String GET_APP_DOMAIN_BY_NAME = "getAppDomainByName";
        String GET_DEVICE_GROUP_BY_APPDOMAIN = "getDeviceGroupByUser";
        String CREATE_APP_EX = "create_app_ex";
        String ADD_ENTITY_TO_APP_EX = "addEntityToApp_ex";
        String DELETE_ENTITY_TO_APP_EX = "deleteEntityToApp_ex";
        String GET_APP_LIST_EX = "getAppListEx";
    }
    interface GROUP {
        String GET_LIST_GROUP = "getListGroup";
        String GET_LIST_GROUP_SIMPLE ="getListGroupSimple";
        String GET_GROUP_DETAIL ="getGroupDetail";
        String GET_LIST_DEVICE_TO_ADD ="getDeviceToAdd";
        String GET_LIST_DEVICE_IDS_BY_GROUP_IDS = "getListDeviceIdsByGroupIds";
    }

    String ROUTING_KEY_USER = "routingKey.User";
    String ROUTING_KEY_AUTH = "routingKey.Auth";
    String ROUTING_KEY_ROLE = "routingKey.Role";
    String ROUTING_KEY_PRIVILEGE = "routingKey.Privilege";
    String ROUTING_KEY_RELATION = "routingKey.Relation";
    String ROUTING_KEY_COUNTRY = "routingKey.Country";
    String ROUTING_KEY_TENANT = "routingKey.Tenant";
    String ROUTING_KEY_APPLICATION = "routingKey.Application";
    String ROUTING_KEY_DEVICE = "routingKey.Device";
    String ROUTING_KEY_APPLICATON_DOMAIN = "routingKey.ApplicationDomain";
    String ROUTING_KEY_ACCESS_CONTROL_POLICY = "routingKey.AccessControlPolicy";
    String ROUTING_KEY_ACCESS_CONTROL_RULE = "routingKey.AccessControlRule";
    String ROUTING_KEY_DYNAMIC_TOKEN = "routingKey.DynamicToken";
    String SYSTEM = "system";
    String ROUTING_KEY_DEVICE_STATUS = ROUTING_KEY_API_REGISTRATION_GROUP_SINGLE + ".routingKey.AE";
    String ROUTING_KEY_GROUP = "routingKey.Group";
    String ROUTING_KEY_MARKET_PLACE ="MarketPlace";
}
