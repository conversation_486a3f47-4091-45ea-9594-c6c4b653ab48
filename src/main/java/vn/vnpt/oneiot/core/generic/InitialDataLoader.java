package vn.vnpt.oneiot.core.generic;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import vn.vnpt.oneiot.common.entities.RoleEntity;
import vn.vnpt.oneiot.core.constants.Constants;
import vn.vnpt.oneiot.core.mongo.entity.PrivilegeEntity;
import vn.vnpt.oneiot.core.mongo.entity.TenantEntity;
import vn.vnpt.oneiot.core.mongo.repositories.PrivilegeRepository;
import vn.vnpt.oneiot.core.mongo.repositories.TenantRepository;
import vn.vnpt.oneiot.core.mongo.entity.UserEntity;
import vn.vnpt.oneiot.core.mongo.repositories.UserRepository;
import vn.vnpt.oneiot.core.mongo.services.DynamicRoleService;
import vn.vnpt.oneiot.core.mongo.services.PrivilegeService;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static vn.vnpt.oneiot.core.generic.Constants.*;

/**
 * Created by huyvv
 * Date: 05/02/2020
 * Time: 2:17 PM
 * for all issues, contact me: <EMAIL>
 **/
@Component
public class InitialDataLoader implements
        ApplicationListener<ContextRefreshedEvent> {

    @SuppressWarnings("unused")
    private static final Logger logger = LoggerFactory.getLogger(InitialDataLoader.class);

    private TenantRepository tenantRepository;
    private UserRepository userRepository;
    private TenantEntity defaultTenant = null;
    private UserEntity superAdmin;
    private PrivilegeRepository privilegeRepository;
    private DynamicRoleService dynamicRoleService;

    public TenantEntity getDefaultTenant() {
        return defaultTenant;
    }

    @Autowired
    public void setDynamicRoleService(DynamicRoleService dynamicRoleService) {
        this.dynamicRoleService = dynamicRoleService;
    }

    @Autowired
    public void setPrivilegeRepository(PrivilegeRepository privilegeRepository) {
        this.privilegeRepository = privilegeRepository;
    }

    @Autowired
    public void setUserRepository(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    @Autowired
    public void setTenantRepository(TenantRepository tenantRepository) {
        this.tenantRepository = tenantRepository;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        //create default tenant
        initRolePlatformAdmin();
        initRoleUser();
        initRoleTenantAdmin();
        createDefaultTenant();
    }

    private void createDefaultTenant(){
        defaultTenant = tenantRepository.findFirstByNameAndCreatedBy(Constants.DEFAULT_TENANT, SYSTEM);
        superAdmin = userRepository.findOneByEmailIgnoreCase(Constants.ADMIN_EMAIL);
        if (StringUtils.isBlank(defaultTenant.getAdminId())) {
            defaultTenant.setAdminId(superAdmin.getId());
            defaultTenant.setAdminName(superAdmin.getFullName());
            defaultTenant.setActive(vn.vnpt.oneiot.base.constants.Constants.EntityStatus.ACTIVE);
            tenantRepository.save(defaultTenant);
        }
        if (StringUtils.isBlank(superAdmin.getTenantId())) {
            superAdmin.setTenantId(defaultTenant.getCode());
            userRepository.save(superAdmin);
        }
        if (superAdmin.getRoleIds() == null || superAdmin.getRoleIds().isEmpty()) {
            RoleEntity roleEntity = dynamicRoleService.findByRoleIdAndHolder(Constants.ROLE_PLATFORM_ADMIN, superAdmin.getEmail());
            superAdmin.getRoleIds().add(roleEntity.getRoleId());
            userRepository.save(superAdmin);
        }
        logger.info(">>>>>> Init successfully <<<<<<");
    }


    private void initRoleUser() {
        RoleEntity roleEntity = dynamicRoleService.findByRoleIdAndHolder(Constants.DEFAULT_ROLE_USER, vn.vnpt.oneiot.common.constants.Constants.GUEST_REQUESTING_ENTITY);
        if (roleEntity.getPrivilegeIds() != null && !roleEntity.getPrivilegeIds().isEmpty()) return;
        List<PrivilegeEntity> allPv = privilegeRepository.findAllByNameIn(Arrays.asList(new String[] {
                "Tenant_View","AuditLog_View","Data_Mgt_Template_View","Data_Mgt_Dashboard_Create","Data_Mgt_Dashboard_View","Data_Mgt_Dashboard_Update","Data_Mgt_Dashboard_Delete","Data_Mgt_Widget_Create","Data_Mgt_Widget_View","Data_Mgt_Widget_Update","Data_Mgt_Widget_Delete","Ipe_View"
        }));
        for (PrivilegeEntity pv : allPv) roleEntity.getPrivilegeIds().add(pv.getId());
        dynamicRoleService.update(roleEntity);
    }


    private void initRolePlatformAdmin() {
        RoleEntity roleEntity = dynamicRoleService.findByRoleIdAndHolder(Constants.ROLE_PLATFORM_ADMIN, Constants.ADMIN_EMAIL);
        if (roleEntity.getPrivilegeIds() != null && !roleEntity.getPrivilegeIds().isEmpty()) return;
        List<PrivilegeEntity> allPv = privilegeRepository.findAll();
        for (PrivilegeEntity pv : allPv) roleEntity.getPrivilegeIds().add(pv.getId());
        dynamicRoleService.update(roleEntity);
    }

    private void initRoleTenantAdmin() {
        RoleEntity roleEntity = dynamicRoleService.findByRoleIdAndHolder(Constants.ROLE_TENANT_ADMIN, vn.vnpt.oneiot.common.constants.Constants.GUEST_REQUESTING_ENTITY);
        if (roleEntity.getPrivilegeIds() != null && !roleEntity.getPrivilegeIds().isEmpty()) return;
        List<PrivilegeEntity> allPv = privilegeRepository.findAllByNameIn(Arrays.asList(new String[] {
                "User_Create","User_View","User_Update","User_Delete","Role_Create","Role_View","Role_Update","Role_Delete","Privilege_View","Tenant_Create","Tenant_View","Tenant_Update","Tenant_Delete","AuditLog_View","App_Create","App_View","App_Update","App_Delete","Device_Create","Device_View","Device_Update","Device_Delete","Data_Mgt_Template_Create","Data_Mgt_Template_View","Data_Mgt_Template_Update","Data_Mgt_Template_Delete","Data_Mgt_Dashboard_Create","Data_Mgt_Dashboard_View","Data_Mgt_Dashboard_Update","Data_Mgt_Dashboard_Delete","Group_Create","Group_View","Group_Update","Group_Delete","Data_Mgt_Widget_Create","Data_Mgt_Widget_View","Data_Mgt_Widget_Update","Data_Mgt_Widget_Delete","Ipe_View","Ipe_Create","Ipe_Delete","Log_Delete"
        }));
        for (PrivilegeEntity pv : allPv) roleEntity.getPrivilegeIds().add(pv.getId());
        dynamicRoleService.update(roleEntity);
    }
}
