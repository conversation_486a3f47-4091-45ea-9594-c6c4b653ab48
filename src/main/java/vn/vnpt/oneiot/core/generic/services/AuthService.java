package vn.vnpt.oneiot.core.generic.services;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;
import io.jsonwebtoken.*;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.quartz.CronExpression;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.vnpt.oneiot.base.constants.AMQPConstant;
import vn.vnpt.oneiot.base.errors.ErrorKey;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.event.EventBus;
import vn.vnpt.oneiot.base.redis.ObjectCache;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.common.constants.*;
import vn.vnpt.oneiot.common.entities.*;
import vn.vnpt.oneiot.common.exceptions.PlatformException;
import vn.vnpt.oneiot.common.utils.Patterns;
import vn.vnpt.oneiot.core.CSEInitialize;
import vn.vnpt.oneiot.core.api.nbi.models.OperationWithToken;
import vn.vnpt.oneiot.core.api.nbi.models.TokenInfoDTO;
import vn.vnpt.oneiot.core.mongo.entity.*;
import vn.vnpt.oneiot.core.mongo.repositories.TenantRepository;
import vn.vnpt.oneiot.core.mongo.repositories.*;
import vn.vnpt.oneiot.core.mongo.services.*;
import vn.vnpt.oneiot.core.utils.*;

import javax.transaction.Transactional;
import java.math.BigInteger;
import java.text.ParseException;
import java.util.*;

import vn.vnpt.oneiot.core.mongo.repositories.MappingAcpIdsResourceRepository;

import static vn.vnpt.oneiot.common.constants.Constants.ADMIN_REQUESTING_ENTITY;
import static vn.vnpt.oneiot.common.constants.Constants.CSE_ID_URI;
import static vn.vnpt.oneiot.core.constants.Constants.AUTHEN_ENDPOINT_ERROR_KEY.*;
import static vn.vnpt.oneiot.core.generic.Constants.IDENTITY_SUB_TYPE.*;
import static vn.vnpt.oneiot.core.generic.Constants.IDENTITY_TYPE.*;

/**
 * Created by huyvv
 * Date: 20/01/2020
 * Time: 10:24 AM
 * for all issues, contact me: <EMAIL>
 **/
@Component
@Transactional
public class AuthService {
    private static Logger logger = LoggerFactory.getLogger(AuthService.class);
    private final String ROUTING_KEY_RATE_PLAN_ORDER = vn.vnpt.oneiot.core.constants.Constants.ROUTING_KEY_API_CHARGING_SERVICE_RATE_PLAN_ORDER;

    private UserService userService;
    private TenantRepository tenantRepository;
    private AccessControlPolicyRepository accessControlPolicyRepository;
    private TokenRepository tokenRepository;
    private AccessControlRuleRepository accessControlRuleRepository;
    private DynamicRoleRepository dynamicRoleRepository;
    private TokenService tokenService;
    private AccessControlOriginatorRepository accessControlOriginatorRepository;
    private MappingAcpIdsResourceRepository mappingAcpIdsResourceRepository;
    private AuthByPlatformAlgorithm algorithm;
    private ServiceSubscribedNodeRepository serviceSubscribedNodeRepository;
    private ServiceSubscribedAppRuleService serviceSubscribedAppRuleService;
    private CSEInitialize cseInitialize;
    private IdentityRepository identityRepository;
    private RelationRepository relationRepository;
    private RelationtypeRepository relationtypeRepository;
    private EventBus eventBus;
    @Autowired
    ObjectCache systemCache;
    // Cache key prefix for identity entities
    private static final String IDENTITY_CACHE_PREFIX = "identity:";
    private static final String TARGET_CACHE_PREFIX = "target:";
    private static final String RELATION_CACHE_PREFIX = "relation:";
    private static final int CACHE_TTL = 300000; // 5 minutes in seconds

    private final Gson gson = new Gson();

    @Autowired
    public void setEventBus(EventBus eventBus) {
        this.eventBus = eventBus;
    }

    @Autowired
    public void setRelationtypeRepository(RelationtypeRepository relationtypeRepository) {
        this.relationtypeRepository = relationtypeRepository;
    }

    @Autowired
    public void setRelationRepository(RelationRepository relationRepository) {
        this.relationRepository = relationRepository;
    }

    @Autowired
    public void setCseInitialize(CSEInitialize cseInitialize) {
        this.cseInitialize = cseInitialize;
    }

    @Autowired
    public void setServiceSubscribedAppRuleService(ServiceSubscribedAppRuleService serviceSubscribedAppRuleService) {
        this.serviceSubscribedAppRuleService = serviceSubscribedAppRuleService;
    }

    @Autowired
    public void setServiceSubscribedNodeRepository(ServiceSubscribedNodeRepository serviceSubscribedNodeRepository) {
        this.serviceSubscribedNodeRepository = serviceSubscribedNodeRepository;
    }

    @Autowired
    public void setMappingAcpIdsResourceRepository(MappingAcpIdsResourceRepository mappingAcpIdsResourceRepository) {
        this.mappingAcpIdsResourceRepository = mappingAcpIdsResourceRepository;
    }

    @Autowired
    public void setAuthByPlatformAlgorithm(AuthByPlatformAlgorithm algorithm) {
        this.algorithm = algorithm;
    }

    @Autowired
    public void setDynamicRoleRepository(DynamicRoleRepository dynamicRoleRepository) {
        this.dynamicRoleRepository = dynamicRoleRepository;
    }

    @Autowired
    public void setTokenService(TokenService tokenService) {
        this.tokenService = tokenService;
    }

    @Autowired
    public void setAccessControlRuleRepository(AccessControlRuleRepository accessControlRuleRepository) {
        this.accessControlRuleRepository = accessControlRuleRepository;
    }

    @Autowired
    public void setTokenRepository(TokenRepository tokenRepository) {
        this.tokenRepository = tokenRepository;
    }

    @Autowired
    public void setAccessControlPolicyRepository(AccessControlPolicyRepository accessControlPolicyRepository) {
        this.accessControlPolicyRepository = accessControlPolicyRepository;
    }

    @Autowired
    public void setTenantRepository(TenantRepository tenantRepository) {
        this.tenantRepository = tenantRepository;
    }

    @Autowired
    public void setUserService(UserService userService) {
        this.userService = userService;
    }

    @Autowired
    public void setIdentityRepository(IdentityRepository identityRepository) {
        this.identityRepository = identityRepository;
    }

    private Event handlerInvalidPermission(Event event) {
        return ErrorUtils.handleErrorResponse(ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue(), event, ErrorKey.AuthErrorKey.INVALID_TOKEN);
    }

    public Event validatePermission(Event event) {
        try {
            event.statusCode = ResponseStatusCode.OK.intValue();
            UserEntity userEntity = userService.current(event.payload);

            //nếu tenant của người này cũng đang trong tình trang active != 1 thì cũng invalid
            TenantEntity tenantEntity = tenantRepository.findFirstByCode(userEntity.getTenantId());
            if (tenantEntity == null || !Integer.valueOf(vn.vnpt.oneiot.base.constants.Constants.EntityStatus.ACTIVE).equals(tenantEntity.getActive())) {
                return handlerInvalidPermission(event);
            }

            event.payload = ObjectMapperUtil.toJsonString(userEntity);
            return event;
        } catch (JwtException | IllegalArgumentException e){
            return handlerInvalidPermission(event);
        }
    }

    @SuppressWarnings("Duplicates")
    //authen onem2m
    @WithSpan
    public Event processDynamicAuthorization(Event event) {
        DynamicAuthorizeEntity dae = gson.fromJson(event.payload, DynamicAuthorizeEntity.class);
        event.type = AMQPConstant.EVENTTYPE_RESPONSE;
        event.statusCode = ResponseStatusCode.OK.intValue();
        event.payload = null;
        //check 1 số case dịnh dạng dữ liệu
        if (dae == null || dae.getOriginatorIds() == null
                || dae.getOriginatorIds().isEmpty() || dae.getOriginatorIds().size() < 1) {
            event.statusCode = ResponseStatusCode.BAD_REQUEST.intValue();
            return event;
        }
        String originatorId = dae.getOriginatorIds().get(0);
        /** full access for cse-base and admin, HTTP API and Adapter must ensure request is from csebase or admin (local request) **/
        if (originatorId.equals(CSE_ID_URI) || originatorId.equals(ADMIN_REQUESTING_ENTITY)) {
            String targetId;
            if (dae.getCustodian() != null) targetId = dae.getCustodian();
            else targetId = dae.getTarget().getOriginatorId();
            // Sử dụng cache cho targetEntity
            IdentityEntity targetEntity = getIdentityEntityFromCacheOrDb(targetId);
            /** Validate Target must be existed in ONE IoT **/
            if (targetEntity == null) {
                event.statusCode = ResponseStatusCode.NOT_FOUND.intValue();
                event.errorText = "Originator or target is not existed";
                return event;
            }
            // Sử dụng cache cho TenantEntity để tăng tốc độ xử lý
            String tenantCacheKey = "tenant:" + targetEntity.getTenantId();
            TenantEntity tenantEntity = (TenantEntity) systemCache.get(tenantCacheKey, TenantEntity.class);

            // Nếu không có trong cache, truy vấn từ database
            if (tenantEntity == null) {
                tenantEntity = tenantRepository.findFirstByCode(targetEntity.getTenantId());
                // Lưu vào cache nếu tìm thấy tenant
                if (tenantEntity != null) {
                    systemCache.putMilisecond(tenantCacheKey, tenantEntity, CACHE_TTL, TenantEntity.class);
                }
            }

            // Kiểm tra trạng thái active của tenant
            if(tenantEntity == null || tenantEntity.getActive() != vn.vnpt.oneiot.base.constants.Constants.EntityStatus.ACTIVE){
                event.statusCode = ResponseStatusCode.BAD_REQUEST.intValue();
                event.errorText = TENANT_NOT_ACTIVE;
                return event;
            }
            // Sử dụng cache cho domain entity
            String domainCacheKey = IDENTITY_CACHE_PREFIX + "domain:" + targetEntity.getMainAppId();
            IdentityEntity domain = (IdentityEntity) systemCache.get(domainCacheKey, IdentityEntity.class);
            if (domain == null) {
                domain = identityRepository.findFirstByMainAppIdAndType(targetEntity.getMainAppId(), APP_DOMAIN_IDENTITY_TYPE);
                if (domain != null) {
                    systemCache.putMilisecond(domainCacheKey, domain, CACHE_TTL, IdentityEntity.class);
                }
            }
            TokenInfoDTO tokenInfoDTO = new TokenInfoDTO();
            tokenInfoDTO.tenantId = targetEntity.getTenantId();
            tokenInfoDTO.mainAppId = targetEntity.getMainAppId();
            tokenInfoDTO.tenantName = tenantEntity.getName();
            tokenInfoDTO.appDomainId = domain.getId();
            tokenInfoDTO.appDomainName = domain.getName();
            tokenInfoDTO.targetId = targetId;
            tokenInfoDTO.targetName = targetEntity.getName();
            tokenInfoDTO.targetType = targetEntity.getType();
            tokenInfoDTO.targetSubType = targetEntity.getSubType();
            tokenInfoDTO.orgType = TokenService.TOKEN_TYPE_USER; // CSE Base as role platform admin
            event.payload = gson.toJson(tokenInfoDTO);
            return event;
        }

        /** =============================================== AUTHORIZATION NEW STYLE BY RELATION =============================================== **/
        if (!cseInitialize.isCertificateRelease()) {
            String aeAnncId = null;
            if (originatorId.startsWith("/")) originatorId = originatorId.substring(1);
            if (originatorId.contains("/")) {
                String[] arr = originatorId.split("/");
                originatorId = arr[0];
                aeAnncId = arr[1];
            }
            TokenEntity tokenEntity;
            try {
                if (dae.getTokens() == null || dae.getTokens().isEmpty()) {
                    event.statusCode = ResponseStatusCode.BAD_REQUEST.intValue();
                    event.errorText = "Token must be present";
                    return event;
                }
                String jwtToken = dae.getTokens().get(0);
                String tokenId;
                /** Them doan nay de ho tro cac thiet bi token da bi het han nhung ko refresh duoc phai doi trong database **/
                try {
                    Jws<Claims> claimsJws = Jwts.parser().setSigningKey(vn.vnpt.oneiot.core.constants.Constants.JWT_SECRET).parseClaimsJws(jwtToken);
                    tokenId = claimsJws.getBody().getId();
                } catch (ExpiredJwtException e) {
                    if (e.getClaims() != null && StringUtils.isNotBlank(e.getClaims().getId())) tokenId = e.getClaims().getId();
                    else throw e;
                }

                // Sử dụng cache cho TokenEntity để tăng tốc độ xử lý
                String tokenCacheKey = "token:" + tokenId;
                tokenEntity = (TokenEntity) systemCache.get(tokenCacheKey, TokenEntity.class);

                // Nếu không có trong cache, truy vấn từ database
                if (tokenEntity == null) {
                    tokenEntity = tokenRepository.findFirstByTokenIdAndActive(tokenId, vn.vnpt.oneiot.base.constants.Constants.EntityStatus.ACTIVE);
                    // Lưu vào cache nếu tìm thấy token
                    if (tokenEntity != null) {
                        systemCache.putMilisecond(tokenCacheKey, tokenEntity, CACHE_TTL, String.class);

                    }
                }
                // token khong hop le, return loi
                if (!(tokenEntity != null && tokenEntity.getTokenObject().equals(jwtToken) && tokenEntity.getHolder().equals(originatorId) &&
                        !(tokenEntity.getNotAfter() != null && tokenEntity.getNotAfter() < System.currentTimeMillis()) &&
                        !(tokenEntity.getNotBefore() != null && tokenEntity.getNotBefore() > System.currentTimeMillis()))) {
                    event.statusCode = ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue();
                    return event;
                }
            } catch (ExpiredJwtException | UnsupportedJwtException | MalformedJwtException | SignatureException | IllegalArgumentException e) {
                event.statusCode = ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue();
                event.errorText = "Invalid token";
                return event;
            }

            TokenInfoDTO tokenInfoDTO = tokenService.getTokenInfo(tokenEntity);

            // Sử dụng cache cho TenantEntity để tăng tốc độ xử lý
            String tenantCacheKey = "tenant:" + tokenInfoDTO.tenantId;
            TenantEntity tenantEntity = (TenantEntity) systemCache.get(tenantCacheKey, TenantEntity.class);

            // Nếu không có trong cache, truy vấn từ database
            if (tenantEntity == null) {
                tenantEntity = tenantRepository.findFirstByCode(tokenInfoDTO.tenantId);
                // Lưu vào cache nếu tìm thấy tenant
                if (tenantEntity != null) {
                    systemCache.putMilisecond(tenantCacheKey, tenantEntity, CACHE_TTL, TenantEntity.class);
                }
            }
            if(tenantEntity.getActive() != vn.vnpt.oneiot.base.constants.Constants.EntityStatus.ACTIVE){
                event.statusCode = ResponseStatusCode.BAD_REQUEST.intValue();
                event.errorText = TENANT_NOT_ACTIVE;
                return event;
            }
            /** Comment tạm phần check gói cước để test
            if(!checkReachLimitApiOrTelemetry(dae, null, tokenInfoDTO, event)){
                return event;
            }
             **/
            /** Authorization when REGISTRATION for <AE>, <remoteCSE> */
            if (dae.getOperation().equals(Operation.CREATE) && (
                ResourceType.AE == dae.getCreatedResourceType().intValue() ||
                ResourceType.REMOTE_CSE == dae.getCreatedResourceType().intValue())) {
                tokenInfoDTO.targetId = tokenInfoDTO.holderId;
                tokenInfoDTO.targetType = tokenInfoDTO.orgType;
                tokenInfoDTO.targetSubType = tokenInfoDTO.orgSubType;
                tokenInfoDTO.targetName = tokenInfoDTO.holderName;
                event.payload = gson.toJson(tokenInfoDTO);
                // Main app registration
                if (originatorId.equals(tokenInfoDTO.mainAppId)) {
                    return event;
                }
                // User app or device registration
                else {
                    IdentityEntity mainAppEntity = identityRepository.findById(tokenInfoDTO.mainAppId).orElse(null);
                    /** Validate ACTIVE status: Main app active **/
                    if (mainAppEntity == null || mainAppEntity.getActive().intValue() != vn.vnpt.oneiot.base.constants.Constants.EntityStatus.ACTIVE) {
                        event.statusCode = ResponseStatusCode.ORIGINATOR_HAS_NOT_REGISTERED.intValue();
                        event.errorText = "Main app has not active yet";
                        return event;
                    }
                    return event;
                }
            }

            /** find target CSE or AE (application or device) to check authorization
             * custodian (optional): the CSE-ID or AE-ID is owner of target resource except for case CREATE <node>
             * custodian is custodian of <node> child resource will be created, because target is IN-CSE
             * target.originatorId: the creator of target resource
             */
            String targetId;
            if (aeAnncId != null) targetId = aeAnncId;
            else if (dae.getCustodian() != null) targetId = dae.getCustodian();
            else targetId = dae.getTarget().getOriginatorId();

            List<String> ids = new ArrayList<>();
            ids.add(originatorId);
            if (!ids.contains(targetId)) ids.add(targetId);
            if (!ids.contains(tokenInfoDTO.mainAppId)) ids.add(tokenInfoDTO.mainAppId);

            // Get identity for originator, main App of originator, target
            IdentityEntity origEntity = null, targetEntity = null, mainAppEntity = null;
            // Tối ưu truy vấn bằng cách kiểm tra cache trước
            List<IdentityEntity> identityEntities = new ArrayList<>();
            List<String> idsToFetch = new ArrayList<>();

            // Kiểm tra cache trước
            for (String id : ids) {
                String cacheKey = IDENTITY_CACHE_PREFIX + id;
                IdentityEntity entity = (IdentityEntity) systemCache.get(cacheKey, IdentityEntity.class);
                if (entity != null) {
                    identityEntities.add(entity);
                } else {
                    idsToFetch.add(id);
                }
            }

            // Chỉ truy vấn DB cho các ID chưa có trong cache
            if (!idsToFetch.isEmpty()) {
                List<IdentityEntity> fetchedEntities = identityRepository.findAllByIdIn(idsToFetch);
                if (fetchedEntities != null && !fetchedEntities.isEmpty()) {
                    // Lưu vào cache và thêm vào kết quả
                    for (IdentityEntity entity : fetchedEntities) {
                        systemCache.putMilisecond(IDENTITY_CACHE_PREFIX + entity.getId(), entity, CACHE_TTL, IdentityEntity.class);
                        identityEntities.add(entity);
                    }
                }
            }
            if (identityEntities == null || identityEntities.isEmpty()) {
                event.statusCode = ResponseStatusCode.NOT_FOUND.intValue();
                event.errorText = "Originator is not existed";
                return event;
            }
            for (IdentityEntity e : identityEntities) {
                if (e.getId().equals(originatorId)) origEntity = e;
                if (e.getId().equals(tokenInfoDTO.mainAppId)) mainAppEntity = e;
                if (e.getId().equals(targetId)) targetEntity = e;
            }

            /** Validate Target, Main app and originator must be existed in ONE IoT **/
            if (origEntity == null || mainAppEntity == null || targetEntity == null) {
                event.statusCode = ResponseStatusCode.NOT_FOUND.intValue();
                event.errorText = "Originator or target is not existed";
                return event;
            }
            /** Validate ACTIVE status: Main app and originator must be active **/
            if (origEntity.getActive().intValue() != vn.vnpt.oneiot.base.constants.Constants.EntityStatus.ACTIVE ||
                mainAppEntity.getActive().intValue() != vn.vnpt.oneiot.base.constants.Constants.EntityStatus.ACTIVE) {
                event.statusCode = ResponseStatusCode.ORIGINATOR_HAS_NOT_REGISTERED.intValue();
                event.errorText = "Originator has not active yet";
                return event;
            }
            // SET category neu ton tai
            if (StringUtils.isNotBlank(targetEntity.getCategory())) {
                tokenInfoDTO.category = targetEntity.getCategory();
            }
            if (StringUtils.isNotBlank(targetEntity.getId())) {
                tokenInfoDTO.targetId = targetEntity.getId();
            }
            if (StringUtils.isNotBlank(targetEntity.getName())) {
                tokenInfoDTO.targetName = targetEntity.getName();
            }
            if (!Objects.isNull(targetEntity.getType())) {
                tokenInfoDTO.targetType = targetEntity.getType();
            }
            if (!Objects.isNull(targetEntity.getSubType())) {
                tokenInfoDTO.targetSubType = targetEntity.getSubType();
            }
            event.payload = gson.toJson(tokenInfoDTO);
            /** Case 1: originator = target, full access **/
            if (origEntity == targetEntity) {
                return event;
            }

            /** Case 2: originator is main app, full access for other app, device in the same domain */
            if (origEntity == mainAppEntity && targetEntity.getMainAppId().equals(originatorId)) {
                return event;
            }

            /** Case 3: originator is device, target is mainApp to create device status */
            if (targetEntity == mainAppEntity && origEntity.getType().intValue() == DEVICE_IDENTITY_TYPE &&
                dae.getOperation().equals(Operation.CREATE) && dae.getCreatedResourceType().intValue() == ResourceType.CONTENT_INSTANCE)
            {
                return event;
            }

            /** Case 4: check grant access by relation between app and app/device/group, or gateway and sub-device **/
            List<RelationEntity> relationEntities;
            boolean onlyCreateCin = false;
            if ((origEntity.getType().intValue() == DEVICE_IDENTITY_TYPE && targetEntity.getType().intValue() == APP_IDENTITY_TYPE) ||
                (origEntity.getType().intValue() == DEVICE_IDENTITY_TYPE && origEntity.getSubType().intValue() == SUB_DEVICE_TYPE &&
                targetEntity.getType().intValue() == DEVICE_IDENTITY_TYPE && targetEntity.getSubType().intValue() == GATE_WAY_TYPE)) {
                // Sử dụng cache cho relation entities
                String relationCacheKey = RELATION_CACHE_PREFIX + targetId + ":" + originatorId;

                // Lấy dữ liệu từ cache dưới dạng mảng JSON
                String cachedData = (String) systemCache.get(relationCacheKey, String.class);

                if (cachedData != null) {
                    // Chuyển đổi từ JSON string sang List<RelationEntity>
                    Type listType = new TypeToken<List<RelationEntity>>(){}.getType();
                    relationEntities = new Gson().fromJson(cachedData, listType);
                } else {
                    // Nếu không có trong cache, truy vấn từ database
                    relationEntities = relationRepository.findAllByOriginatorIdAndTargetId(targetId, originatorId);

                    // Lưu vào cache dưới dạng JSON string để giữ nguyên kiểu dữ liệu
                    if (relationEntities != null && !relationEntities.isEmpty()) {
                        String jsonData = new Gson().toJson(relationEntities);
                        systemCache.putMilisecond(relationCacheKey, jsonData, CACHE_TTL, String.class);
                    }
                }
                onlyCreateCin = true;
            } else {
                // Sử dụng cache cho relation entities
                String relationCacheKey = RELATION_CACHE_PREFIX + originatorId + ":" + targetId;

                // Lấy dữ liệu từ cache dưới dạng mảng JSON
                String cachedData = (String) systemCache.get(relationCacheKey, String.class);

                if (cachedData != null) {
                    // Chuyển đổi từ JSON string sang List<RelationEntity>
                    Type listType = new TypeToken<List<RelationEntity>>(){}.getType();
                    relationEntities = new Gson().fromJson(cachedData, listType);
                } else {
                    // Nếu không có trong cache, truy vấn từ database
                    relationEntities = relationRepository.findAllByOriginatorIdAndTargetId(originatorId, targetId);

                    // Lưu vào cache dưới dạng JSON string để giữ nguyên kiểu dữ liệu
                    if (relationEntities != null && !relationEntities.isEmpty()) {
                        String jsonData = new Gson().toJson(relationEntities);
                        systemCache.putMilisecond(relationCacheKey, jsonData, CACHE_TTL, String.class);
                    }
                }
            }
            if (relationEntities == null || relationEntities.isEmpty()) {
                event.statusCode = ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue();
                event.errorText = "Originator has no privilege to access";
                return event;
            }
            for (RelationEntity relationEntity : relationEntities) {
                if ((!onlyCreateCin || (dae.getOperation().equals(Operation.CREATE) && dae.getCreatedResourceType().equals(ResourceType.CONTENT_INSTANCE))) &&
                    (relationEntity.getRelationType().equals(vn.vnpt.oneiot.core.generic.Constants.RELATION_TYPE.PARENT) ||
                    relationEntity.getRelationType().equals(vn.vnpt.oneiot.core.generic.Constants.RELATION_TYPE.OWNER) ||
                    relationEntity.getRelationType().equals(vn.vnpt.oneiot.core.generic.Constants.RELATION_TYPE.COMMON))) {
                    return event;
                }
            }

            // Default no access
            event.statusCode = ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue();
            event.errorText = "Originator has no privilege to access";
            return event;
        }


        /** =============================================== AUTHORIZATION OLD STYLE =============================================== **/

        /** Authorization by m2m service subscription profile for create <AE> **/
        if ((dae.getOperation().equals(Operation.CREATE) &&
            (dae.getCreatedResourceType().intValue() == ResourceType.AE ||
            dae.getCreatedResourceType().intValue() == ResourceType.AE_ANNC)) ||
            (dae.getOperation().equals(Operation.UPDATE) &&
            dae.getTarget().getResourceType() == ResourceType.AE_ANNC) &&
            dae.getAppId() != null) {
            String cseId = null;
            String aeIdstem = null;
            if (originatorId == null || originatorId.isEmpty()) {
                /** Get registrar cseid **/
                if (dae.getTarget().getTargetId().startsWith("/")) {
                    cseId = "/" + dae.getTarget().getTargetId().substring(1).split("/")[0];
                } else if (dae.getTarget().getTargetId().startsWith("//")) {
                    cseId = "/" + dae.getTarget().getTargetId().substring(2).split("/")[1];
                }
            }
            /** Authorization by m2mServiceSubscriptionProfile if originator (aeid stem) is "S" or "C" **/
            else if (originatorId.startsWith("S") || originatorId.startsWith("C") ||
                originatorId.startsWith("/S") || originatorId.startsWith("//" + Constants.M2M_SP_ID + "/S") ||
                originatorId.startsWith(Patterns.SP_RELATIVE_PREFIX + "C") || originatorId.startsWith(Patterns.ABS_PREFIX + "C")) {
                /** Get registrar cseid **/
                if (dae.getTarget().getTargetId().startsWith("/")) {
                    cseId = "/" + dae.getTarget().getTargetId().substring(1).split("/")[0];
                } else if (dae.getTarget().getTargetId().startsWith("//")) {
                    cseId = "/" + dae.getTarget().getTargetId().substring(2).split("/")[1];
                }

                String[] arr = originatorId.split("/");
                aeIdstem = arr[arr.length - 1];
            } else if (originatorId.startsWith("//") && originatorId.endsWith("/S")) {
                String[] arr = originatorId.substring(2).split("/");
                cseId = "/" + arr[1];
                aeIdstem = arr[2];
            } else if (originatorId.startsWith("/") && originatorId.endsWith("/S")) {
                String arr[] = originatorId.substring(1).split("/");
                cseId = "/" + arr[0];
                aeIdstem = arr[1];
            }

            if (cseId != null) {

                /** Check the existence of ServiceSubscribedNode of Registrar CSE **/
                // Sử dụng cache cho ServiceSubscribedNodeEntity
                String ssneCacheKey = "ssne:" + cseId;
                ServiceSubscribedNodeEntity ssne = (ServiceSubscribedNodeEntity) systemCache.get(ssneCacheKey, ServiceSubscribedNodeEntity.class);
                if (ssne == null) {
                    ssne = serviceSubscribedNodeRepository.findFirstByCseidEquals(cseId);
                    if (ssne != null) {
                        systemCache.putMilisecond(ssneCacheKey, ssne, CACHE_TTL, String.class);
                    }
                }
                if (ssne == null) {
                    event.statusCode = ResponseStatusCode.SECURITY_ASSOCIATION_REQUIRED.intValue();
                    return event;
                }
                try {
                    List<ServiceSubscribedAppRuleEntity> asar = serviceSubscribedAppRuleService.matched(dae.getCredentialId(), dae.getAppId(), aeIdstem, ssne.getResourceID());
                    if (asar != null) {
                        String allowedAes = "";
                        for (ServiceSubscribedAppRuleEntity item: asar)
                            allowedAes = allowedAes + "|" + item.getAllowedAEs().getPattern();
                        event.payload = gson.toJson(allowedAes.substring(1));
                        return event;
                    } else if (cseInitialize.isCertificateRelease()) {
                        event.statusCode = ResponseStatusCode.SECURITY_ASSOCIATION_REQUIRED.intValue();
                        return event;
                    }
                } catch (Exception e) {
                    logger.error("error", e);
                    event.statusCode = ResponseStatusCode.SECURITY_ASSOCIATION_REQUIRED.intValue();
                    return event;
                }
            }
        }

        /** Authorization by Access Control Policy for other resource **/
        //Filter originator id and role id is valid that match with originator of request
        if (dae.getOriginatorIds().size() > 1) {
            List<String> removeRole = new ArrayList<>();
            for (int i = 1; i < dae.getOriginatorIds().size(); i++) {
                String roleId = dae.getOriginatorIds().get(i);
                // Sử dụng cache cho RoleEntity
                String roleCacheKey = "role:" + roleId;
                RoleEntity roleEntity = (RoleEntity) systemCache.get(roleCacheKey, RoleEntity.class);
                if (roleEntity == null) {
                    roleEntity = dynamicRoleRepository.findFirstByRoleIdIs(roleId);
                    if (roleEntity != null) {
                        systemCache.putMilisecond(roleCacheKey, roleEntity, CACHE_TTL, RoleEntity.class);
                    }
                }
                if (roleEntity == null || !roleEntity.getHolder().equals(dae.getOriginatorIds().get(0))) {
                    removeRole.add(roleId);
                }
            }
            if (removeRole.size() > 0) dae.getOriginatorIds().removeAll(removeRole);
        }

        // Create list of operator need to authorization
        List<BigInteger> operators = new ArrayList<>();
        operators.add(dae.getOperation());

        /** Check if the Originator has privileges for retrieving the subscribed-to resource when create <Subscription> **/
        if (dae.getOperation().equals(Operation.CREATE) && dae.getCreatedResourceType().intValue() == ResourceType.SUBSCRIPTION) {
            operators.add(Operation.RETRIEVE);
        }

        /** Super admin is always permitted **/
        if (dae.getOriginatorIds().contains(Constants.ADMIN_REQUESTING_ENTITY)) {
            return event;
        }

        /** Authorization for create <AccessControlPolicy> request **/
        if (dae.getTarget().getResourceType() == ResourceType.ACCESS_CONTROL_POLICY && !dae.getOperation().equals(Operation.CREATE)) {
            try {
                AccessControlPolicyEntity acpEntity = accessControlPolicyRepository.findById(dae.getTarget().getTargetId()).get();
                if (!authorizeListAccessControlRule(acpEntity.getSelfPrivileges(), dae.getOriginatorIds(), operators, dae.getOriginatorIp(), dae.getReceivedTimestamp())) {
                    event.statusCode = ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue();
                }
                return event;
            } catch (Exception e) {
                event.statusCode = ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue();
                return event;
            }
        }

        //Get acp list
        MappingAcpIdsResourceEntity mappingAcpIdsResourceEntity;
        if (ResourceUtils.supportAccessControlPolicyIds(dae.getTarget().getResourceType()))
            mappingAcpIdsResourceEntity = mappingAcpIdsResourceRepository.findFirstByResourceIdIs(dae.getTarget().getTargetId());
        else
            mappingAcpIdsResourceEntity = mappingAcpIdsResourceRepository.findFirstByResourceIdIs(dae.getTarget().getParentId());
        List<AccessControlPolicyEntity> acpList = null;

        if (mappingAcpIdsResourceEntity != null && mappingAcpIdsResourceEntity.getAccessControlPolicyIds().size() > 0) {
            // Sử dụng cache cho danh sách AccessControlPolicyEntity
            String acpListCacheKey = "acpList:" + String.join(",", mappingAcpIdsResourceEntity.getAccessControlPolicyIds());
            acpList = (List<AccessControlPolicyEntity>) systemCache.get(acpListCacheKey, List.class);
            if (acpList == null) {
                acpList = accessControlPolicyRepository.findAllByResourceIDIn(mappingAcpIdsResourceEntity.getAccessControlPolicyIds());
                if (acpList != null && !acpList.isEmpty()) {
                    systemCache.putMilisecond(acpListCacheKey, acpList, CACHE_TTL, List.class);
                }
            }
        }

        if (acpList != null) {
            for (AccessControlPolicyEntity acp :
                    acpList) {
                if (!dae.isUpdateAcpIds() && !acp.getPrivileges().isEmpty()) {
                    // Phan quyen ok
                    if (Boolean.TRUE.equals(authorizeListAccessControlRule(acp.getPrivileges(), dae.getOriginatorIds(), operators, dae.getOriginatorIp(), dae.getReceivedTimestamp()))) {
                        return event;
                    }
                } else if (dae.isUpdateAcpIds() && !acp.getSelfPrivileges().isEmpty()) {
                    if (Boolean.TRUE.equals(authorizeListAccessControlRule(acp.getSelfPrivileges(), dae.getOriginatorIds(), operators, dae.getOriginatorIp(), dae.getReceivedTimestamp()))) {
                        return event;
                    }
                }
            }
        }

        //3. Authorization bang token
        if (dae.getTokens() != null && dae.getTokens().size() > 0) {
            for (String jwtToken : dae.getTokens()) {
                try {
                    Jws<Claims> claimsJws = Jwts.parser().setSigningKey(vn.vnpt.oneiot.core.constants.Constants.JWT_SECRET).parseClaimsJws(jwtToken);
                    String tokenId = claimsJws.getBody().getId();
                    // Sử dụng cache cho TokenEntity
                    String tokenCacheKey = "token:" + tokenId;
                    TokenEntity tokenEntity = (TokenEntity) systemCache.get(tokenCacheKey, TokenEntity.class);
                    if (tokenEntity == null) {
                        tokenEntity = tokenRepository.findFirstByTokenIdAndActive(tokenId, vn.vnpt.oneiot.base.constants.Constants.EntityStatus.ACTIVE);
                        if (tokenEntity != null) {
                            systemCache.putMilisecond(tokenCacheKey, tokenEntity, CACHE_TTL, TokenEntity.class);
                        }
                    }
                    if (tokenEntity != null) event.payload = gson.toJson(tokenEntity);
                    // token khong hop le, return loi
                    if (!(tokenEntity != null && tokenEntity.getTokenObject().equals(jwtToken) && tokenEntity.getHolder().equals(originatorId) &&
                            !(tokenEntity.getNotAfter() != null && tokenEntity.getNotAfter() < System.currentTimeMillis()) &&
                            !(tokenEntity.getNotBefore() != null && tokenEntity.getNotBefore() > System.currentTimeMillis()) &&
                            tokenEntity.getPermissions() != null)) {
                        event.statusCode = ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue();
                        return event;
                    }
                    if (tokenEntity.getPermissions() != null && tokenEntity.getPermissions().size() > 0) {
                        for (PermissionEntity item : tokenEntity.getPermissions()) {
                            // target khong khop resource trong permission, next sang permission tiep theo
                            if (item.getResourceIDs() != null && !item.getResourceIDs().contains(dae.getTarget().getTargetId())) {
                                break;
                            }
                            if (item.getRoleIDs() != null && item.getRoleIDs().size() > 0 && acpList != null) {
                                for (AccessControlPolicyEntity acp :
                                        acpList) {
                                    if (!dae.isUpdateAcpIds() && !acp.getPrivileges().isEmpty()) {
                                        // phan quyen ok
                                        if (Boolean.TRUE.equals(authorizeListAccessControlRule(acp.getPrivileges(), item.getRoleIDs(), operators, dae.getOriginatorIp(), dae.getReceivedTimestamp()))) {
                                            return event;
                                        }
                                    } else if (dae.isUpdateAcpIds() && !acp.getSelfPrivileges().isEmpty()) {
                                        if (Boolean.TRUE.equals(authorizeListAccessControlRule(acp.getSelfPrivileges(), item.getRoleIDs(), operators, dae.getOriginatorIp(), dae.getReceivedTimestamp()))) {
                                            return event;
                                        }
                                    }
                                }
                            }

                            if (item.getPrivileges() != null && !item.getPrivileges().isEmpty() &&
                                    item.getResourceIDs() != null && !item.getResourceIDs().isEmpty()) {
                                ArrayList<String> tokenOriId = new ArrayList<>();
                                tokenOriId.addAll(dae.getOriginatorIds());
                                if (item.getRoleIDs() != null) {
                                    for (String tokenRoleId : item.getRoleIDs()) {
                                        if (!dae.getOriginatorIds().contains(tokenRoleId))
                                            tokenOriId.add(tokenRoleId);
                                    }
                                }
                                // phan quyen ok
                                if (Boolean.TRUE.equals(authorizeListAccessControlRule(item.getPrivileges(), tokenOriId, operators, dae.getOriginatorIp(), dae.getReceivedTimestamp()))) {
                                    return event;
                                }
                            }
                        }
                    }
                } catch (ExpiredJwtException e) {
                    e.printStackTrace();
                } catch (UnsupportedJwtException e) {
                    e.printStackTrace();
                } catch (MalformedJwtException e) {
                    e.printStackTrace();
                } catch (SignatureException e) {
                    e.printStackTrace();
                } catch (IllegalArgumentException e) {
                    e.printStackTrace();
                }
            }
        }

        if (cseInitialize.isCertificateRelease()) {
            if (acpList != null && !acpList.isEmpty()) {
                for (AccessControlPolicyEntity acpEntity : acpList) {
                    if (!acpEntity.getResourceID().equals(CSEInitialize.acpAdminId) && !acpEntity.getResourceID().equals(CSEInitialize.acpGuestId)
                            && acpEntity.getPrivileges() != null && acpEntity.getPrivileges().size() > 0) {
                        event.statusCode = ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue();
                        return event;
                    }
                }
            }
        }

        // Default access privilege is unlimited for creator of target resource
        logger.debug("No acp associate with target resource " + dae.getTarget().getTargetId());
        if (isOriginatorEqual(originatorId, dae.getTarget().getOriginatorId())) {
            return event;
        }

        event.statusCode = ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue();
        return event;
    }

    /**
     * Helper method to get IdentityEntity from cache or database
     */
    private IdentityEntity getIdentityEntityFromCacheOrDb(String id) {
        if (id == null) return null;

        String cacheKey = IDENTITY_CACHE_PREFIX + id;
        IdentityEntity entity = (IdentityEntity) systemCache.get(cacheKey, IdentityEntity.class);

        if (entity == null) {
            entity = identityRepository.findById(id).orElse(null);
            if (entity != null) {
                systemCache.putMilisecond(cacheKey, entity, CACHE_TTL, IdentityEntity.class);

            }
        }

        return entity;
    }

    private boolean checkReachLimitApiOrTelemetry(DynamicAuthorizeEntity dae, TenantEntity tenantEntity, TokenInfoDTO tokenEntity, Event event) {
        try {
            if(tokenEntity != null && tenantEntity == null){
                // Sử dụng cache cho TenantEntity để tăng tốc độ xử lý
                String tenantCacheKey = "tenant:" + tokenEntity.tenantId;
                tenantEntity = (TenantEntity) systemCache.get(tenantCacheKey, TenantEntity.class);

                // Nếu không có trong cache, truy vấn từ database
                if (tenantEntity == null) {
                    tenantEntity = tenantRepository.findFirstByCode(tokenEntity.tenantId);
                    // Lưu vào cache nếu tìm thấy tenant
                    if (tenantEntity != null) {
                        systemCache.putMilisecond(tenantCacheKey, tenantEntity, CACHE_TTL, TenantEntity.class);
                    }
                }
            }
            if (dae.getTarget() != null && dae.getTarget().getResourceType() == ResourceType.CONTAINER) {
//                if(tenantEntity.getApiFlag() == null || tenantEntity.getTelemetryFlag() == null || tenantEntity.getApiFlag() == 0 || tenantEntity.getTelemetryFlag() == 0){
//                    event.statusCode = ResponseStatusCode.OPERATION_NOT_ALLOWED.intValue();
//                    event.errorText = vn.vnpt.oneiot.core.constants.Constants.ResultMessage.RATING_PLAN_DISABLE_FLAG;
//                    return false;
//                }
                Event eventRequest = new Event();
                eventRequest.id = UUID.randomUUID().toString();
                eventRequest.type = AMQPConstant.EVENTTYPE_REQUEST;
                eventRequest.method = vn.vnpt.oneiot.base.constants.Constants.Method.CHECK_USAGE_RATING_PLAN;
                if(dae.getTarget().getHuri().endsWith(vn.vnpt.oneiot.core.constants.Constants.CONTAINER_COMMAND_NAME)){
                    eventRequest.payload = String.format("%s/%s",tenantEntity.getCode(),"api");
                }else{
                    eventRequest.payload = String.format("%s/%s",tenantEntity.getCode(),"telemetry");
                }

                MessageProperties properties = new MessageProperties();
                properties.setReplyTo(AMQPConstant.ROUTING_KEY_INTERNAL_AUTHORIZATION);
                eventRequest = eventBus.publishAndReceiveSynch(AMQPConstant.getExchangeFromRoutingKey(ROUTING_KEY_RATE_PLAN_ORDER), ROUTING_KEY_RATE_PLAN_ORDER, eventRequest, properties, 30000);
                if(eventRequest.statusCode != ResponseStatusCode.OK.intValue()){
                    event.statusCode = ResponseStatusCode.OPERATION_NOT_ALLOWED.intValue();
                    if(eventRequest.statusCode.intValue() == ResponseStatusCode.NOT_FOUND.intValue()){
                        event.errorText = vn.vnpt.oneiot.core.constants.Constants.ResultMessage.NOT_REGISTER_RATING_PLAN;
                    }else if(eventRequest.statusCode.intValue() == ResponseStatusCode.NOT_ACCEPTABLE.intValue()){
                        event.errorText = vn.vnpt.oneiot.core.constants.Constants.ResultMessage.RATING_PLAN_EXPIRED;
                    }else if(eventRequest.statusCode.intValue() == ResponseStatusCode.CONTENTS_UNACCEPTABLE.intValue()){
                        event.errorText = vn.vnpt.oneiot.core.constants.Constants.ResultMessage.DATA_VOLUME_REACH_LIMIT;
                    }else{
                        if(eventRequest.payload.endsWith("api")){
                            event.errorText = vn.vnpt.oneiot.core.constants.Constants.ResultMessage.API_REACH_LIMIT;
                        }else{
                            event.errorText = vn.vnpt.oneiot.core.constants.Constants.ResultMessage.TELEMETRY_REACH_LIMIT;
                        }
                    }
                    return false;
                }
            }
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            event.statusCode = ResponseStatusCode.BAD_REQUEST.intValue();
            event.errorText = vn.vnpt.oneiot.core.constants.Constants.ResultMessage.DATA_CHECK_ERROR;
            return false;
        }
        return true;
    }

    /**
     * authorize with List AccessControlRule
     **/
    private Boolean authorizeListAccessControlRule(List<AccessControlRuleEntity> acrs, List<String> originatorIds, List<BigInteger> operation, String ipRequest, BigInteger receivedTimestamp) {
        for (AccessControlRuleEntity acr : acrs) {
            Boolean permit = authorizeAccessControlRule(acr, originatorIds, operation, ipRequest, receivedTimestamp);
            if (Boolean.TRUE.equals(permit)) return permit;
        }
        return Boolean.FALSE;
    }

    /**
     * authorize with AccessControlRule
     * 1. operator (1-Create, 2-Retrieve, 4-Update, 8-Delete, 16-Notify, 32-Discovery, 63-All)
     * 2. originators
     * 3. contexts (ignore)
     * 4. access-control-object-details (ignore)
     **/
    private Boolean authorizeAccessControlRule(AccessControlRuleEntity acr, List<String> originatorIds, List<BigInteger> operation, String ipRequest, BigInteger receivedTimestamp) {
        return authorizeOperators(acr, operation) && authorizeOriginators(acr.getAccessControlOriginators(), originatorIds)
                && authorizeContext(acr.getAccessControlContexts(), ipRequest, receivedTimestamp);
    }

    private Boolean authorizeOriginators(List<AccessControlOriginatorEntity> originatorAcrList, List<String> originatorRequests) {
        if (originatorAcrList != null && originatorRequests != null) {
            for (String originatorId : originatorRequests) {
                if (authorizeOriginator(originatorAcrList, originatorId)) return true;
            }
        }
        return false;
    }

    private Boolean authorizeContext(List<AccessControlContextEntity> accessControlContextEntityList, String ipRequest, BigInteger receivedTimestamp) {
        if (accessControlContextEntityList != null && accessControlContextEntityList.size() > 0) {
            for (AccessControlContextEntity acco : accessControlContextEntityList) {
                if (authorizeTimeWindow(acco.getCronExpressionACTW(), receivedTimestamp) &&
                authorizeIp(acco.getIpv4Addresses(), acco.getIpv6Addresses(), ipRequest)) return true;
            }
            return false;
        } else {
            return true;
        }
    }

    private Boolean authorizeIp(List<String> ipv4, List<String> ipv6, String ipRequest) {
        if (ipRequest.contains(":")) {
            // ipv6
            return matchingSubnetListAndIP(ipv6, ipRequest);
        } else {
            // ipv4
            return matchingSubnetListAndIP(ipv4, ipRequest);
        }
    }

    private boolean matchingSubnetListAndIP(List<String> subnets, String ipRequest) {
        if (subnets != null && subnets.size() > 0) {
            for (String subnet : subnets) {
                if (new IpAddressMatcher(subnet).matches(ipRequest)) return true;
            }
            return false;
        } else {
            return true;
        }
    }

    private Boolean authorizeTimeWindow(List<String> timeWindow, BigInteger receivedTimestamp) {
        if (timeWindow != null && timeWindow.size() > 0) {
            for (String tw : timeWindow) {
                try {
                    CronExpression exp = new CronExpression(tw);
                    if (exp.isSatisfiedBy(new Date(receivedTimestamp.longValue()))) return true;
                } catch (ParseException e) {
                    e.printStackTrace();
                    throw new PlatformException("Cannot check timestamp " + receivedTimestamp + " with schedule entry " + tw, ResponseStatusCode.INTERNAL_SERVER_ERROR);
                }
            }
            return false;
        } else {
            return true;
        }
    }

    private Boolean authorizeOperator(AccessControlRuleEntity acr, BigInteger operatorRequest) {
        if (operatorRequest.equals(Operation.CREATE) && acr.isCreate()) return true;
        if (operatorRequest.equals(Operation.RETRIEVE) && acr.isRetrieve()) return true;
        if (operatorRequest.equals(Operation.UPDATE) && acr.isUpdate()) return true;
        if (operatorRequest.equals(Operation.DELETE) && acr.isDelete()) return true;
        if (operatorRequest.equals(Operation.NOTIFY) && acr.isNotify()) return true;
        if (operatorRequest.equals(Operation.DISCOVERY) && acr.isDiscovery()) return true;
        return false;
    }


    private Boolean authorizeOperators(AccessControlRuleEntity acr, List<BigInteger> operatorRequests) {
        if (operatorRequests == null || operatorRequests.isEmpty()) return false;
        boolean result = true;
        for (BigInteger operator : operatorRequests) {
            result = result & authorizeOperator(acr, operator);
            if (!result) return false;
        }
        return true;
    }

    private Boolean authorizeOriginator(List<AccessControlOriginatorEntity> originatorAcrList, String originatorRequest) {
        for (AccessControlOriginatorEntity item : originatorAcrList) {
            if (item.getOriginatorID().equals(Constants.GUEST_REQUESTING_ENTITY) ||
                    isOriginatorEqual(item.getOriginatorID(), originatorRequest) ||
            originatorRequest.matches(item.getOriginatorID().replace("*", ".*")))
                return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private List<AccessControlRuleEntity> getAcrListByUriMapper(String uri) {
        List<AccessControlPolicyEntity> acpList = new ArrayList<>();
        List<AccessControlRuleEntity> acrList = new ArrayList<>();

//        UriMapperEntity uriMapperEntity = uriMapperRepository.findFirstByNonHierarchicalUri(uri);
//        if(uriMapperEntity == null) return acrList;
//
//        if(matchResource(AE_PATTERN, uriMapperEntity.getHierarchicalUri())){
//            AeEntity aeEntity = aeRepository.findFirstByResourceID(uriMapperEntity.getNonHierarchicalUri());
//            if(aeEntity != null){
//                acpList = aeEntity.getAccessControlPolicies();
//            }
//        }
//        //TODO: else if other resource

        for (AccessControlPolicyEntity acp : acpList) {
            acrList.addAll(acp.getSelfPrivileges());
        }

        return acrList;
    }

    //authen api
    @WithSpan
    public Event processAuthorByPlatform(Event event) {
        OperationWithToken operationWithToken = ObjectMapperUtil.objectMapper(event.payload, OperationWithToken.class);
        String token = operationWithToken.token;
        List<String> operations = operationWithToken.operations;
        if (Strings.isEmpty(token)) {
            return ErrorUtils.handleErrorResponse(ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue(), event, MISSING_ACCESS_TOKEN);
        }
        if (operations == null || operations.isEmpty()) {
            return ErrorUtils.handleErrorResponse(ResponseStatusCode.BAD_REQUEST.intValue(), event, OPERATION_NOT_ALLOW);
        }
        TokenEntity tokenEntity;
        if ((tokenEntity = algorithm.isValidToken(token)) == null) {
            return ErrorUtils.handleErrorResponse(ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue(), event, TOKEN_EXPIRED);
        }
        TokenInfoDTO tokenInfoDTO = tokenService.getTokenInfo(tokenEntity);
        /** check tenant, user must be active **/
        UserEntity userEntity = userService.get(tokenInfoDTO.userId);
        TenantEntity tenantEntity;
        if(userEntity.getActive() != vn.vnpt.oneiot.base.constants.Constants.EntityStatus.ACTIVE){
            return ErrorUtils.handleErrorResponse(ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue(), event, USER_NOT_ACTIVE);
        }else{
            tenantEntity = tenantRepository.findFirstByCode(userEntity.getTenantId());
            if(tenantEntity == null || tenantEntity.getActive() != vn.vnpt.oneiot.base.constants.Constants.EntityStatus.ACTIVE){
                return ErrorUtils.handleErrorResponse(ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue(), event, TENANT_NOT_ACTIVE);
            }
        }
        /** Check mainApp and user app must be active **/
        if (tokenInfoDTO.orgType != null && tokenInfoDTO.orgType != TokenService.TOKEN_TYPE_USER) {
            if (tokenInfoDTO.holderId.equals(tokenInfoDTO.mainAppId)) {
                IdentityEntity identityEntity = identityRepository.findById(tokenInfoDTO.holderId).orElse(null);
                if (identityEntity != null && identityEntity.getActive() != vn.vnpt.oneiot.base.constants.Constants.EntityStatus.ACTIVE) {
                    return ErrorUtils.handleErrorResponse(ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue(), event, APPLICATION_NOT_ACTIVE);
                }
            } else {
                List<IdentityEntity> identityEntities = identityRepository.findAllByIdIn(Arrays.asList(new String[] {tokenInfoDTO.holderId, tokenInfoDTO.mainAppId}));
                if (identityEntities == null || identityEntities.size() != 2) {
                    return ErrorUtils.handleErrorResponse(ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue(), event, APPLICATION_NOT_FOUND);
                }
                if (identityEntities.get(0).getActive() != vn.vnpt.oneiot.base.constants.Constants.EntityStatus.ACTIVE ||
                        identityEntities.get(1).getActive() != vn.vnpt.oneiot.base.constants.Constants.EntityStatus.ACTIVE) {
                    return ErrorUtils.handleErrorResponse(ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue(), event, APPLICATION_NOT_ACTIVE);
                }
            }
        }

        List<String> validOperation = algorithm.hasOperationPrivilege(tokenEntity, operations, operationWithToken.targetIds, tokenInfoDTO);
        if (validOperation == null || validOperation.isEmpty()) {
            return ErrorUtils.handleErrorResponse(ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue(), event, OPERATION_NOT_ALLOW);
        }

        /** Coment tạm phần check gói cước để test
        if(!tenantEntity.getName().equals(vn.vnpt.oneiot.core.constants.Constants.DEFAULT_TENANT)){
            if(operations.contains(vn.vnpt.oneiot.core.constants.Constants.APP_CREATE)){
                if(tenantEntity.getStatusRatingPlan() == null || tenantEntity.getFreeUserAppQuota() == null
                    || !(tenantEntity.getStatusRatingPlan().equals(RatePlanOrderStatus.ACTIVED) || tenantEntity.getStatusRatingPlan().equals(RatePlanOrderStatus.EXPIRED))
                ){
                    return ErrorUtils.handleErrorResponse(ResponseStatusCode.OPERATION_NOT_ALLOWED.intValue(), event, NOT_REGISTER_RATING_PLAN);
                }
                Long countUserApp = identityRepository.countByTenantIdAndTypeAndSubTypeAndActiveNotIn(tenantEntity.getCode(), APP_IDENTITY_TYPE, USER_APP_TYPE, Arrays.asList(new Integer[]{vn.vnpt.oneiot.base.constants.Constants.EntityStatus.DELETED}));
                if(countUserApp >= tenantEntity.getFreeUserAppQuota() && tenantEntity.getFreeUserAppQuota() > -1){
                    return ErrorUtils.handleErrorResponse(ResponseStatusCode.OPERATION_NOT_ALLOWED.intValue(), event, USERAPP_REACH_LIMITED);
                }
            }else if(operations.contains(vn.vnpt.oneiot.core.constants.Constants.DEVICE_CREATE)){
                if(tenantEntity.getStatusRatingPlan() == null || tenantEntity.getFreeUserAppQuota() == null
                        || !(tenantEntity.getStatusRatingPlan().equals(RatePlanOrderStatus.ACTIVED) || tenantEntity.getStatusRatingPlan().equals(RatePlanOrderStatus.EXPIRED))
                ){
                    return ErrorUtils.handleErrorResponse(ResponseStatusCode.OPERATION_NOT_ALLOWED.intValue(), event, NOT_REGISTER_RATING_PLAN);
                }
                Integer numberDeviceCreate = 1;
                for(String op : operations){
                    if(op.startsWith("TotalDeviceCreate_")){
                        numberDeviceCreate = Integer.valueOf(op.replace("TotalDeviceCreate_", ""));
                    }
                }
                Long countDevice = identityRepository.countByTenantIdAndTypeAndActiveNotIn(tenantEntity.getCode(), DEVICE_IDENTITY_TYPE, Arrays.asList(new Integer[]{vn.vnpt.oneiot.base.constants.Constants.EntityStatus.DELETED})) + numberDeviceCreate;
                if(countDevice > tenantEntity.getFreeDeviceQuota() && tenantEntity.getFreeDeviceQuota() > -1){
                    return ErrorUtils.handleErrorResponse(ResponseStatusCode.OPERATION_NOT_ALLOWED.intValue(), event, DEVICE_REACH_LIMITED);
                }
            }
        }
         **/

        event.payload = ObjectMapperUtil.toJsonString(tokenInfoDTO);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    private boolean isOriginatorEqual (String originator1, String originator2) {
        if (originator1.startsWith(Patterns.SP_RELATIVE_PREFIX)) originator1 = originator1.substring(Patterns.SP_RELATIVE_PREFIX.length());
        else if (originator1.startsWith(Patterns.ABS_PREFIX)) originator1 = originator1.substring(Patterns.ABS_PREFIX.length());
        if (originator2.startsWith(Patterns.SP_RELATIVE_PREFIX)) originator2 = originator2.substring(Patterns.SP_RELATIVE_PREFIX.length());
        else if (originator2.startsWith(Patterns.ABS_PREFIX)) originator2 = originator2.substring(Patterns.ABS_PREFIX.length());
        if (originator1.equals(originator2)) return true;
        return false;
    }
}
