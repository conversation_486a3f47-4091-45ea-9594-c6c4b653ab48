package vn.vnpt.oneiot.core.generic.dto;

import java.util.List;

public class CategoriesListResponse {
    private String id;
    private String name;
    private String parentId;
    private List<CategoriesListResponse> child;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public List<CategoriesListResponse> getChild() {
        return child;
    }

    public void setChild(List<CategoriesListResponse> child) {
        this.child = child;
    }
}
