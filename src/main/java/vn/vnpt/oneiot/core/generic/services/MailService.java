package vn.vnpt.oneiot.core.generic.services;

import io.github.jhipster.config.JHipsterProperties;
import org.apache.commons.lang3.CharEncoding;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring4.SpringTemplateEngine;
import vn.vnpt.oneiot.core.mongo.entity.UserEntity;

import javax.mail.internet.MimeMessage;
import java.util.Locale;

/**
 * Created by huyvv
 * Date: 03/02/2020
 * Time: 1:48 PM
 * for all issues, contact me: <EMAIL>
 **/
@Service
public class MailService {
    private final Logger logger = LoggerFactory.getLogger(MailService.class);

    @Value("${baseUrl}")
    private String baseUrl;

    private static final String USER = "userEntity";

    private static final String BASE_URL = "baseUrl";

    private JavaMailSender javaMailSender;

    private MessageSource messageSource;

    private final SpringTemplateEngine templateEngine;

    private JHipsterProperties jHipsterProperties;

    @Autowired
    public void setJavaMailSender(JavaMailSender javaMailSender) {
        this.javaMailSender = javaMailSender;
    }

    @Autowired
    public void setMessageSource(MessageSource messageSource) {
        this.messageSource = messageSource;
    }

    @Autowired
    public void setjHipsterProperties(JHipsterProperties jHipsterProperties) {
        this.jHipsterProperties = jHipsterProperties;
    }

    public MailService(SpringTemplateEngine templateEngine){
        this.templateEngine = templateEngine;
    }

    @Async
    public void sendEmail(String to, String subject, String content, boolean isMultipart, boolean isHtml) {
        if(!isValid(to)) return;
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        try {
            MimeMessageHelper message = new MimeMessageHelper(mimeMessage, isMultipart, CharEncoding.UTF_8);
            message.setTo(to);
            message.setFrom(jHipsterProperties.getMail().getFrom());
            message.setSubject(subject);
            message.setText(content, isHtml);
            javaMailSender.send(mimeMessage);
            logger.debug("Sent email to User '{}'", to);
        } catch (Exception e) {
            logger.warn("Email could not be sent to user '{}'", to);
            e.printStackTrace();
        }
    }

    @Async
    public void sendCreationEmail(UserEntity userEntity) {
        logger.debug("Sending creation email to '{}'", userEntity.getEmail());
        Locale locale = getLocale(userEntity.getLangKey());
        Context context = new Context(locale);

        String resetUrl = baseUrl + "/#/reset-password?token=" + userEntity.getActivationToken() + "&email=" + userEntity.getEmail()  + "&active=1";
        context.setVariable("RESET_URL", resetUrl);
        context.setVariable(USER, userEntity);
        context.setVariable(BASE_URL, jHipsterProperties.getMail().getBaseUrl());
        String content;
        String subject;
        System.out.println(userEntity.getFullName());
        System.out.println(userEntity.getFirstName());
        System.out.println(userEntity.getLastName());
        content= templateEngine.process("mails/creationEmail", context);
        subject = messageSource.getMessage("email.activation.title", null, locale);

        sendEmail(userEntity.getEmail(), subject, content, false, true);
    }

    @Async
    public void sendPasswordResetMail(UserEntity userEntity) {
        logger.debug("Sending password reset email to '{}'", userEntity.getEmail());
        Locale locale = getLocale(userEntity.getLangKey());
        Context context = new Context();
        String resetUrl = baseUrl+ "/#/reset-password?token=" + userEntity.getForgotPasswordToken() + "&email=" + userEntity.getEmail() + "&active=0";
        context.setVariable("RESET_URL", resetUrl);
        context.setVariable(USER, userEntity);
        context.setVariable(BASE_URL, jHipsterProperties.getMail().getBaseUrl());
        String content = templateEngine.process("mails/passwordResetEmail", context);
        String subject = messageSource.getMessage("email.reset.title", null, locale);
        sendEmail(userEntity.getEmail(), subject, content, false, true);
    }

    @Async
    public void sendNotificationMail(String content, String subject, String email) {
        sendEmail(email, subject, content, false, true);
    }

    public Locale getLocale(String language) {
        if(language != null) return Locale.forLanguageTag(language);
        return Locale.forLanguageTag("en");
    }

    static boolean isValid(String email) {
        if(email.contains(".") && email.contains("@")){
            return true;
        }
        return false;
    }
}
