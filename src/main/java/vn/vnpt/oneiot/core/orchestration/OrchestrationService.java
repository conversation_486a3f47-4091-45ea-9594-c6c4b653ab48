package vn.vnpt.oneiot.core.orchestration;

import com.google.common.base.Strings;
import com.google.gson.Gson;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.vnpt.oneiot.base.constants.AMQPConstant;
import vn.vnpt.oneiot.base.event.AMQPSubscribes;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.event.EventBus;
import vn.vnpt.oneiot.base.mongo.controller.BaseTransactionService;
import vn.vnpt.oneiot.base.mongo.controller.TransactionService;
import vn.vnpt.oneiot.common.constants.ResourceType;
import vn.vnpt.oneiot.common.entities.AeEntity;
import vn.vnpt.oneiot.common.entities.ResourceEntity;
import vn.vnpt.oneiot.common.entities.TransactionEntity;
import vn.vnpt.oneiot.common.resource.ResponsePrimitive;
import vn.vnpt.oneiot.core.constants.Constants;
import vn.vnpt.oneiot.core.mongo.generic.MgCrudService;
import vn.vnpt.oneiot.core.mongo.services.AccessControlPolicyService;

import static vn.vnpt.oneiot.base.constants.AMQPConstant.*;

/**
 * Author: kiendt
 * Date: 4/13/2020
 * Contact: <EMAIL>
 */
@Service
@EnableScheduling
@Transactional
public class OrchestrationService extends BaseTransactionService {

    AccessControlPolicyService accessControlPolicyService;

    @Autowired
    public void setAccessControlPolicyService(AccessControlPolicyService accessControlPolicyService) {
        this.accessControlPolicyService = accessControlPolicyService;
    }

    @Autowired
    public void setTransactionService(TransactionService transactionService) {
        super.transactionService = transactionService;
    }

    @Autowired
    public void setEventBus(EventBus eventBus) {
        this.eventBus = eventBus;
    }

    @Autowired
    public void setMasterOrchesSynchronized(@Value("${master-orches-sychronized}") boolean masterOrchesSynchonized) {
        this.masterOrchesSynchonized = masterOrchesSynchonized;
    }

    @Autowired
    public void setRetryNumberSychronized(@Value("${retry-number-sychronized}") Integer retryNumberSychronized) {
        this.retryNumber = retryNumberSychronized;
    }

    @Autowired
    public void setKey() {
        this.retryRoutingKey = Constants.ROUTING_ORCHESTRATION_RETRY_AUTHORIZATION;
        this.retryExchangeKey = this.rollbackExchangeKey = AMQPConstant.AMQP_EXCHANGE_ONEM2M;
        this.rollbackRoutingKey = Constants.ROUTING_ORCHESTRATION_ROLLBACK_AUTHORIZATION;
    }

    public void sendEvent(Event event, String routingKey) {
        MessageProperties messageProperties = new MessageProperties();
        messageProperties.setReplyTo(ROUTING_KEY_ONEM2M_PRIMITIVE_REGISTRATION_GROUP);
        eventBus.publish(getExchangeFromRoutingKey(routingKey), routingKey, event, messageProperties);
    }

    @Scheduled(fixedRate = 60000)
    public void getDataFromTransactionToRetry() throws InterruptedException {
        if (isReady()) {
            super.getDataFromTransactionToRetry();
        }
    }

    //    @Scheduled(fixedRate = 60000 * 60)
//    @Scheduled(fixedRate = 60000 * 10)
    public void getDataFromTransactionToRollback() throws InterruptedException {
        if (isReady()) {
            super.getDataFromTransactionToRetry();
        }
    }

//    @AMQPSubscribes(queue = Constants.QUEUE_ORCHESTRATION_RETRY_AUTHORIZATION, exchange = AMQPConstant.AMQP_EXCHANGE_INTERNAL, routingKey = Constants.ROUTING_ORCHESTRATION_RETRY_AUTHORIZATION)
    public void processRetrySynchronize(Event event, MessageProperties properties) {
        if (!Strings.isNullOrEmpty(event.payload)) {
            TransactionEntity entity = new Gson().fromJson(event.payload, TransactionEntity.class);
            entity.setRetryNumber(entity.getRetryNumber() + 1);
            transactionService.update(entity);
            if (entity.getRetryNumber() == retryNumber) {
                Event eventSyn = new Event();
                eventSyn.payload = (new Gson()).toJson(entity);
                eventBus.publish(this.rollbackExchangeKey, this.rollbackRoutingKey, eventSyn);
            } else {
                // send retry synchronized to orchestration
                ResponsePrimitive responsePrimitive = new Gson().fromJson(entity.getTransactionSynchronizeData(), ResponsePrimitive.class);
                logger.info(new Gson().toJson(responsePrimitive));
                sendEvent(createEventToSynchonized(responsePrimitive), ROUTING_KEY_ONEM2M_PRIMITIVE_ORCHESTRATION);
                //
            }
        }
    }

//    @AMQPSubscribes(queue = Constants.QUEUE_ORCHESTRATION_ROLLBACK_AUTHORIZATION, exchange = AMQPConstant.AMQP_EXCHANGE_INTERNAL, routingKey = Constants.ROUTING_ORCHESTRATION_ROLLBACK_AUTHORIZATION)
    public void processRollback(Event event, MessageProperties properties) {
        if (!Strings.isNullOrEmpty(event.payload)) {
            TransactionEntity entity = new Gson().fromJson(event.payload, TransactionEntity.class);
            if (entity.getTransactionDataType().equals(ResourceType.ACCESS_CONTROL_POLICY)) {
                rollback(entity.getTransactionStatus(), entity.getTransactionId(), accessControlPolicyService, new Gson().fromJson(entity.getTransactionData(), AeEntity.class));
            }
        }
    }


    protected void rollback(String transacationStatus, String transactionId, MgCrudService service, ResourceEntity entity) {
        byte var6 = -1;
        switch(transacationStatus.hashCode()) {
            case -**********:
                if (transacationStatus.equals("UPDATE")) {
                    var6 = 1;
                }
                break;
            case **********:
                if (transacationStatus.equals("CREATE")) {
                    var6 = 0;
                }
                break;
            case 2012838315:
                if (transacationStatus.equals("DELETE")) {
                    var6 = 2;
                }
        }

        switch(var6) {
            case 0:
                this.rollbackEventCreate(transactionId, service, entity);
                break;
            case 1:
                this.rollbackEventUpdated(transactionId, service, entity);
                break;
            case 2:
                this.rollbackEventDelete(transactionId, service, entity);
                break;
            default:
                this.transactionService.delete(transactionId);
                this.logger.error("Cannot found method for transaction status {}", transacationStatus);
        }

    }

    protected void rollbackEventCreate(String transactionId, MgCrudService service, ResourceEntity entity) {
        service.delete(entity.getResourceID());
        this.transactionService.delete(transactionId);
        this.logger.info("Roll back entity {}", entity);
    }

    protected void rollbackEventUpdated(String transactionId, MgCrudService service, ResourceEntity entity) {
        service.update(entity);
        this.transactionService.delete(transactionId);
        this.logger.info("Roll back entity {}", entity);
    }

    protected void rollbackEventDelete(String transactionId, MgCrudService service, ResourceEntity entity) {
        service.create(entity);
        this.transactionService.delete(transactionId);
        this.logger.info("Roll back entity {}", entity);
    }

}
