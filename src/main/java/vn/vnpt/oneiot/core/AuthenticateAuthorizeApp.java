package vn.vnpt.oneiot.core;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.EnableScheduling;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.core.api.nbi.models.CreateTokenDTO;
import vn.vnpt.oneiot.core.mongo.entity.IdentityEntity;
import vn.vnpt.oneiot.core.mongo.repositories.IdentityRepository;
import vn.vnpt.oneiot.core.mongo.services.TokenService;

import java.util.List;

@SpringBootApplication(exclude = {
        DataSourceAutoConfiguration.class,
        DataSourceTransactionManagerAutoConfiguration.class,
        HibernateJpaAutoConfiguration.class
})
@EnableScheduling
@EntityScan("vn.vnpt.oneiot.core")
public class AuthenticateAuthorizeApp {
    private static Logger logger = LoggerFactory.getLogger(AuthenticateAuthorizeApp.class);

    @Autowired
    private TokenService tokenService;

    @Autowired
    private IdentityRepository identityRepository;

    @EventListener(ApplicationReadyEvent.class)
    public void doSomethingAfterStartup() throws InterruptedException {
        logger.info("Iot Platform have just started up");

        // Điều thông tin của ứng dụng và device sau khi khởi động
        refreshApplicationAndDeviceTokens();
    }

    /**
     * Hàm refresh token cho tất cả ứng dụng và device trong hệ thống
     * Điều thông tin của cả ứng dụng và device
     */
    public Event refreshToken(Event event) {
        try {
            logger.info("Starting refresh token process for applications and devices");

            // Lấy thông tin từ event payload
            CreateTokenDTO tokenInfo = ObjectMapperUtil.objectMapper(event.payload, CreateTokenDTO.class);

            // Gọi service để refresh token
            Event result = tokenService.refreshToken(event);

            if (result.statusCode == ResponseStatusCode.OK.intValue()) {
                logger.info("Token refreshed successfully for holder: {}", tokenInfo.getHolderId());

                // Log thông tin chi tiết về token đã refresh
                CreateTokenDTO refreshedToken = ObjectMapperUtil.objectMapper(result.payload, CreateTokenDTO.class);
                logger.info("New access token generated for holder: {}", refreshedToken.getHolderId());
                logger.info("Token type: {}", refreshedToken.getType());
                logger.info("Token expiry: {}", refreshedToken.getExpiry());
            } else {
                logger.error("Failed to refresh token. Status code: {}, Error: {}",
                    result.statusCode, result.errorText);
            }

            return result;

        } catch (Exception e) {
            logger.error("Error occurred while refreshing token: {}", e.getMessage(), e);
            event.statusCode = ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue();
            event.errorText = "Internal server error during token refresh";
            return event;
        }
    }

    /**
     * Điều thông tin của tất cả ứng dụng và device trong hệ thống
     */
    private void refreshApplicationAndDeviceTokens() {
        try {
            logger.info("Starting to refresh tokens for all applications and devices");

            // Lấy danh sách tất cả ứng dụng (type = 1)
            List<IdentityEntity> applications = identityRepository.findByType(1); // APP_IDENTITY_TYPE = 1
            logger.info("Found {} applications to refresh tokens", applications != null ? applications.size() : 0);

            if (applications != null) {
                for (IdentityEntity app : applications) {
                    refreshTokenForEntity(app, "Application");
                }
            }

            // Lấy danh sách tất cả device (type = 2)
            List<IdentityEntity> devices = identityRepository.findByType(2); // DEVICE_IDENTITY_TYPE = 2
            logger.info("Found {} devices to refresh tokens", devices != null ? devices.size() : 0);

            if (devices != null) {
                for (IdentityEntity device : devices) {
                    refreshTokenForEntity(device, "Device");
                }
            }

            logger.info("Completed refreshing tokens for all applications and devices");

        } catch (Exception e) {
            logger.error("Error occurred while refreshing tokens for applications and devices: {}",
                e.getMessage(), e);
        }
    }

    /**
     * Refresh token cho một entity cụ thể (application hoặc device)
     */
    private void refreshTokenForEntity(IdentityEntity entity, String entityType) {
        try {
            if (entity.getAccessToken() != null && entity.getRefreshToken() != null) {
                // Tạo CreateTokenDTO để refresh token
                CreateTokenDTO tokenInfo = new CreateTokenDTO();
                tokenInfo.setAccessToken(entity.getAccessToken());
                tokenInfo.setRefreshToken(entity.getRefreshToken());
                tokenInfo.setHolderId(entity.getId());
                tokenInfo.setType(entity.getType());

                // Tạo Event để gọi refresh token
                Event refreshEvent = new Event();
                refreshEvent.payload = ObjectMapperUtil.toJsonString(tokenInfo);

                // Gọi hàm refresh token
                Event result = refreshToken(refreshEvent);

                if (result.statusCode == ResponseStatusCode.OK.intValue()) {
                    logger.info("{} token refreshed successfully - ID: {}, Name: {}",
                        entityType, entity.getId(), entity.getName());

                    // Log thông tin chi tiết
                    logEntityDetails(entity, entityType);
                } else {
                    logger.warn("Failed to refresh {} token - ID: {}, Name: {}, Error: {}",
                        entityType, entity.getId(), entity.getName(), result.errorText);
                }
            } else {
                logger.debug("{} {} does not have tokens to refresh", entityType, entity.getName());
            }

        } catch (Exception e) {
            logger.error("Error refreshing token for {} {}: {}", entityType, entity.getName(), e.getMessage());
        }
    }

    /**
     * Log thông tin chi tiết của entity
     */
    private void logEntityDetails(IdentityEntity entity, String entityType) {
        logger.info("=== {} Details ===", entityType);
        logger.info("ID: {}", entity.getId());
        logger.info("Name: {}", entity.getName());
        logger.info("Type: {}", entity.getType());
        logger.info("SubType: {}", entity.getSubType());
        logger.info("TenantId: {}", entity.getTenantId());
        logger.info("MainAppId: {}", entity.getMainAppId());
        logger.info("UserId: {}", entity.getUserId());
        logger.info("Active Status: {}", entity.getActive());
        logger.info("Description: {}", entity.getDescription());
        if (entity.getCategory() != null) {
            logger.info("Category: {}", entity.getCategory());
        }
        logger.info("Created: {}", entity.getCreated());
        logger.info("Updated: {}", entity.getUpdated());
        logger.info("========================");
    }

    public static void main(String[] args) {
        SpringApplication.run(AuthenticateAuthorizeApp.class, args);
    }
}
