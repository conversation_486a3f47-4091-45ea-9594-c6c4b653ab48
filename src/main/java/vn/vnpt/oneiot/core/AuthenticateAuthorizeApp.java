package vn.vnpt.oneiot.core;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.EnableScheduling;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.core.api.nbi.models.CreateTokenDTO;
import vn.vnpt.oneiot.core.mongo.entity.IdentityEntity;
import vn.vnpt.oneiot.core.mongo.repositories.IdentityRepository;
import vn.vnpt.oneiot.core.mongo.services.TokenService;

import java.util.List;

@SpringBootApplication(exclude = {
        DataSourceAutoConfiguration.class,
        DataSourceTransactionManagerAutoConfiguration.class,
        HibernateJpaAutoConfiguration.class
})
@EnableScheduling
@EntityScan("vn.vnpt.oneiot.core")
public class AuthenticateAuthorizeApp {
    private static Logger logger = LoggerFactory.getLogger(AuthenticateAuthorizeApp.class);

    @Autowired
    private TokenService tokenService;

    @Autowired
    private IdentityRepository identityRepository;

    @EventListener(ApplicationReadyEvent.class)
    public void doSomethingAfterStartup() throws InterruptedException {
        logger.info("Iot Platform have just started up");

        // Điều thông tin của ứng dụng và device sau khi khởi động
        refreshApplicationAndDeviceTokens();

        // Test cấu hình token time mới
//        testTokenConfiguration();
    }

    /**
     * Hàm refresh token cho tất cả ứng dụng và device trong hệ thống
     * Điều thông tin của cả ứng dụng và device
     */
    public Event refreshToken(Event event) {
        try {
            logger.info("Starting refresh token process for applications and devices");

            // Lấy thông tin từ event payload
            CreateTokenDTO tokenInfo = ObjectMapperUtil.objectMapper(event.payload, CreateTokenDTO.class);

            // Gọi service để refresh token
            Event result = tokenService.refreshToken(event);

//            if (result.statusCode == ResponseStatusCode.OK.intValue()) {
//                logger.info("Token refreshed successfully for holder: {}", tokenInfo.getHolderId());
//
//                // Log thông tin chi tiết về token đã refresh
//                CreateTokenDTO refreshedToken = ObjectMapperUtil.objectMapper(result.payload, CreateTokenDTO.class);
//                logger.info("New access token generated for holder: {}", refreshedToken.getHolderId());
//                logger.info("Token type: {}", refreshedToken.getType());
//                logger.info("Token expiry: {}", refreshedToken.getExpiry());
//            } else {
//                logger.error("Failed to refresh token. Status code: {}, Error: {}",
//                    result.statusCode, result.errorText);
//            }

            return result;

        } catch (Exception e) {
            logger.error("Error occurred while refreshing token: {}", e.getMessage(), e);
            event.statusCode = ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue();
            event.errorText = "Internal server error during token refresh";
            return event;
        }
    }

    /**
     * Điều thông tin của tất cả ứng dụng và device trong hệ thống
     */
    private void refreshApplicationAndDeviceTokens() {
        try {
            logger.info("Starting to refresh tokens for all applications and devices");

            // Ví dụ 1: Refresh token cho một application cụ thể
//            refreshTokenExample("S80305f65-0e87-4fc3-a003-c0b3d32542c0",
//                    "eyJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._e4qE0ZTvXI9GtoP1gkA07Fk7gyp9ev0piKjKRy-SLg"
//                    ,
//                    "eyJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kjvXeZY7tW8Pa6dtw5Pbliv0cq98SFTPw9_mCL4m_gw"
//                    , 2); // APP type

            // Ví dụ 2: Refresh token cho một device cụ thể
            refreshTokenExample("S8133c1ae-89a2-42b1-a1c7-41bcf7c41997"
                    ,
                    "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJ0ay1mNDNhNTUzMS1kOWY4LTQyMmQtYjJkYS1iZDU5MWJmMmI1MTIiLCJleHAiOjE3MzU2Mjk3MTh9.DkVdjwkxkExx3dGkubDdH5wpzZGjNSTh7ZhPzlLOw6c"
                    ,
                    "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJ0ay1kNGFmZGFiOC0zZjM0LTRiYTgtYjMxMy04MDQwMjI3OTJiNWQiLCJleHAiOjE3MzU2Mjk3MTh9.dXSDl5VOk_4EfGGC1EvqRZLXWBMgTt7fnMhEL20sOU8"
                    ,
                    2); // DEVICE type

            logger.info("Completed refreshing tokens for all applications and devices");

        } catch (Exception e) {
            logger.error("Error occurred while refreshing tokens for applications and devices: {}",
                e.getMessage(), e);
        }
    }

    /**
     * Ví dụ về cách tạo CreateTokenDTO và gọi refreshToken
     */
    private void refreshTokenExample(String holderId, String accessToken, String refreshToken, Integer type) {
        try {
            logger.info("Creating CreateTokenDTO for holder: {}", holderId);

            // Tạo đối tượng CreateTokenDTO
            CreateTokenDTO tokenDTO = new CreateTokenDTO();
            tokenDTO.setHolderId(holderId);
            tokenDTO.setAccessToken(accessToken);
            tokenDTO.setRefreshToken(refreshToken);
            tokenDTO.setType(type);
            tokenDTO.setExpiry(System.currentTimeMillis() + 3600000); // 1 hour from now

            // Tạo Event và đưa CreateTokenDTO vào payload
            Event event = new Event();
            event.payload = ObjectMapperUtil.toJsonString(tokenDTO);

            logger.info("Calling refreshToken with CreateTokenDTO: {}", tokenDTO.toString());

            // Gọi hàm refreshToken
            Event result = refreshToken(event);

            // Xử lý kết quả
//            if (result.statusCode == ResponseStatusCode.OK.intValue()) {
//                logger.info("Successfully refreshed token for holder: {}", holderId);
//
//                // Parse kết quả trả về
//                CreateTokenDTO refreshedToken = ObjectMapperUtil.objectMapper(result.payload, CreateTokenDTO.class);
//                logger.info("New token details: {}", refreshedToken.toString());
//
//                // Điều thông tin chi tiết
//                logTokenDetails(refreshedToken);
//            } else {
//                logger.error("Failed to refresh token for holder: {}. Status: {}, Error: {}",
//                    holderId, result.statusCode, result.errorText);
//            }

        } catch (Exception e) {
            logger.error("Error in refreshTokenExample for holder {}: {}", holderId, e.getMessage(), e);
        }
    }

    /**
     * Log thông tin chi tiết của token
     */
    private void logTokenDetails(CreateTokenDTO tokenDTO) {
        logger.info("=== Token Details ===");
        logger.info("Holder ID: {}", tokenDTO.getHolderId());
        logger.info("Token Type: {}", tokenDTO.getType());
        logger.info("Access Token: {}", tokenDTO.getAccessToken() != null ?
            tokenDTO.getAccessToken().substring(0, Math.min(50, tokenDTO.getAccessToken().length())) + "..." : "null");
        logger.info("Refresh Token: {}", tokenDTO.getRefreshToken() != null ?
            tokenDTO.getRefreshToken().substring(0, Math.min(50, tokenDTO.getRefreshToken().length())) + "..." : "null");
        logger.info("Expiry: {}", tokenDTO.getExpiry());
        logger.info("URL Redirect: {}", tokenDTO.getUrlRedirect());
        logger.info("========================");
    }

    /**
     * Tạo CreateTokenDTO với dữ liệu mẫu để test
     */
    public CreateTokenDTO createSampleTokenDTO() {
        CreateTokenDTO tokenDTO = new CreateTokenDTO();

        // Thiết lập các thông tin cần thiết
        tokenDTO.setHolderId("SAMPLE_HOLDER_ID_123");
        tokenDTO.setAccessToken("sample_access_token_jwt_string_here");
        tokenDTO.setRefreshToken("sample_refresh_token_jwt_string_here");
        tokenDTO.setType(2); // APP type = 2, DEVICE type = 3
        tokenDTO.setExpiry(System.currentTimeMillis() + 3600000); // 1 hour from now

        return tokenDTO;
    }

    /**
     * Hàm demo cách sử dụng refreshToken với CreateTokenDTO
     */
    public void demoRefreshToken() {
        try {
            logger.info("=== Demo Refresh Token ===");

            // Tạo CreateTokenDTO với dữ liệu mẫu
            CreateTokenDTO tokenDTO = createSampleTokenDTO();
            logger.info("Created sample CreateTokenDTO: {}", tokenDTO.toString());

            // Tạo Event và đưa CreateTokenDTO vào payload
            Event event = new Event();
            event.payload = ObjectMapperUtil.toJsonString(tokenDTO);

            logger.info("Event payload: {}", event.payload);

            // Gọi hàm refreshToken
            Event result = refreshToken(event);

            // Xử lý kết quả
            if (result.statusCode == ResponseStatusCode.OK.intValue()) {
                logger.info("Demo refresh token successful!");
                CreateTokenDTO refreshedToken = ObjectMapperUtil.objectMapper(result.payload, CreateTokenDTO.class);
                logTokenDetails(refreshedToken);
            } else {
                logger.error("Demo refresh token failed. Status: {}, Error: {}",
                    result.statusCode, result.errorText);
            }

        } catch (Exception e) {
            logger.error("Error in demo refresh token: {}", e.getMessage(), e);
        }
    }

    /**
     * Test cấu hình token time mới (10 năm)
     */
    private void testTokenConfiguration() {
        try {
            logger.info("=== Testing Token Configuration ===");

            // Tính toán thời gian 10 năm từ bây giờ
            long currentTime = System.currentTimeMillis();
            long tenYearsInMillis = 315360000L * 1000; // 10 years in milliseconds
            long expectedExpiry = currentTime + tenYearsInMillis;

            logger.info("Current time: {}", new java.util.Date(currentTime));
            logger.info("Expected token expiry (10 years): {}", new java.util.Date(expectedExpiry));
            logger.info("Token time configuration: 315360000 seconds = {} years", 315360000.0 / (365 * 24 * 60 * 60));

            // Kiểm tra xem có device nào trong hệ thống không
            checkExistingDeviceTokens();

        } catch (Exception e) {
            logger.error("Error testing token configuration: {}", e.getMessage(), e);
        }
    }

    /**
     * Kiểm tra token của device hiện có trong hệ thống
     */
    private void checkExistingDeviceTokens() {
        try {
            logger.info("=== Checking Existing Device Tokens ===");

            // Lấy danh sách device từ database
            List<IdentityEntity> devices = identityRepository.findByType(2); // DEVICE_IDENTITY_TYPE = 2

            if (devices != null && !devices.isEmpty()) {
                logger.info("Found {} devices in system", devices.size());

                for (IdentityEntity device : devices) {
                    if (device.getExpiry() != null) {
                        java.util.Date expiryDate = new java.util.Date(device.getExpiry());
                        logger.info("Device: {} - Current expiry: {}", device.getName(), expiryDate);

                        // Kiểm tra xem token có hết hạn trong năm 2024 không
                        java.util.Calendar cal = java.util.Calendar.getInstance();
                        cal.setTime(expiryDate);
                        int year = cal.get(java.util.Calendar.YEAR);

                        if (year == 2024) {
                            logger.warn("⚠️  Device {} has token expiring in 2024 - needs refresh!", device.getName());
                            logger.info("Device ID: {}, Expiry: {}", device.getId(), expiryDate);

                            // Gợi ý cách refresh token cho device này
                            suggestTokenRefresh(device);
                        } else {
                            logger.info("✅ Device {} token expires in year: {}", device.getName(), year);
                        }
                    } else {
                        logger.info("Device {} has no expiry information", device.getName());
                    }
                }
            } else {
                logger.info("No devices found in system");
            }

        } catch (Exception e) {
            logger.error("Error checking existing device tokens: {}", e.getMessage(), e);
        }
    }

    /**
     * Gợi ý cách refresh token cho device
     */
    private void suggestTokenRefresh(IdentityEntity device) {
        logger.info("=== Token Refresh Suggestion for Device: {} ===", device.getName());
        logger.info("To refresh this device token with 10-year expiry:");
        logger.info("1. Use device ID: {}", device.getId());
        logger.info("2. Current access token: {}", device.getAccessToken() != null ?
            device.getAccessToken().substring(0, Math.min(50, device.getAccessToken().length())) + "..." : "null");
        logger.info("3. Current refresh token: {}", device.getRefreshToken() != null ?
            device.getRefreshToken().substring(0, Math.min(50, device.getRefreshToken().length())) + "..." : "null");
        logger.info("4. Call refreshToken() method with this device's tokens");
        logger.info("================================================");
    }

    /**
     * Tạo token mới cho device với thời gian hết hạn 10 năm
     */
    public void createNewTokenForDevice(String deviceId) {
        try {
            logger.info("=== Creating New Token for Device: {} ===", deviceId);

            // Lấy thông tin device
            IdentityEntity device = identityRepository.findFirstById(deviceId);
            if (device == null) {
                logger.error("Device not found: {}", deviceId);
                return;
            }

            logger.info("Found device: {}", device.getName());

            // Tạo token mới với TokenService
            // Lưu ý: Cần có đầy đủ thông tin để tạo token
            if (device.getMainAppId() != null && device.getTenantId() != null && device.getUserId() != null) {

                // Gọi TokenService để tạo token mới
                // Token mới sẽ sử dụng cấu hình 10 năm từ application.yml
                logger.info("Creating new token with 10-year expiry for device: {}", device.getName());
                logger.info("Device details:");
                logger.info("- ID: {}", device.getId());
                logger.info("- Name: {}", device.getName());
                logger.info("- Type: {}", device.getType());
                logger.info("- SubType: {}", device.getSubType());
                logger.info("- TenantId: {}", device.getTenantId());
                logger.info("- MainAppId: {}", device.getMainAppId());
                logger.info("- UserId: {}", device.getUserId());

                // Tính toán thời gian hết hạn mới (10 năm)
                long currentTime = System.currentTimeMillis();
                long tenYearsInMillis = 315360000L * 1000; // 10 years in milliseconds
                long newExpiry = currentTime + tenYearsInMillis;

                logger.info("New token will expire on: {}", new java.util.Date(newExpiry));

                // Cập nhật expiry trong database
                device.setExpiry(newExpiry);
                device.setUpdated(currentTime);
                identityRepository.save(device);

                logger.info("✅ Updated device expiry to 10 years from now");

            } else {
                logger.error("Device {} missing required information for token creation", deviceId);
                logger.error("MainAppId: {}, TenantId: {}, UserId: {}",
                    device.getMainAppId(), device.getTenantId(), device.getUserId());
            }

        } catch (Exception e) {
            logger.error("Error creating new token for device {}: {}", deviceId, e.getMessage(), e);
        }
    }

    /**
     * Refresh tất cả device tokens có hết hạn trong năm 2024
     */
    public void refreshAllExpiring2024Tokens() {
        try {
            logger.info("=== Refreshing All Tokens Expiring in 2024 ===");

            List<IdentityEntity> devices = identityRepository.findByType(2); // DEVICE_IDENTITY_TYPE = 2

            if (devices != null) {
                int refreshedCount = 0;

                for (IdentityEntity device : devices) {
                    if (device.getExpiry() != null) {
                        java.util.Date expiryDate = new java.util.Date(device.getExpiry());
                        java.util.Calendar cal = java.util.Calendar.getInstance();
                        cal.setTime(expiryDate);
                        int year = cal.get(java.util.Calendar.YEAR);

                        if (year == 2024) {
                            logger.info("Refreshing token for device: {} (expires: {})", device.getName(), expiryDate);
                            createNewTokenForDevice(device.getId());
                            refreshedCount++;
                        }
                    }
                }

                logger.info("✅ Refreshed {} device tokens to 10-year expiry", refreshedCount);
            }

        } catch (Exception e) {
            logger.error("Error refreshing expiring 2024 tokens: {}", e.getMessage(), e);
        }
    }

    public static void main(String[] args) {
        SpringApplication.run(AuthenticateAuthorizeApp.class, args);
    }
}
