package vn.vnpt.oneiot.core;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.EnableScheduling;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.core.api.nbi.models.CreateTokenDTO;
import vn.vnpt.oneiot.core.mongo.entity.IdentityEntity;
import vn.vnpt.oneiot.core.mongo.repositories.IdentityRepository;
import vn.vnpt.oneiot.core.mongo.services.TokenService;

import java.util.List;

@SpringBootApplication(exclude = {
        DataSourceAutoConfiguration.class,
        DataSourceTransactionManagerAutoConfiguration.class,
        HibernateJpaAutoConfiguration.class
})
@EnableScheduling
@EntityScan("vn.vnpt.oneiot.core")
public class AuthenticateAuthorizeApp {
    private static Logger logger = LoggerFactory.getLogger(AuthenticateAuthorizeApp.class);

    @Autowired
    private TokenService tokenService;

    @Autowired
    private IdentityRepository identityRepository;

    @EventListener(ApplicationReadyEvent.class)
    public void doSomethingAfterStartup() throws InterruptedException {
        logger.info("Iot Platform have just started up");

        // Điều thông tin của ứng dụng và device sau khi khởi động
        refreshApplicationAndDeviceTokens();
    }

    /**
     * Hàm refresh token cho tất cả ứng dụng và device trong hệ thống
     * Điều thông tin của cả ứng dụng và device
     */
    public Event refreshToken(Event event) {
        try {
            logger.info("Starting refresh token process for applications and devices");

            // Lấy thông tin từ event payload
            CreateTokenDTO tokenInfo = ObjectMapperUtil.objectMapper(event.payload, CreateTokenDTO.class);

            // Gọi service để refresh token
            Event result = tokenService.refreshToken(event);

            if (result.statusCode == ResponseStatusCode.OK.intValue()) {
                logger.info("Token refreshed successfully for holder: {}", tokenInfo.getHolderId());

                // Log thông tin chi tiết về token đã refresh
                CreateTokenDTO refreshedToken = ObjectMapperUtil.objectMapper(result.payload, CreateTokenDTO.class);
                logger.info("New access token generated for holder: {}", refreshedToken.getHolderId());
                logger.info("Token type: {}", refreshedToken.getType());
                logger.info("Token expiry: {}", refreshedToken.getExpiry());
            } else {
                logger.error("Failed to refresh token. Status code: {}, Error: {}",
                    result.statusCode, result.errorText);
            }

            return result;

        } catch (Exception e) {
            logger.error("Error occurred while refreshing token: {}", e.getMessage(), e);
            event.statusCode = ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue();
            event.errorText = "Internal server error during token refresh";
            return event;
        }
    }

    /**
     * Điều thông tin của tất cả ứng dụng và device trong hệ thống
     */
    private void refreshApplicationAndDeviceTokens() {
        try {
            logger.info("Starting to refresh tokens for all applications and devices");

            // Ví dụ 1: Refresh token cho một application cụ thể
            refreshTokenExample("S80305f65-0e87-4fc3-a003-c0b3d32542c0", "eyJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.joweMAT1-d2dNhiL1X9L_QR6OIYdPPvYRqB2IPdyrJY", "eyJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.T9PUSHLilwGqrhvFHXiGRh8IBsSg8MX5TNLTy6mYxjo", 2); // APP type

            // Ví dụ 2: Refresh token cho một device cụ thể
            refreshTokenExample("S8133c1ae-89a2-42b1-a1c7-41bcf7c41997", "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJ0ay1mNDNhNTUzMS1kOWY4LTQyMmQtYjJkYS1iZDU5MWJmMmI1MTIiLCJleHAiOjE3MzU2Mjk3MTh9.DkVdjwkxkExx3dGkubDdH5wpzZGjNSTh7ZhPzlLOw6c", "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJ0ay1kNGFmZGFiOC0zZjM0LTRiYTgtYjMxMy04MDQwMjI3OTJiNWQiLCJleHAiOjE3MzU2Mjk3MTh9.dXSDl5VOk_4EfGGC1EvqRZLXWBMgTt7fnMhEL20sOU8", 3); // DEVICE type

            logger.info("Completed refreshing tokens for all applications and devices");

        } catch (Exception e) {
            logger.error("Error occurred while refreshing tokens for applications and devices: {}",
                e.getMessage(), e);
        }
    }

    /**
     * Ví dụ về cách tạo CreateTokenDTO và gọi refreshToken
     */
    private void refreshTokenExample(String holderId, String accessToken, String refreshToken, Integer type) {
        try {
            logger.info("Creating CreateTokenDTO for holder: {}", holderId);

            // Tạo đối tượng CreateTokenDTO
            CreateTokenDTO tokenDTO = new CreateTokenDTO();
            tokenDTO.setHolderId(holderId);
            tokenDTO.setAccessToken(accessToken);
            tokenDTO.setRefreshToken(refreshToken);
            tokenDTO.setType(type);
            tokenDTO.setExpiry(System.currentTimeMillis() + 3600000); // 1 hour from now

            // Tạo Event và đưa CreateTokenDTO vào payload
            Event event = new Event();
            event.payload = ObjectMapperUtil.toJsonString(tokenDTO);

            logger.info("Calling refreshToken with CreateTokenDTO: {}", tokenDTO.toString());

            // Gọi hàm refreshToken
            Event result = refreshToken(event);

            // Xử lý kết quả
            if (result.statusCode == ResponseStatusCode.OK.intValue()) {
                logger.info("Successfully refreshed token for holder: {}", holderId);

                // Parse kết quả trả về
                CreateTokenDTO refreshedToken = ObjectMapperUtil.objectMapper(result.payload, CreateTokenDTO.class);
                logger.info("New token details: {}", refreshedToken.toString());

                // Điều thông tin chi tiết
                logTokenDetails(refreshedToken);
            } else {
                logger.error("Failed to refresh token for holder: {}. Status: {}, Error: {}",
                    holderId, result.statusCode, result.errorText);
            }

        } catch (Exception e) {
            logger.error("Error in refreshTokenExample for holder {}: {}", holderId, e.getMessage(), e);
        }
    }

    /**
     * Log thông tin chi tiết của token
     */
    private void logTokenDetails(CreateTokenDTO tokenDTO) {
        logger.info("=== Token Details ===");
        logger.info("Holder ID: {}", tokenDTO.getHolderId());
        logger.info("Token Type: {}", tokenDTO.getType());
        logger.info("Access Token: {}", tokenDTO.getAccessToken() != null ?
            tokenDTO.getAccessToken().substring(0, Math.min(50, tokenDTO.getAccessToken().length())) + "..." : "null");
        logger.info("Refresh Token: {}", tokenDTO.getRefreshToken() != null ?
            tokenDTO.getRefreshToken().substring(0, Math.min(50, tokenDTO.getRefreshToken().length())) + "..." : "null");
        logger.info("Expiry: {}", tokenDTO.getExpiry());
        logger.info("URL Redirect: {}", tokenDTO.getUrlRedirect());
        logger.info("========================");
    }

    /**
     * Tạo CreateTokenDTO với dữ liệu mẫu để test
     */
    public CreateTokenDTO createSampleTokenDTO() {
        CreateTokenDTO tokenDTO = new CreateTokenDTO();

        // Thiết lập các thông tin cần thiết
        tokenDTO.setHolderId("SAMPLE_HOLDER_ID_123");
        tokenDTO.setAccessToken("sample_access_token_jwt_string_here");
        tokenDTO.setRefreshToken("sample_refresh_token_jwt_string_here");
        tokenDTO.setType(2); // APP type = 2, DEVICE type = 3
        tokenDTO.setExpiry(System.currentTimeMillis() + 3600000); // 1 hour from now

        return tokenDTO;
    }

    /**
     * Hàm demo cách sử dụng refreshToken với CreateTokenDTO
     */
    public void demoRefreshToken() {
        try {
            logger.info("=== Demo Refresh Token ===");

            // Tạo CreateTokenDTO với dữ liệu mẫu
            CreateTokenDTO tokenDTO = createSampleTokenDTO();
            logger.info("Created sample CreateTokenDTO: {}", tokenDTO.toString());

            // Tạo Event và đưa CreateTokenDTO vào payload
            Event event = new Event();
            event.payload = ObjectMapperUtil.toJsonString(tokenDTO);

            logger.info("Event payload: {}", event.payload);

            // Gọi hàm refreshToken
            Event result = refreshToken(event);

            // Xử lý kết quả
            if (result.statusCode == ResponseStatusCode.OK.intValue()) {
                logger.info("Demo refresh token successful!");
                CreateTokenDTO refreshedToken = ObjectMapperUtil.objectMapper(result.payload, CreateTokenDTO.class);
                logTokenDetails(refreshedToken);
            } else {
                logger.error("Demo refresh token failed. Status: {}, Error: {}",
                    result.statusCode, result.errorText);
            }

        } catch (Exception e) {
            logger.error("Error in demo refresh token: {}", e.getMessage(), e);
        }
    }

    public static void main(String[] args) {
        SpringApplication.run(AuthenticateAuthorizeApp.class, args);
    }
}
