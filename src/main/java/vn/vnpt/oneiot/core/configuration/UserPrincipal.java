package vn.vnpt.oneiot.core.configuration;

import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * Created by huyvv
 * Date: 06/02/2020
 * Time: 5:09 PM
 * for all issues, contact me: <EMAIL>
 **/
public class UserPrincipal extends org.springframework.security.core.userdetails.User{
    //add new property for principal
    private String tenantId;
    private String userId;

    public UserPrincipal(String username, String password, Collection<? extends GrantedAuthority> authorities, String tenantId, String userId) {
        super(username, password, authorities);
        this.tenantId = tenantId;
        this.userId = userId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
