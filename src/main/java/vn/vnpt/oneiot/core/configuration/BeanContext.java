package vn.vnpt.oneiot.core.configuration;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * Created by huyvv
 * Date: 06/02/2020
 * Time: 5:45 PM
 * for all issues, contact me: <EMAIL>
 **/
@Component
public final class BeanContext implements ApplicationContextAware {

    private BeanContext() {

    }

    private static ApplicationContext context;

    public static <T extends Object> T getBean(Class<T> beanClass) {
        return context.getBean(beanClass);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }
}
