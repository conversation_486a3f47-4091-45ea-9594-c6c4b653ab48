package vn.vnpt.oneiot.core.configuration;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

/**
 * Author: kiendt
 * Date: 3/31/2020
 * Contact: <EMAIL>
 */

@Configuration
@ComponentScan(basePackages = "vn.vnpt.oneiot.base", excludeFilters =
@ComponentScan.Filter(type = FilterType.REGEX, pattern = "vn.vnpt.oneiot.base.event.*")
)
@EnableMongoRepositories(basePackages = {"vn.vnpt.oneiot.core", "vn.vnpt.oneiot.base.mongo.repository"})
public class InitBaseConfiguration {
}
