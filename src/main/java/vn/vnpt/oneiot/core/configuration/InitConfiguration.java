package vn.vnpt.oneiot.core.configuration;

import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisSentinelConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.scheduling.annotation.EnableAsync;
import vn.vnpt.oneiot.base.redis.RedisCache;
import vn.vnpt.oneiot.base.event.AMQPService;
import vn.vnpt.oneiot.base.event.EventBus;
import vn.vnpt.oneiot.base.event.amqp.AMQPEventBus;
import vn.vnpt.oneiot.core.redis.listener.MessageSubscriber;
//import vn.vnpt.oneiot.core.cache.cmd.MessageSubscriber;
//import vn.vnpt.oneiot.core.constants.Constants;

import javax.sql.DataSource;
import java.util.HashSet;

@Configuration
@EnableAsync
@EnableAspectJAutoProxy
//@EnableJpaRepositories("vn.vnpt.oneiot.core.jpa")
//@EnableTransactionManagement
public class InitConfiguration {
    @Autowired
    public Environment env;

    /* ========================= RABBIT MQ ============================ */
    @Bean
    @ConfigurationProperties(prefix = "spring.rabbitmq")
    public CachingConnectionFactory connectionFactory() {
        return new CachingConnectionFactory();
    }

    @Bean
    public EventBus eventBus(CachingConnectionFactory connectionFactory, ApplicationContext ctx) {
        return new AMQPEventBus(connectionFactory, ctx);
    }

    @Bean
    public AMQPService amqpService(ApplicationContext ctx) {
        return new AMQPService(ctx);
    }

    /* ========================= Redis ============================ */

    private boolean useSentinelConfigs() {
        String master = env.getProperty("spring.redis.sentinel.master");
        String nodes = env.getProperty("spring.redis.sentinel.nodes");
        return master != null && nodes != null && !master.isEmpty() && !nodes.isEmpty();
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.redis")
    public RedisConnectionFactory redisConnectionFactory(RedisSentinelConfiguration redisSentinelConfiguration) {
        if (useSentinelConfigs()) return new JedisConnectionFactory(redisSentinelConfiguration);
        return new JedisConnectionFactory();
    }

    // Setup Sentinel config
    @Bean
    public RedisSentinelConfiguration getSentinelConfig() {
        RedisSentinelConfiguration configuration = new RedisSentinelConfiguration();
        if (!useSentinelConfigs()) return configuration;

        String master = env.getProperty("spring.redis.sentinel.master");
        String nodes = env.getProperty("spring.redis.sentinel.nodes");
        configuration.master(master);
        for (String node : nodes.split(",")) {
            String split[] = node.split(":");
            configuration.sentinel(split[0].trim(), Integer.parseInt(split[1].trim()));
        }
        return configuration;
    }

    @Bean
    @ConditionalOnMissingBean(name = "redisTemplate")
    public RedisTemplate<Object, Object> redisTemplateObj(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<Object, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }


    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory jedisConnectionFactory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<String, Object>();
        redisTemplate.setConnectionFactory(jedisConnectionFactory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new StringRedisSerializer());
        return redisTemplate;
    }

    @Bean
    public RedisCache systemCache(RedisTemplate redisTemplate) {
        return new RedisCache(redisTemplate);
    }

    @Bean
    MessageListenerAdapter messageListener(RedisCache objectCache, RedisTemplate redisTemplate) {
        return new MessageListenerAdapter(new MessageSubscriber(objectCache, redisTemplate));
    }

    @Bean
    RedisMessageListenerContainer redisContainer(MessageListenerAdapter messageListenerAdapter, RedisConnectionFactory redisConnectionFactory) {
        RedisMessageListenerContainer container
                = new RedisMessageListenerContainer();
        container.setConnectionFactory(redisConnectionFactory);

        HashSet topicSet = new HashSet();
        topicSet.add(new PatternTopic("__keyevent@*__:expired"));
        container.addMessageListener(messageListenerAdapter, topicSet);
        return container;
    }

    /* ========================= Mysql ============================ */
    @Bean
    @ConditionalOnProperty(name = "spring.datasource")
    @ConfigurationProperties(prefix = "spring.datasource")
    @Primary
    public DataSource dataSource() {
        return new DriverManagerDataSource();
    }
}
