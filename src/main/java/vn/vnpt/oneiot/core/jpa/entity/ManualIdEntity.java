package vn.vnpt.oneiot.core.jpa.entity;

import vn.vnpt.oneiot.core.generic.entity.AbstractEntity;

import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

/**
 * Created by huyvv
 * Date: 16/01/2020
 * Time: 11:00 AM
 * for all issues, contact me: <EMAIL>
 **/
@MappedSuperclass
public class ManualIdEntity extends AbstractEntity {
    @Id
    public Long id;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
