package vn.vnpt.oneiot.core.jpa.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import vn.vnpt.oneiot.core.generic.entity.AbstractEntity;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

/**
 * Created by huyvv
 * Date: 16/01/2020
 * Time: 11:00 AM
 * for all issues, contact me: <EMAIL>
 **/
@MappedSuperclass
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IdEntity extends AbstractEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
