package vn.vnpt.oneiot.core.jpa.entity;

import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;

/**
 * Created by huyvv
 * Date: 10/02/2020
 * Time: 11:30 AM
 * for all issues, contact me: <EMAIL>
 **/
@MappedSuperclass
public class MultipleAbstractEntity {
    private Long created;
    private Long updated;
    private String createdBy;
    private String updatedBy;
    private Integer active;
    private String tenantId;

    public Long getCreated() {
        return created;
    }

    public void setCreated(Long created) {
        this.created = created;
    }

    public Long getUpdated() {
        return updated;
    }

    public void setUpdated(Long updated) {
        this.updated = updated;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Integer getActive() {
        return active;
    }

    public void setActive(Integer active) {
        this.active = active;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}
