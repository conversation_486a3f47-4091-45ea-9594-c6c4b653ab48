package vn.vnpt.oneiot.core.jpa.entity;

import vn.vnpt.oneiot.core.generic.entity.AbstractEntity;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

/**
 * Created by huyvv
 * Date: 16/01/2020
 * Time: 11:00 AM
 * for all issues, contact me: <EMAIL>
 **/
@MappedSuperclass
public class NamedEntity extends AbstractEntity {
    @Id
    @Column(name = "name")
    private String id;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
