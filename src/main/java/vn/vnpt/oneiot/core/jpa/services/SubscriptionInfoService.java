package vn.vnpt.oneiot.core.jpa.services;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.base.constants.AMQPConstant;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.event.EventBus;

@Service
public class SubscriptionInfoService {
    private static Logger logger = LoggerFactory.getLogger(SubscriptionInfoService.class);
    protected String routingKey = "api." + AMQPConstant.CHARGING_SERVICE + ".serviceSubscription";

    @Autowired
    EventBus eventBus;

    public void deleteBySubscriptionId(String subscriptionId) {
        String query = "subscriptionId==" + subscriptionId;
        Event event = new Event();
        event.type = AMQPConstant.EVENTTYPE_REQUEST;
        event.method = AMQPConstant.DatabaseMethod.DELETE_BY_CONDITION;
        event.payload = query;
        MessageProperties messageProperties = new MessageProperties();
        messageProperties.setReplyTo(AMQPConstant.ROUTING_KEY_INTERNAL_AUTHORIZATION);
        event = eventBus.publish(AMQPConstant.getExchangeFromRoutingKey(routingKey), routingKey, event, messageProperties);
        logger.info("delete subscriptionInfo with event: {}", event);
    }


}
