package vn.vnpt.oneiot.core.jpa.services;


import com.github.tennaito.rsql.jpa.JpaPredicateVisitor;
import cz.jirutka.rsql.parser.RSQLParser;
import cz.jirutka.rsql.parser.RSQLParserException;
import cz.jirutka.rsql.parser.ast.Node;
import cz.jirutka.rsql.parser.ast.RSQLVisitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;
import vn.vnpt.oneiot.base.constants.Constants;
import vn.vnpt.oneiot.base.errors.ErrorKey;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.core.jpa.entity.IdEntity;
import vn.vnpt.oneiot.base.errors.RemoveSystemEntityException;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.core.api.nbi.models.ErrorInfo;
import vn.vnpt.oneiot.core.api.nbi.models.IdInfo;
import vn.vnpt.oneiot.core.api.nbi.models.PageInfo;
import vn.vnpt.oneiot.core.api.nbi.models.SearchInfo;
import vn.vnpt.oneiot.core.jpa.repositories.CustomJpaRepository;
import vn.vnpt.oneiot.base.rsql.CustomRsqlVisitor;
import vn.vnpt.oneiot.core.utils.ErrorUtils;
import vn.vnpt.oneiot.core.utils.SecurityUtils;

import javax.persistence.EntityManager;
import javax.persistence.EntityNotFoundException;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import javax.transaction.Transactional;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.function.Function;

/**
 * Created by huyvv
 * Date: 16/01/2020
 * Time: 11:00 AM
 * for all issues, contact me: <EMAIL>
 **/
@Transactional
@SuppressWarnings({"Duplicates", "unchecked"})
public class CrudService<T extends IdEntity, ID extends Serializable> {
    private static Logger logger = LoggerFactory.getLogger(CrudService.class);
    protected CustomJpaRepository<T,ID> repository;
    private final Class<T> typeParameterClass;
    @PersistenceContext
    private EntityManager em;

    public CrudService(Class<T> typeParameterClass) {
        this.typeParameterClass = typeParameterClass;
    }

    public Event process(Event event){
        switch (event.method){
            case Constants.Method.CREATE:
                return processCreate(event);
            case Constants.Method.UPDATE:
                return processUpdate(event);
            case Constants.Method.DELETE:
                return processDelete(event);
            case Constants.Method.BATCH_DELETE:
                return processBatchDelete(event);
            case Constants.Method.ACTIVE:
                return processActive(event);
            case Constants.Method.DE_ACTIVE:
                return processDeActive(event);
            case Constants.Method.GET_ONE:
                return processGetOne(event);
            case Constants.Method.SEARCH:
                return processSearch(event);
            case Constants.Method.SEARCH_ONE_FIELD:
                return processSearchOneFiled(event);
            case Constants.Method.DELETE_BY_CONDITION:
                return deleteByCondition(event);
            default:
                event.statusCode = ResponseStatusCode.NOT_IMPLEMENTED.intValue();
                return event;
        }
    }

    protected Event processCreate(Event event){
        T entity = ObjectMapperUtil.objectMapper(event.payload, typeParameterClass);
        event.payload = ObjectMapperUtil.toJsonString(create(entity));
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processUpdate(Event event){
        T entity = ObjectMapperUtil.objectMapper(event.payload, typeParameterClass);
        event.payload = ObjectMapperUtil.toJsonString(update((ID) entity.getId(), entity));
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processDelete(Event event){
        IdInfo idInfo = ObjectMapperUtil.objectMapper(event.payload, IdInfo.class);
        T entity = get((ID) idInfo.getId());
        //if entity created by system, can not remove
        if(Constants.SYSTEM.equalsIgnoreCase(entity.getCreatedBy())){
            return ErrorUtils.handleErrorResponse(ResponseStatusCode.OPERATION_NOT_ALLOWED.intValue(), event, ErrorKey.CommonErrorKey.REMOVE_SYSTEM_ENTITY);
        }
        softDelete((ID) idInfo.getId());
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processBatchDelete(Event event) {
        List<Long> ids = ObjectMapperUtil.listMapper(event.payload, Long.class);
        List<Long> fail = new ArrayList<>();
        for(Long id : ids){
            try{
                softDelete((ID) id);
            }catch (Exception ex){
                logger.error(ex.getMessage(), ex);
                fail.add(id);
            }
        }
        event.payload = ObjectMapperUtil.toJsonString(fail);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processActive(Event event){
        IdInfo idInfo = ObjectMapperUtil.objectMapper(event.payload, IdInfo.class);
        activate((ID) idInfo.getId());
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processDeActive(Event event){
        IdInfo idInfo = ObjectMapperUtil.objectMapper(event.payload, IdInfo.class);
        deactivate((ID) idInfo.getId());
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processGetOne(Event event){
        IdInfo idInfo = ObjectMapperUtil.objectMapper(event.payload, IdInfo.class);
        event.payload = ObjectMapperUtil.toJsonString(get((ID) idInfo.getId()));
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processSearch(Event event){
        SearchInfo searchInfo = ObjectMapperUtil.objectMapper(event.payload, SearchInfo.class);
        String orders = searchInfo.getOrders();
        Pageable pageable;
        if(orders == null || "".equals(orders)){
            pageable = PageRequest.of(searchInfo.getPageNumber(), searchInfo.getPageSize());
        }else {
            pageable = PageRequest.of
                    (searchInfo.getPageNumber(), searchInfo.getPageSize(), vn.vnpt.oneiot.base.utils.StringUtils.toSort(orders));
        }
        Page<T> page = search(searchInfo.getQuery(), pageable);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(page.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(page.getContent()));

        event.payload = ObjectMapperUtil.toJsonString(pageInfo);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processSearchOneFiled(Event event) {
        SearchInfo searchInfo = ObjectMapperUtil.objectMapper(event.payload, SearchInfo.class);
        String query = searchInfo.getQuery();
        Node rootNode = new RSQLParser().parse(query);
        // Create criteria and from
        CriteriaBuilder builder = em.getCriteriaBuilder();
        CriteriaQuery<T> criteria = builder.createQuery(typeParameterClass);
        Root<T> root = criteria.from(typeParameterClass);
        RSQLVisitor<Predicate, EntityManager> visitor = new JpaPredicateVisitor<>().defineRoot(root);
        Predicate predicate = rootNode.accept(visitor, em);
        criteria = criteria.where(predicate);
        criteria = criteria.select(root.get(searchInfo.getResultField())).groupBy(root.get(searchInfo.getResultField()));
        TypedQuery<T> typedQuery = em.createQuery(criteria);
        if(searchInfo.getPageSize() != 0){
            typedQuery.setFirstResult(searchInfo.getPageNumber()*searchInfo.getPageSize()).setMaxResults(searchInfo.getPageSize());
        }
        if (!StringUtils.isEmpty(searchInfo.getOrders())) {
            String[] oderArr = searchInfo.getOrders().split(":");
            if(Sort.Direction.ASC.name().equalsIgnoreCase(oderArr[1])) {
                criteria.orderBy(builder.asc(root.get(oderArr[0])));
            } else {
                criteria.orderBy(builder.desc(root.get(oderArr[0])));
            }
        }

        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(repository.count(rootNode.accept(new CustomRsqlVisitor<T>())));
        pageInfo.setData(ObjectMapperUtil.toJsonString(typedQuery.getResultList()));
        event.payload = ObjectMapperUtil.toJsonString(pageInfo);
        event.statusCode = ResponseStatusCode.OK.intValue();
        logger.info(ObjectMapperUtil.toJsonString(event.payload));
        return event;
    }

    protected Event deleteByCondition(Event event) {
        String query = event.payload;
        Node rootNode = new RSQLParser().parse(query);
        // Create criteria and from
        CriteriaBuilder builder = em.getCriteriaBuilder();
        CriteriaDelete<T> criteria = builder.createCriteriaDelete(typeParameterClass);
        Root root = criteria.from(typeParameterClass);
        RSQLVisitor<Predicate, EntityManager> visitor = new JpaPredicateVisitor<>().defineRoot(root);
        Predicate predicate = rootNode.accept(visitor, em);
        criteria = criteria.where(predicate);
        int count = em.createQuery(criteria).executeUpdate();
        event.payload = ObjectMapperUtil.toJsonString(count);
        event.statusCode = ResponseStatusCode.OK.intValue();
        logger.info(ObjectMapperUtil.toJsonString(event.payload));
        return event;
    }

    public T get(ID id) {
        return repository.findById(id).orElse(null);
    }

    public List<T> findAll() {
        return repository.findAll();
    }

    public Page<T> findAll(Pageable pageable) {
        return repository.findAll(pageable);
    }

    public List<T> search(String query) {
        if(StringUtils.isEmpty(query)){
            return repository.findAll();
        }
        Node rootNode = new RSQLParser().parse(query);
        Specification<T> spec = rootNode.accept(new CustomRsqlVisitor<T>());
        return repository.findAll(spec);
    }

    public Page<T> search(String query, Pageable pageable) {
        query = addMultipleTenantQuery(query);
        return searchByQuery(query, pageable);
    }

    public Page<T> searchByQuery(String query, Pageable pageable){
        if(StringUtils.isEmpty(query)){
            return repository.findAll(pageable);
        }
        try {
            Node rootNode = new RSQLParser().parse(query);
            Specification<T> spec = rootNode.accept(new CustomRsqlVisitor<T>());
            return repository.findAll(spec, pageable);
        } catch(RSQLParserException pe) {
            logger.error("SEARCH FAIL: {}",query);
            logger.error(pe.getMessage(), pe);
            return emptyPage();
        } catch (Exception e) {
            logger.error("SEARCH FAIL: {}",query);
            logger.error(e.getMessage(), e);
            return emptyPage();
        }
    }

    protected String addMultipleTenantQuery(String query){
        //do something for change query here
        return query;
    }

    public T create(T entity) {
        beforeCreate(entity);
        repository.save(entity);
        afterCreate(entity);
        return entity;
    }

    public T update(ID id, T entity) {
        beforeUpdate(entity);
        T old = get(id);
        if(entity.getCreated() == null) entity.setCreated(old.getCreated());
        if(entity.getCreatedBy() == null) entity.setCreatedBy(old.getCreatedBy());
        if(old == null) {
            throw new EntityNotFoundException("No entity with id " + id);
        }
        repository.save(entity);
        afterUpdate(old,entity);
        return entity;
    }

    public void delete(T entity) {
        if(entity.getCreatedBy()!= null && entity.getCreatedBy().equals(Constants.SYSTEM)){
            throw new RemoveSystemEntityException();
        }
        beforeDelete(entity);
        repository.delete(entity);
        afterDelete(entity);
    }

    public void deleteById(ID id) {
        T entity = get(id);
        if(entity.getCreatedBy()!= null && entity.getCreatedBy().equals(Constants.SYSTEM)){
            throw new RemoveSystemEntityException();
        }
        delete(entity);
    }

    public void softDelete(ID id){
        T entity = get(id);
        if(entity.getCreatedBy()!= null && entity.getCreatedBy().equals(Constants.SYSTEM)){
            throw new RemoveSystemEntityException();
        }
        beforeSoftDelete(entity);
        repository.save(entity);
        afterSoftDelete(entity);
    }

    public Long count() {
        return repository.count();
    }

    public void batchDelete(List<ID> ids) {
        for(ID id : ids) {
            deleteById(id);
        }
    }

    protected void beforeCreate(T entity) {
        entity.setCreated(System.currentTimeMillis());
        if(entity.getCreatedBy() == null) {
            String currentUsername = SecurityUtils.getCurrentUserLogin();
            entity.setCreatedBy(currentUsername);
        }
        if(entity.getActive() == null) {
            entity.setActive(Constants.EntityStatus.ACTIVE);
        }
    }

    protected void afterCreate(T entity) {
        //do something after create
    }

    protected void beforeUpdate(T entity) {
        entity.setUpdated(System.currentTimeMillis());
        entity.setUpdatedBy(SecurityUtils.getCurrentUserLogin());
        if(entity.getActive() == null) {
            entity.setActive(Constants.EntityStatus.ACTIVE);
        }
    }

    protected void afterUpdate(T old, T updated) {
        //do something after update
    }

    protected void beforeDelete(T entity) {
        //do something before delete
    }

    protected void afterDelete(T entity) {
        //do something after delete
    }

    protected void beforeSoftDelete(T entity){
        entity.setActive(Constants.EntityStatus.DELETED);
        entity.setUpdated(System.currentTimeMillis());
        entity.setUpdatedBy(SecurityUtils.getCurrentUserLogin());
    }

    protected void afterSoftDelete(T entity){
        //do something after soft delete
    }

    public Page<T> emptyPage() {
        return new Page<T>() {
            @Override
            public int getTotalPages() {
                return 0;
            }

            @Override
            public long getTotalElements() {
                return 0;
            }

            @Override
            public <U> Page<U> map(Function<? super T, ? extends U> function) {
                return null;
            }

            @Override
            public int getNumber() {
                return 0;
            }

            @Override
            public int getSize() {
                return 0;
            }

            @Override
            public int getNumberOfElements() {
                return 0;
            }

            @Override
            public List<T> getContent() {
                return new ArrayList<>();
            }

            @Override
            public boolean hasContent() {
                return false;
            }

            @Override
            public Sort getSort() {
                return null;
            }

            @Override
            public boolean isFirst() {
                return false;
            }

            @Override
            public boolean isLast() {
                return false;
            }

            @Override
            public boolean hasNext() {
                return false;
            }

            @Override
            public boolean hasPrevious() {
                return false;
            }

            @Override
            public Pageable nextPageable() {
                return null;
            }

            @Override
            public Pageable previousPageable() {
                return null;
            }

            @Override
            public Iterator<T> iterator() {
                return null;
            }
        };
    }

    public void activate(ID id) {
        T t = repository.findById(id).orElse(null);
        if(t != null) {
            t.setActive(Constants.EntityStatus.ACTIVE);
            update(id, t);
        }
    }

    public void deactivate(ID id) {
        T t = repository.findById(id).orElse(null);
        if(t != null) {
            t.setActive(Constants.EntityStatus.IN_ACTIVE);
            update(id, t);
        }
    }
}
