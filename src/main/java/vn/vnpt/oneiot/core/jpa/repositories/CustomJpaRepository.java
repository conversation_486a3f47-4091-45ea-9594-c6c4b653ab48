package vn.vnpt.oneiot.core.jpa.repositories;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;
import java.io.Serializable;

/**
 * Created by huyvv
 * Date: 16/01/2020
 * Time: 11:00 AM
 * for all issues, contact me: <EMAIL>
 **/
@NoRepositoryBean
public interface CustomJpaRepository<T, ID extends Serializable> extends JpaRepository<T,ID>, JpaSpecificationExecutor<T> {
}
