package vn.vnpt.oneiot.core.jpa.services;

import org.springframework.stereotype.Component;
import vn.vnpt.oneiot.core.mongo.entity.UserEntity;

/**
 * Created by huyvv
 * Date: 16/01/2020
 * Time: 2:21 PM
 * for all issues, contact me: <EMAIL>
 **/
@Component
public class PasswordAuthProvider implements AuthenticationProvider{
    @Override
    public UserEntity authenticate(String email, String password) {
        return null;
    }
}
