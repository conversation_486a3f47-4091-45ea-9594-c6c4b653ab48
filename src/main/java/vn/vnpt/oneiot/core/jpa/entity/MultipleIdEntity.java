package vn.vnpt.oneiot.core.jpa.entity;

import com.fasterxml.jackson.annotation.JsonInclude;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

/**
 * Created by huyvv
 * Date: 10/02/2020
 * Time: 11:31 AM
 * for all issues, contact me: <EMAIL>
 **/
@MappedSuperclass
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MultipleIdEntity extends MultipleAbstractEntity{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
