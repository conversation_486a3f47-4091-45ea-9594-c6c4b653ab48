package vn.vnpt.oneiot.core.redis.dto;

import vn.vnpt.oneiot.common.entities.TokenEntity;
import vn.vnpt.oneiot.common.resource.RequestPrimitive;
import vn.vnpt.oneiot.common.resource.ResponsePrimitive;

import java.io.Serializable;

public class Transaction implements Serializable, Cloneable {
    RequestPrimitive requestPrimitive;
    ResponsePrimitive responsePrimitive;
    String replyToRoutingKey;
    int state;
    int type;
    String eventId;
    String holderId;
    TokenEntity accessToken;
    TokenEntity refreshToken;


    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public RequestPrimitive getRequestPrimitive() {
        return requestPrimitive;
    }

    public void setRequestPrimitive(RequestPrimitive requestPrimitive) {
        this.requestPrimitive = requestPrimitive;
    }

    public ResponsePrimitive getResponsePrimitive() {
        return responsePrimitive;
    }

    public void setResponsePrimitive(ResponsePrimitive responsePrimitive) {
        this.responsePrimitive = responsePrimitive;
    }

    public String getReplyToRoutingKey() {
        return replyToRoutingKey;
    }

    public void setReplyToRoutingKey(String replyToRoutingKey) {
        this.replyToRoutingKey = replyToRoutingKey;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getHolderId() {
        return holderId;
    }

    public void setHolderId(String holderId) {
        this.holderId = holderId;
    }

    public TokenEntity getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(TokenEntity accessToken) {
        this.accessToken = accessToken;
    }

    public TokenEntity getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(TokenEntity refreshToken) {
        this.refreshToken = refreshToken;
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}
