package vn.vnpt.oneiot.core.redis.listener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.RedisTemplate;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.redis.ObjectCache;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.core.api.internal.service.ConfigService;
import vn.vnpt.oneiot.core.redis.dto.Transaction;

import static vn.vnpt.oneiot.base.constants.AMQPConstant.*;
import static vn.vnpt.oneiot.core.constants.Constants.ServiceKey;
import static vn.vnpt.oneiot.core.generic.Constants.Method.REFRESH_TOKEN;

/**
 * Created by HIEUDT on 12/12/2019.
 */


public class MessageSubscriber implements MessageListener {
    private static final Logger logger = LoggerFactory.getLogger(MessageSubscriber.class);
    private ObjectCache systemCache;
    private RedisTemplate redisTemplate;
    private ConfigService configService = ConfigService.shareInstance();


    public MessageSubscriber(ObjectCache objectCache, RedisTemplate redisTemplate) {
        this.systemCache = objectCache;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void onMessage(Message message, byte[] bytes) {
        String channel = new String(message.getChannel());

        /** Expired event **/
        if (channel.endsWith(":expired")) {
            try {
                String key = (String) redisTemplate.getKeySerializer().deserialize(message.getBody());
                String arrKey[] = key.split(":");
                if (key.contains(ServiceKey)) {
                    String transactionKey = arrKey[1];
                    Transaction transaction = (Transaction) systemCache.get(transactionKey, Transaction.class);
                    if (transaction != null) {
                        Event eventResp = new Event();
                        eventResp.id = transaction.getEventId();
                        eventResp.type = EVENTTYPE_RESPONSE;
                        eventResp.method = REFRESH_TOKEN;
                        eventResp.statusCode = ResponseStatusCode.TARGET_NOT_REACHABLE.intValue();
                        eventResp.errorText = "Timeout when trying send new token to target";
                        configService.publicMess( transaction.getReplyToRoutingKey(),  eventResp);
//                        eventBus.publish(getExchangeFromRoutingKey(ROUTING_KEY_INTERNAL_AUTHORIZATION), transaction.getReplyToRoutingKey(), eventResp);
                        logger.debug("Transaction {} timeout!", eventResp.id);
                    } else {
                        logger.info("Transaction not found, transactionKey = {}", transactionKey);
                    }

                }
                // TODO: Gui yeu cau rollback toi cac module da nhan
            } catch (IndexOutOfBoundsException e) {
                logger.info("Unknown key " + message.getBody());
            }
        }
    }
}
