package vn.vnpt.oneiot.core.mongo.repositories;

import vn.vnpt.oneiot.common.entities.AccessControlOriginatorEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCustomRepository;

/**
 * Created by huyvv
 * Date: 09/03/2020
 * Time: 5:49 PM
 * for all issues, contact me: <EMAIL>
 **/
public interface AccessControlOriginatorRepository extends MgCustomRepository<AccessControlOriginatorEntity, String> {
    public AccessControlOriginatorEntity findFirstByOriginatorIDIs(String orgId);
    void deleteAllByOriginatorIDEquals(String orgId);
}
