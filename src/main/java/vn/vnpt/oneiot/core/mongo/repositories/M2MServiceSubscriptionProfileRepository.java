package vn.vnpt.oneiot.core.mongo.repositories;

import vn.vnpt.oneiot.common.entities.M2MServiceSubscriptionProfileEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCustomRepository;

public interface M2MServiceSubscriptionProfileRepository extends MgCustomRepository<M2MServiceSubscriptionProfileEntity, String> {
    M2MServiceSubscriptionProfileEntity findFirstByNameIsAndParentIDIs(String name, String parentId);
}
