package vn.vnpt.oneiot.core.mongo.services;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.common.entities.ServiceSubscribedNodeEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCrudService;
import vn.vnpt.oneiot.core.mongo.repositories.ServiceSubscribedNodeRepository;

import javax.transaction.Transactional;

@Service
@Transactional
public class ServiceSubscribedNodeService extends MgCrudService<ServiceSubscribedNodeEntity, String> {

    protected static final Logger logger = LoggerFactory.getLogger(ServiceSubscribedNodeService.class);
    private ServiceSubscribedNodeRepository serviceSubscribedNodeRepository;

    public ServiceSubscribedNodeService(ServiceSubscribedNodeRepository repository) {
        super(ServiceSubscribedNodeEntity.class);
        logger.info("init ServiceSubscribedNodeService");
        this.repository = this.serviceSubscribedNodeRepository = repository;
    }

    public ServiceSubscribedNodeEntity getOneByParentIdAndName(String parentId, String name) {
        return serviceSubscribedNodeRepository.findFirstByNameIsAndParentIDIs(name, parentId);
    }
}
