package vn.vnpt.oneiot.core.mongo.repositories;

import vn.vnpt.oneiot.common.entities.RoleEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCustomRepository;

import java.util.List;
import java.util.Set;

/**
 * Created by huyvv
 * Date: 12/03/2020
 * Time: 2:46 PM
 * for all issues, contact me: <EMAIL>
 **/
public interface DynamicRoleRepository extends MgCustomRepository<RoleEntity, String> {
    RoleEntity findFirstByResourceID(String id);
    RoleEntity findFirstByRoleIdIs(String roleId);
    RoleEntity findFirstByRoleIdIsAndHolderIs(String roleId, String holder);
    List<RoleEntity> findAllByRoleIdInAndHolderIn(List<String> roleIds, List<String> holder);
    void deleteAllByHierarchicalURIStartsWith(String hierarchicalWithSlash);
    RoleEntity findFirstByRoleName(String name);

    RoleEntity findFirstByRoleId(String roleId);
    RoleEntity  findFirstByNameAndTenantId(String name, String tenantId);
    void deleteAllByTenantIdIn(Set<String> ids);

    List<RoleEntity> findAllByRoleIdIn(List<String> rolesId);
}
