package vn.vnpt.oneiot.core.mongo.services;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.base.constants.AMQPConstant;
import vn.vnpt.oneiot.base.event.EventBus;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.common.exceptions.PlatformException;
import vn.vnpt.oneiot.core.api.internal.service.ConfigService;
import vn.vnpt.oneiot.core.api.nbi.controller.RelationController;
import vn.vnpt.oneiot.core.api.nbi.models.SearchInfo;
import vn.vnpt.oneiot.core.api.nbi.models.request.AddTopoRequest;
import vn.vnpt.oneiot.core.api.nbi.models.request.AeStatusRequest;
import vn.vnpt.oneiot.core.api.nbi.models.request.GetListTopoRequest;
import vn.vnpt.oneiot.core.api.nbi.models.response.*;
import vn.vnpt.oneiot.core.generic.dto.CheckRelationRequest;
import vn.vnpt.oneiot.core.generic.dto.CheckRelationResponse;
import vn.vnpt.oneiot.core.jpa.GetListRelationByListNameAndTypeRequest;
import vn.vnpt.oneiot.core.generic.entity.UpdateRelationNameRequest;
import vn.vnpt.oneiot.core.mongo.entity.*;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.core.generic.Constants;
import vn.vnpt.oneiot.core.mongo.generic.MgCrudAdminService;
import vn.vnpt.oneiot.core.mongo.pageable.OffsetBasedPageRequest;
import vn.vnpt.oneiot.core.mongo.repositories.IdentityRepository;
import vn.vnpt.oneiot.core.mongo.repositories.RelationRepository;
import vn.vnpt.oneiot.core.mongo.repositories.RelationtypeRepository;
import vn.vnpt.oneiot.core.mongo.repositories.RoleByRelationRepository;


import javax.transaction.Transactional;
import java.util.*;

import static vn.vnpt.oneiot.base.constants.AMQPConstant.*;
import static vn.vnpt.oneiot.base.constants.Constants.EntityStatus.ACTIVE;
import static vn.vnpt.oneiot.base.constants.Constants.EntityStatus.REGISTER;
import static vn.vnpt.oneiot.core.generic.Constants.IDENTITY_SUB_TYPE.*;
import static vn.vnpt.oneiot.core.generic.Constants.IDENTITY_TYPE.*;
import static vn.vnpt.oneiot.core.generic.Constants.RELATION_TYPE.PARENT;

/**
 * Created by huyvv
 * Date: 16/01/2020
 * Time: 11:00 AM
 * for all issues, contact me: <EMAIL>
 **/
@Service
@Transactional
public class RelationService extends MgCrudAdminService<RelationEntity, String> {
    @SuppressWarnings("unused")
    private static Logger logger = LoggerFactory.getLogger(RelationService.class);
    private RelationRepository relationRepository;

    private static String deviceStatusRoutingKey = Constants.ROUTING_KEY_DEVICE_STATUS;

    @Autowired
    ConfigService configService;

    private IdentityRepository identityRepository;

    private Map<String, RelationTypeEntity> mapRelationType = null;

    RelationtypeRepository relationtypeRepository;

    @Autowired
    public void setRelationtypeRepository(RelationtypeRepository relationtypeRepository) {
        this.relationtypeRepository = relationtypeRepository;
    }

    @Autowired
    EventBus eventBus;

    @Autowired
    public void setIdentityRepository(IdentityRepository identityRepository) {
        this.identityRepository = identityRepository;
    }

    public RelationService(RelationRepository relationRepository, RoleByRelationRepository roleByRelationRepository) {
        super(RelationEntity.class);
        this.repository = this.relationRepository = relationRepository;
    }

    @Override
    public Event process(Event event) {
        event = super.process(event);

        switch (event.method) {
            case RelationController
                    .Method.SEARCH_DEVICE_TO_ADD_INTO_GROUP_OF_APP:
                RelationController.SearchDeviceToAddGroupRequest searchDeviceToAddGroupRequest = ObjectMapperUtil.objectMapper(
                        event.payload, RelationController.SearchDeviceToAddGroupRequest.class);
                String likeDevice = "1=1";
                if (StringUtils.isNotEmpty(searchDeviceToAddGroupRequest.deviceName)) {
                    likeDevice = "targetName LIKE '" + searchDeviceToAddGroupRequest.deviceName + "%'";
                }

                event.payload = new Gson().toJson(this.relationRepository.getDeviceNameToAddGroup(likeDevice, searchDeviceToAddGroupRequest.appId, searchDeviceToAddGroupRequest.groupName));
                event.statusCode = ResponseStatusCode.OK.intValue();
                break;
            case Constants.Method.CHECK_RELATION:
                CheckRelationRequest checkRelationRequest = ObjectMapperUtil.objectMapper(event.payload, CheckRelationRequest.class);
                List<RelationEntity> entityRelationList;
                if (checkRelationRequest.getTypeRes() != null) {
                    entityRelationList = this.relationRepository
                            .getAllByOrigitorTypeAndOriginatorIdAndTargetTypeOrTargetTypeAndTargetIdAndOrigitorType
                                    (checkRelationRequest.getTypeReq(), checkRelationRequest.getId(),
                                            checkRelationRequest.getTypeRes(), checkRelationRequest.getTypeReq(),
                                            checkRelationRequest.getId(), checkRelationRequest.getTypeRes());
                } else {
                    entityRelationList = this.relationRepository
                            .getAllByOrigitorTypeAndOriginatorIdOrTargetTypeAndTargetId(
                                    checkRelationRequest.getTypeReq(), checkRelationRequest.getId(),
                                    checkRelationRequest.getTypeReq(), checkRelationRequest.getId());
                }
                CheckRelationResponse checkRelationResponse = new CheckRelationResponse();
                checkRelationResponse.setRelationList(entityRelationList);

                event.payload = ObjectMapperUtil.toJsonString(checkRelationResponse);
                event.statusCode = ResponseStatusCode.OK.intValue();
                break;
            case Constants.Method.UPDATE_NAME:
                UpdateRelationNameRequest updateNameRequest = ObjectMapperUtil.objectMapper(event.payload, UpdateRelationNameRequest.class);
                String stringQuery = "";
                stringQuery = "{ '" + "origitor_name" + "': '" + updateNameRequest.getOldName() + "', '" + "origitor_type" + "': '" + updateNameRequest.getType() + "' , '" + "active" + "': 1 }";
                Update updateOrigitor = new Update();
                updateOrigitor.set("origitor_name", "updateNameRequest.getNewName()");
                updateDataUsingNativeQuery(stringQuery, updateOrigitor);
                stringQuery = "{ '" + "target_name" + "': '" + updateNameRequest.getOldName() + "', '" + "target_type" + "': '" + updateNameRequest.getType() + "' , '" + "active" + "': 1 }";
                Update updateTarget= new Update();
                updateDataUsingNativeQuery(stringQuery, updateTarget);
                event.statusCode = ResponseStatusCode.OK.intValue();
                break;
            case Constants.Method.GET_LIST_RELATION_BY_LIST_NAME_AND_TYPE:
                GetListRelationByListNameAndTypeRequest getListRequest = ObjectMapperUtil.objectMapper(event.payload, GetListRelationByListNameAndTypeRequest.class);
                List<RelationEntity> relationEntities = this.relationRepository.getListByListNameAndType(getListRequest.getListName(), getListRequest.getType());
                event.payload = ObjectMapperUtil.toJsonString(relationEntities);
                event.statusCode = ResponseStatusCode.OK.intValue();
                break;
            case vn.vnpt.oneiot.core.generic.Constants.Method.ADD_TOPO_GATEWAY:
                event = processAddTopoGateway(event);
                break;
            case vn.vnpt.oneiot.core.generic.Constants.Method.GET_LIST_TOPO:
                event = processGetListTopo(event);
                break;
            case vn.vnpt.oneiot.core.generic.Constants.Method.DELETE_TOPO:
                event = processDeleteTopo(event);
                break;
            case vn.vnpt.oneiot.core.generic.Constants.Method.SEARCH_TOPO:
                event = processSearchTopo(event);
                break;
        }
        return event;
    }

    private Event processAddTopoGateway(Event event){
        AddTopoRequest addTopoRequest = ObjectMapperUtil.objectMapper(event.payload,AddTopoRequest.class);
        // check device co phai la gateway hay khong
        String gatewayId = addTopoRequest.getGatewayId();
        IdentityEntity gateway = identityRepository.findFirstById(gatewayId);
        if(gateway == null || !gateway.getSubType().equals(GATE_WAY_TYPE)) {
            throw new PlatformException(gatewayId + " is not a gateway device", ResponseStatusCode.BAD_REQUEST);
        }
        AddTopoResponse response = new AddTopoResponse();
        // check đã tạo relation chưa
        List<RelationEntity> existedTopo = relationRepository.findAllByRelationTypeAndTargetIdIn(PARENT, addTopoRequest.getSubList());
        if (existedTopo != null && existedTopo.size() > 0) {
            throw new PlatformException(existedTopo.get(0).getTargetId() + " is in other topo", ResponseStatusCode.BAD_REQUEST);
        }
        List<IdentityEntity> subDeviceEntities = identityRepository.findAllByIdIn(addTopoRequest.getSubList());
        List<RelationEntity> relationEntities = new ArrayList<>();
        for (IdentityEntity subDeviceEntity : subDeviceEntities) {
            if(subDeviceEntity.getType().equals(DEVICE_IDENTITY_TYPE) && subDeviceEntity.getSubType().equals(SUB_DEVICE_TYPE)) {
                relationEntities.add(createRelationByIdentityNotSave(gateway, subDeviceEntity, PARENT));
                AddTopoResponse.SubDevice subDevice = new AddTopoResponse.SubDevice();
                subDevice.setDeviceId(subDeviceEntity.getId());
                subDevice.setDeviceName(subDeviceEntity.getName());
                response.getSubList().add(subDevice);
            } else {
                throw new PlatformException(subDeviceEntity.getId() + " is not a sub device", ResponseStatusCode.BAD_REQUEST);
            }
        }
        // Save relation
        relationRepository.saveAll(relationEntities);
        response.setTotal(Long.valueOf(relationEntities.size()));
        event = setResultSuccess(event, ObjectMapperUtil.toJsonString(response));
        return event;
    }

    private Event processGetListTopo(Event event) {
        GetListTopoRequest getListTopoRequest = ObjectMapperUtil.objectMapper(event.payload,GetListTopoRequest.class);
        int limit, offset;
        String query = "originatorId==" + getListTopoRequest.getGatewayId();
        if (getListTopoRequest.getFromDate() != null) {
            query = query + ";created>=" + getListTopoRequest.getFromDate();
        }
        if (getListTopoRequest.getToDate() != null) {
            query = query + ";created<=" + getListTopoRequest.getToDate();
        }
        if (getListTopoRequest.getLimit() != null) {
            limit = getListTopoRequest.getLimit();
        }else {
            limit = 1000;
        }
        if (getListTopoRequest.getOffset() != null) {
            offset = getListTopoRequest.getOffset();
        }else {
            offset = 0;
        }
        Pageable pageable = new OffsetBasedPageRequest(limit, offset);
        try {
            Page<RelationEntity> relationPage = search(query, pageable);
            List<SubDeviceResponse> subDeviceList = new ArrayList<>();
            List<RelationEntity> listRelation = relationPage.getContent();
            if(listRelation.isEmpty()) {
                GetListTopoResponse response = new GetListTopoResponse();
                response.setErrorCode(ResponseStatusCode.OK.intValue());
                response.setTotal(0L);
                response.setSubList(new ArrayList<>());
                event.payload = ObjectMapperUtil.toJsonString(response);
                event.statusCode = ResponseStatusCode.OK.intValue();
                return event;
            }
            List<String> ids = new ArrayList<>();
            for (RelationEntity e: listRelation) {
                ids.add(e.getTargetId());
                SubDeviceResponse subDeviceResponse = new SubDeviceResponse();
                subDeviceResponse.setDeviceId(e.getTargetId());
                subDeviceResponse.setDeviceName(e.getTargetName());
                subDeviceResponse.setCreated(String.valueOf(e.getCreated()));
                subDeviceList.add(subDeviceResponse);
            }
            List<IdentityEntity> deviceIdentityList = identityRepository.findAllByIdIn(ids);
            List<String> finalIds = new ArrayList<>();
            deviceIdentityList.stream().forEach(e -> {
                if(e.getActive() == ACTIVE) finalIds.add(e.getId());
            });

            // build event get device status
            AeStatusRequest aeStatusRequest = new AeStatusRequest();
            aeStatusRequest.ids = finalIds;
            aeStatusRequest.type = 0;
            aeStatusRequest.from = 1;
            MessageProperties messageProperties = new MessageProperties();
            messageProperties.setReplyTo(ROUTING_KEY_API_AUTHORIZATION);
            String exchange = getExchangeFromRoutingKey(deviceStatusRoutingKey);
            Event event1 = new Event();
            event1.id = UUID.randomUUID().toString();
            event1.payload = new Gson().toJson(aeStatusRequest);
            event1.method = vn.vnpt.oneiot.core.generic.Constants.Method.GET_STATUS_AE;
            event1.type = AMQPConstant.EVENTTYPE_REQUEST;
            event1 = eventBus.publishAndReceiveSynch(exchange,deviceStatusRoutingKey, event1, messageProperties, 30000);
            List<DeviceStatus> jsonObject = new Gson().fromJson(event1.payload, new TypeToken<List<DeviceStatus>>(){}.getType());
            for(SubDeviceResponse e : subDeviceList){
                e.setState(REGISTER);
                for(DeviceStatus deviceStatus : jsonObject){
                    if(e.getDeviceId().equals(deviceStatus.deviceId)){
                        e.setState(deviceStatus.deviceStatus);
                    }
                }
            }
            GetListTopoResponse response = new GetListTopoResponse();
            response.setErrorCode(ResponseStatusCode.OK.intValue());
            response.setTotal((long) subDeviceList.size());
            response.setSubList(subDeviceList);
            event.payload = ObjectMapperUtil.toJsonString(response);
            event.statusCode = ResponseStatusCode.OK.intValue();
        } catch (Exception e) {
            e.printStackTrace();
            event.statusCode = ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue();
        }
        return event;
    }

    private Event processSearchTopo(Event event) {
        SearchInfo searchInfo = ObjectMapperUtil.objectMapper(event.payload, SearchInfo.class);
        String orders = searchInfo.getOrders();
        Pageable pageable;
        if(orders == null || "".equals(orders)){
            pageable = PageRequest.of(searchInfo.getPageNumber(), searchInfo.getPageSize());
        }else {
            pageable = PageRequest.of
                    (searchInfo.getPageNumber(), searchInfo.getPageSize(), vn.vnpt.oneiot.base.utils.StringUtils.toSort(orders));
        }
        try {
            Page<RelationEntity> relationPage = search(searchInfo.getQuery(), pageable);
            List<SubDeviceResponse> subDeviceList = new ArrayList<>();
            List<RelationEntity> listRelation = relationPage.getContent();
            if(listRelation.isEmpty()) {
                GetListTopoResponse response = new GetListTopoResponse();
                response.setErrorCode(ResponseStatusCode.OK.intValue());
                response.setTotal(0L);
                event.payload = ObjectMapperUtil.toJsonString(response);
                event.statusCode = ResponseStatusCode.OK.intValue();
                return event;
            }
            List<String> ids = new ArrayList<>();
            for (RelationEntity e: listRelation) {
                ids.add(e.getTargetId());
                SubDeviceResponse subDeviceResponse = new SubDeviceResponse();
                subDeviceResponse.setDeviceId(e.getTargetId());
                subDeviceResponse.setDeviceName(e.getTargetName());
                subDeviceResponse.setCreated(String.valueOf(e.getCreated()));
                subDeviceList.add(subDeviceResponse);
            }
            List<IdentityEntity> deviceIdentityList = identityRepository.findAllByIdIn(ids);
            List<String> finalIds = new ArrayList<>();
            deviceIdentityList.stream().forEach(e -> {
                if(e.getActive() == ACTIVE) finalIds.add(e.getId());
            });

            // build event get device status
            AeStatusRequest aeStatusRequest = new AeStatusRequest();
            aeStatusRequest.ids = finalIds;
            aeStatusRequest.type = 0;
            aeStatusRequest.from = 1;
            MessageProperties messageProperties = new MessageProperties();
            messageProperties.setReplyTo(ROUTING_KEY_API_AUTHORIZATION);
            String exchange = getExchangeFromRoutingKey(deviceStatusRoutingKey);
            Event event1 = new Event();
            event1.id = UUID.randomUUID().toString();
            event1.payload = new Gson().toJson(aeStatusRequest);
            event1.method = vn.vnpt.oneiot.core.generic.Constants.Method.GET_STATUS_AE;
            event1.type = AMQPConstant.EVENTTYPE_REQUEST;
            event1 = eventBus.publishAndReceiveSynch(exchange,deviceStatusRoutingKey, event1, messageProperties, 30000);
            List<DeviceStatus> jsonObject = new Gson().fromJson(event1.payload, new TypeToken<List<DeviceStatus>>(){}.getType());
            for(SubDeviceResponse e : subDeviceList){
                e.setState(REGISTER);
                for(DeviceStatus deviceStatus : jsonObject){
                    if(e.getDeviceId().equals(deviceStatus.deviceId)){
                        e.setState(deviceStatus.deviceStatus);
                        break;
                    }
                }
            }
            GetListTopoResponse response = new GetListTopoResponse();
            response.setErrorCode(ResponseStatusCode.OK.intValue());
            response.setTotal((long) subDeviceList.size());
            response.setSubList(subDeviceList);
            event.payload = ObjectMapperUtil.toJsonString(response);
            event.statusCode = ResponseStatusCode.OK.intValue();
        } catch (Exception e) {
            e.printStackTrace();
            event.statusCode = ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue();
        }
        return event;
    }


    private Event processDeleteTopo(Event event) {
        AddTopoRequest deleteTopoRequest = ObjectMapperUtil.objectMapper(event.payload, AddTopoRequest.class);
        // check device co phai la gateway hay khong
        String gatewayId = deleteTopoRequest.getGatewayId();
        IdentityEntity gateway = identityRepository.findFirstById(gatewayId);
        if(gateway == null || !gateway.getSubType().equals(GATE_WAY_TYPE)) {
            event.statusCode = ResponseStatusCode.BAD_REQUEST.intValue();
            event.errorText = "Gateway not found";
            return event;
        }
        relationRepository.deleteByOriginatorIdAndTargetIdInAndRelationType(
                deleteTopoRequest.getGatewayId(),deleteTopoRequest.getSubList(), PARENT);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    public RelationEntity createRelationByIdentity(IdentityEntity origin, IdentityEntity target, Integer relationType) {
        return create(createRelationByIdentityNotSave(origin, target, relationType));
    }

    public RelationEntity createRelationByIdentityNotSave(IdentityEntity origin, IdentityEntity target, Integer relationType) {
        if (origin.getType().intValue() == APP_DOMAIN_IDENTITY_TYPE || target.getType().intValue() == APP_DOMAIN_IDENTITY_TYPE) {
            throw new PlatformException("Add relation for application domain is not allowed", ResponseStatusCode.BAD_REQUEST);
        }
        if (target.getType().intValue() == APP_IDENTITY_TYPE) {
            throw new PlatformException("Add relation for application is not allowed", ResponseStatusCode.BAD_REQUEST);
        }
        if ((origin.getType().intValue() == APP_IDENTITY_TYPE && origin.getSubType().intValue() == MAIN_APP_TYPE)) {
            throw new PlatformException("Add relation for main application is not allowed", ResponseStatusCode.BAD_REQUEST);
        }
        if (!origin.getMainAppId().equals(target.getMainAppId())) {
            throw new PlatformException("Add relation with other application domain is not allow", ResponseStatusCode.BAD_REQUEST);
        }
        if (relationType.equals(PARENT) && !(origin.getType().intValue() == DEVICE_IDENTITY_TYPE && target.getType().intValue() == DEVICE_IDENTITY_TYPE
                                            && origin.getSubType().intValue() == GATE_WAY_TYPE && target.getSubType().intValue() == SUB_DEVICE_TYPE)) {
            throw new PlatformException(origin.getId() + " is not allowed to be parent of " + target.getId() + ". Only gateway is parent of sub device", ResponseStatusCode.BAD_REQUEST);
        }
        RelationEntity relationEntity = new RelationEntity();
        relationEntity.setOriginatorId(origin.getId());
        relationEntity.setOrigitorName(origin.getName());
        relationEntity.setOrigitorType(origin.getType());
        relationEntity.setTargetId(target.getId());
        relationEntity.setTargetName(target.getName());
        relationEntity.setTargetType(target.getType());
        relationEntity.setRelationType(relationType);
        beforeCreate(relationEntity);
        return relationEntity;
    }

    public List<RelationEntity> findAllByOriginatorIdAndRelationTypeAndActive(String origitorId, Integer relationType, Integer active) {
        List<RelationEntity> resp = relationRepository.findAllByOriginatorIdAndRelationTypeAndActive(origitorId,relationType, active);
        return resp;
    }

    public Map<String, RelationTypeEntity> getMapRelationType() {
        if (mapRelationType == null) {
            mapRelationType = new HashMap<>();
            List<RelationTypeEntity> relationTypeEntityList = relationtypeRepository.findAll();
            relationTypeEntityList.stream().forEach(e -> {
                mapRelationType.put(e.getName(), e);
            });
        }
        return mapRelationType;
    }

    public class DeviceStatus{
        public String deviceId;
        public Integer deviceStatus;
        public Long lastConnection;
    }
}
