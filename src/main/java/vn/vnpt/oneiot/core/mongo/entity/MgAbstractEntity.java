package vn.vnpt.oneiot.core.mongo.entity;

import org.springframework.data.mongodb.core.mapping.Document;
import vn.vnpt.oneiot.core.generic.entity.AbstractEntity;

import javax.persistence.Id;

@Document
public class MgAbstractEntity extends AbstractEntity implements Cloneable{

    @Id
    private String id;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

}
