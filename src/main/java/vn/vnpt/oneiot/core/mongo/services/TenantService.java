package vn.vnpt.oneiot.core.mongo.services;

import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.base.constants.Constants;
import vn.vnpt.oneiot.base.errors.ErrorKey;
import vn.vnpt.oneiot.base.errors.RemoveEntityOfOtherTenantException;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.common.constants.RatePlanOrderStatus;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.common.dto.RatePlanOrderDto;
import vn.vnpt.oneiot.common.utils.Util;
import vn.vnpt.oneiot.core.mongo.entity.UserEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCrudAdminService;
import vn.vnpt.oneiot.core.mongo.repositories.TenantRepository;
import vn.vnpt.oneiot.core.mongo.repositories.UserRepository;
import vn.vnpt.oneiot.core.mongo.entity.TenantEntity;
import vn.vnpt.oneiot.core.utils.ErrorUtils;
import vn.vnpt.oneiot.core.utils.SecurityUtils;

import javax.transaction.Transactional;
import java.util.*;

import static vn.vnpt.oneiot.core.constants.Constants.ROLE_TENANT_ADMIN;
import static vn.vnpt.oneiot.core.generic.Constants.Method.*;
import static vn.vnpt.oneiot.core.generic.Constants.UserType.TENANT_ADMIN;

/**
 * Created by luctq
 * Date: 11/04/2023
 * Time: 18:00 PM
 * for all issues, contact me: <EMAIL>
 **/
@Service
@Transactional
public class TenantService extends MgCrudAdminService<TenantEntity,String> {
    private static Logger logger = LoggerFactory.getLogger(TenantService.class);

    private TenantRepository tenantRepository;

    private UserService userService;
    private UserRepository userRepository;


    public TenantService(TenantRepository tenantRepository) {
        super(TenantEntity.class);
        this.repository = this.tenantRepository = tenantRepository;
    }

    @Autowired
    public void setUserRepository(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    @Autowired
    public void setUserService(UserService userService) {
        this.userService = userService;
    }

    @Override
    public Event process(Event event){
        event = super.process(event);
        switch (event.method){
            case GET_TENANT_BY_CODE:
                return getTenantByCode(event);
            case GET_TENANT_NAME:
                return getTenantName(event);
            case GET_TENANT_BY_IOT_MARKET_PLACE_UUID:
                return getTenantByIotMarketPlaceUUID(event);
            case Constants.Method.NOTIFY_SUBSCRIPTION:
                return handleNotifySubscription(event);
            default:
                return event;
        }
    }

    private Event handleNotifySubscription(Event event) {
        event.statusCode = ResponseStatusCode.OK.intValue();
        try {
            RatePlanOrderDto ratePlanOrder = Util.getInstanceGsonCustom().fromJson(event.payload, RatePlanOrderDto.class);
            TenantEntity tenantEntity = tenantRepository.findFirstByCode(ratePlanOrder.getTenantCode());
            tenantEntity.setApiFlag(ratePlanOrder.getApiFlag());
            tenantEntity.setTelemetryFlag(ratePlanOrder.getTelemetryFlag());
            if(ratePlanOrder.getStatus().equals(RatePlanOrderStatus.FINISHED)){
                tenantEntity.setFreeDeviceQuota(null);
                tenantEntity.setFreeUserAppQuota(null);
                tenantEntity.setFreeApiQuota(null);
                tenantEntity.setFreeTelemetryQuota(null);
                tenantEntity.setFreeDataVolumeQuota(null);
                tenantEntity.setStatusRatingPlan(ratePlanOrder.getStatus());
            }else {
                tenantEntity.setFreeDeviceQuota(ratePlanOrder.getFreeDeviceQuota());
                tenantEntity.setFreeUserAppQuota(ratePlanOrder.getFreeUserAppQuota());
                tenantEntity.setFreeApiQuota(ratePlanOrder.getFreeApiQuota());
                tenantEntity.setFreeTelemetryQuota(ratePlanOrder.getFreeTelemetryQuota());
                tenantEntity.setFreeDataVolumeQuota(ratePlanOrder.getFreeDataVolumeQuota());
                tenantEntity.setStatusRatingPlan(ratePlanOrder.getStatus());
            }
            tenantRepository.save(tenantEntity);
            event.statusCode = ResponseStatusCode.OK.intValue();
        }catch (Exception e){
            logger.error(e.getMessage(), e);
        }
        return event;
    }

    @Override
    protected void beforeCreate(TenantEntity entity) {
        super.beforeCreate(entity);
        //generate tenant-code
        entity.setCode("tnt-" + UUID.randomUUID().toString());
        entity.setActive(Constants.EntityStatus.REGISTER);

        //set created,createdBy
        entity.setCreatedBy(entity.getTenantAdmin().getEmail());
        entity.setCreated(System.currentTimeMillis());
        //create tenant admin
        UserEntity tenantAdmin = entity.getTenantAdmin();
        tenantAdmin.setCompanyName(entity.getCompanyName());
        tenantAdmin.setCreatedBy(tenantAdmin.getEmail());
        tenantAdmin.setCreated(System.currentTimeMillis());
        if(tenantAdmin.getFullName() == null)
            tenantAdmin.setFullName(tenantAdmin.getFirstName() + " " + tenantAdmin.getLastName());
        tenantAdmin.setType(TENANT_ADMIN);
        //add role TENANT_ADMIN for new tenant-user
        tenantAdmin.getRoleIds().add(ROLE_TENANT_ADMIN);
        try{
            tenantAdmin = userService.create(tenantAdmin);
            entity.setAdminId(tenantAdmin.getId());
            entity.setAdminName(tenantAdmin.getFullName());
            entity.setTenantAdmin(tenantAdmin);
        }catch (Exception e){
            logger.error(e.getMessage(), e);
        }
    }

    @Override
    protected void afterCreate(TenantEntity entity) {
        super.afterCreate(entity);
        //update tenant id for tenantAdmin
        UserEntity tenantAdmin = entity.getTenantAdmin();
        tenantAdmin.setTenantId(entity.getCode());
        userRepository.save(tenantAdmin);
    }

    @Override
    public String addMultipleTenantQuery(String query) {
        // replace tenantId== => code ==
        if (query != null && query.startsWith("tenantId==")) {
            return ("code==" + query.substring("tenantId==".length()));
        }
        return query;
    }

    @SuppressWarnings("Duplicates")
    @Override
    protected Event processCreate(Event event) {
        //check trung email
        TenantEntity tenantEntity = ObjectMapperUtil.objectMapper(event.payload, TenantEntity.class);
        logger.info("check mail:"+tenantEntity.getTenantAdmin().getEmail());
//        UserEntity other = userRepository.findOneByEmailIgnoreCase(tenantEntity.getTenantAdmin().getEmail());
        TenantEntity tenantByEmail = tenantRepository.findFirstByCreatedBy(tenantEntity.getTenantAdmin().getEmail());
        if(tenantByEmail != null){
            return ErrorUtils.handleErrorResponse(ResponseStatusCode.CONFLICT.intValue(), event, ErrorKey.UserErrorKey.DUPLICATE_EMAIL);
        }
        return super.processCreate(event);
    }

//    @Override
//    protected void afterSoftDelete(TenantEntity entity) {
//        super.afterSoftDelete(entity);
//        //delete all user of Tenant
//        List<UserEntity> userEntityList = userRepository.findAllByTenantId(entity.getCode());
//        for(UserEntity userEntity : userEntityList){
//            userEntity.setActive(Constants.EntityStatus.DELETED);
//            userEntity.setUpdated(System.currentTimeMillis());
//            userEntity.setUpdatedBy(SecurityUtils.getCurrentUserLogin());
//        }
//        userRepository.saveAll(userEntityList);
//    }

    //override hàm này để check các trường hợp: xóa tenant không thuộc quyền của ng đó
    @Override
    protected void beforeSoftDelete(TenantEntity entity) {
        TenantEntity defaultTenantEntity = tenantRepository.
                findFirstByNameAndCreatedBy(vn.vnpt.oneiot.core.constants.Constants.DEFAULT_TENANT, Constants.SYSTEM);

        if(!entity.getCode().equals(SecurityUtils.getTenantId())
                && !defaultTenantEntity.getCode().equals(SecurityUtils.getTenantId())){
            throw new RemoveEntityOfOtherTenantException();
        }
        super.beforeSoftDelete(entity);
    }

    //override hàm này để check các trường hợp: update tenant không thuộc quyền của ng đó
    @Override
    protected Event processUpdate(Event event) {
        TenantEntity tenantEntity = ObjectMapperUtil.objectMapper(event.payload, TenantEntity.class);
        if(!tenantEntity.getCode().equals(SecurityUtils.getTenantId())) {
            return ErrorUtils.handleErrorResponse(ResponseStatusCode.OPERATION_NOT_ALLOWED.intValue(), event, ErrorKey.TenantErrorKey.UPDATE_ENTITY_OF_OTHER_TENANT);
        }
        return super.processUpdate(event);
    }

    public Event getTenantByCode(Event event) {
        String tenantCode = event.payload;
        if(tenantCode != null && !tenantCode.isEmpty()) {
            event.payload = ObjectMapperUtil.toJsonString(tenantRepository.findFirstByCode(tenantCode));
            event.statusCode = ResponseStatusCode.OK.intValue();
            return event;
        }
        return event;
    }

    public Event getTenantName(Event event){
        List<String> tenanIds = ObjectMapperUtil.listMapper(event.payload,String.class);
        List<TenantEntity> entities = tenantRepository.findAllByCodeIn(tenanIds);
        Map<String,String> map = new HashMap<>();
        for (String id : tenanIds){
            map.put(id,"Unown");
            if (entities.size()>0){
                for (TenantEntity e : entities){
                    if (e.getCode().equals(id)){
                        map.put(id,e.getName());
                    }
                }
            }
        }
       event.payload = new Gson().toJson(map);
       event.statusCode = ResponseStatusCode.OK.intValue();
       return event;
    }

    private Event getTenantByIotMarketPlaceUUID(Event event) {
        String iotMarketPlaceUUID = event.payload;
        TenantEntity entity = tenantRepository.findFirsByIotMarketPlaceUUID(iotMarketPlaceUUID);
        if(entity != null){
            event.payload = Util.getInstanceGsonCustom().toJson(entity);
            event.statusCode = ResponseStatusCode.OK.intValue();
        }else{
            event.statusCode = ResponseStatusCode.NOT_FOUND.intValue();
        }
        return event;
    }
    
}
