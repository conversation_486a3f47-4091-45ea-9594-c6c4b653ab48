package vn.vnpt.oneiot.core.mongo.repositories;

import com.mongodb.client.result.DeleteResult;
import vn.vnpt.oneiot.common.entities.AccessControlPolicyEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCustomRepository;

import java.util.List;

/**
 * Created by huyvv
 * Date: 09/03/2020
 * Time: 5:49 PM
 * for all issues, contact me: <EMAIL>
 **/
public interface AccessControlPolicyRepository extends MgCustomRepository<AccessControlPolicyEntity, String> {
    AccessControlPolicyEntity findFirstByNameAndParentID(String name, String parentId);
    AccessControlPolicyEntity findFirstByNameEndingWith(String name);
    List<AccessControlPolicyEntity> findAllByResourceIDIn(List<String> resourceId);
    void deleteAllByHierarchicalURIStartsWith(String hierarchicalWithSlash);
    List<AccessControlPolicyEntity> findAllByHierarchicalURIStartsWith(String hierarchicalWithSlash);
}
