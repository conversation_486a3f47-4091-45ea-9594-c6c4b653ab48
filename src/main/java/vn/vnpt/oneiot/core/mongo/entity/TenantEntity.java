package vn.vnpt.oneiot.core.mongo.entity;

import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection = "BASE_TENANTS")
public class TenantEntity extends MgAbstractEntity {

    @Field
    private String code;
    @Field
    private String name;
    @Field
    private String iotMarketPlaceUUID;
    @Field
    private String adminId;
    @Field
    private String adminName;
    @Field
    private String companyName;

    @Field
    private Integer apiFlag;
    @Field
    private Integer telemetryFlag;
    @Field
    private Integer statusRatingPlan;
    @Field
    private Long freeDeviceQuota;
    @Field
    private Long freeUserAppQuota;
    @Field
    private Long freeApiQuota;
    @Field
    private Long freeTelemetryQuota;
    @Field
    private Long freeDataVolumeQuota;

    @Transient
    private UserEntity tenantAdmin;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIotMarketPlaceUUID() {
        return iotMarketPlaceUUID;
    }

    public void setIotMarketPlaceUUID(String iotMarketPlaceUUID) {
        this.iotMarketPlaceUUID = iotMarketPlaceUUID;
    }

    public String getAdminId() {
        return adminId;
    }

    public void setAdminId(String adminId) {
        this.adminId = adminId;
    }

    public String getAdminName() {
        return adminName;
    }

    public void setAdminName(String adminName) {
        this.adminName = adminName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public UserEntity getTenantAdmin() {
        return tenantAdmin;
    }

    public void setTenantAdmin(UserEntity tenantAdmin) {
        this.tenantAdmin = tenantAdmin;
    }

    public Integer getApiFlag() {
        return apiFlag;
    }

    public void setApiFlag(Integer apiFlag) {
        this.apiFlag = apiFlag;
    }

    public Integer getTelemetryFlag() {
        return telemetryFlag;
    }

    public void setTelemetryFlag(Integer telemetryFlag) {
        this.telemetryFlag = telemetryFlag;
    }

    public Long getFreeDeviceQuota() {
        return freeDeviceQuota;
    }

    public void setFreeDeviceQuota(Long freeDeviceQuota) {
        this.freeDeviceQuota = freeDeviceQuota;
    }

    public Long getFreeUserAppQuota() {
        return freeUserAppQuota;
    }

    public void setFreeUserAppQuota(Long freeUserAppQuota) {
        this.freeUserAppQuota = freeUserAppQuota;
    }

    public Long getFreeApiQuota() {
        return freeApiQuota;
    }

    public void setFreeApiQuota(Long freeApiQuota) {
        this.freeApiQuota = freeApiQuota;
    }

    public Long getFreeTelemetryQuota() {
        return freeTelemetryQuota;
    }

    public void setFreeTelemetryQuota(Long freeTelemetryQuota) {
        this.freeTelemetryQuota = freeTelemetryQuota;
    }

    public Long getFreeDataVolumeQuota() {
        return freeDataVolumeQuota;
    }

    public void setFreeDataVolumeQuota(Long freeDataVolumeQuota) {
        this.freeDataVolumeQuota = freeDataVolumeQuota;
    }

    public Integer getStatusRatingPlan() {
        return statusRatingPlan;
    }

    public void setStatusRatingPlan(Integer statusRatingPlan) {
        this.statusRatingPlan = statusRatingPlan;
    }
}
