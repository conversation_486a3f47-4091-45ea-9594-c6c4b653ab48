package vn.vnpt.oneiot.core.mongo.services;

import org.bson.BsonRegularExpression;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import vn.vnpt.oneiot.common.constants.ResourceType;
import vn.vnpt.oneiot.common.constants.ShortName;
import vn.vnpt.oneiot.common.entities.ServiceSubscribedAppRuleEntity;
import vn.vnpt.oneiot.common.entities.ServiceSubscribedNodeEntity;
import vn.vnpt.oneiot.common.resource.ServiceSubscribedAppRule;
import vn.vnpt.oneiot.core.CSEInitialize;
import vn.vnpt.oneiot.core.mongo.generic.MgCrudService;
import vn.vnpt.oneiot.core.mongo.repositories.ServiceSubscribedAppRuleRepository;

import javax.transaction.Transactional;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

@Service
@Transactional
public class ServiceSubscribedAppRuleService extends MgCrudService<ServiceSubscribedAppRuleEntity, String>{

    protected static final Logger logger = LoggerFactory.getLogger(ServiceSubscribedAppRuleService.class);

    private ServiceSubscribedAppRuleRepository serviceSubscribedAppRuleRepository;

    private MongoTemplate mongoTemplate;

    @Value("${certificateRelease}")
    boolean isCertificateReleaseVersion;

    ServiceSubscribedAppRuleService asarService;

    ServiceSubscribedNodeService svsnService;

    @Autowired
    public void setAsarService(ServiceSubscribedAppRuleService asarService) {
        this.asarService = asarService;
    }

    @Autowired
    public void setSvsnService(ServiceSubscribedNodeService svsnService) {
        this.svsnService = svsnService;
    }

    @Autowired
    public void setMongoTemplate(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public ServiceSubscribedAppRuleService(ServiceSubscribedAppRuleRepository repository) {
        super(ServiceSubscribedAppRuleEntity.class);
        logger.info("init ServiceSubscribedAppRuleService");
        this.repository = this.serviceSubscribedAppRuleRepository = repository;
    }

    @org.springframework.transaction.annotation.Transactional(propagation = Propagation.REQUIRES_NEW, noRollbackFor = Exception.class)
    public List<ServiceSubscribedAppRuleEntity> matched(String credId, String appId, String aeId, String serviceSubscribedNodeId) throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        String stringQuery = "";
        if (credId != null) {
            stringQuery = "{$expr:{$regexFind:{input:" +
                    "\"" + credId + "\"" +
                    ", regex:\"$applicableCredIDs\"}}}";
        }
        if (appId != null) {
            if (!stringQuery.isEmpty()) stringQuery = stringQuery + ",";
            stringQuery = stringQuery + "{$expr:{$regexFind:{input:" +
                    "\"" + appId + "\"" +
                    ", regex:\"$allowedAppIDs\"}}}";
        }
        if (aeId != null && !aeId.equals("S") && !aeId.equals("C")) {
            if (!stringQuery.isEmpty()) stringQuery = stringQuery + ",";
            stringQuery = stringQuery + "{$expr:{$regexFind:{input:" +
                    "\"" + aeId + "\"" +
                    ", regex:\"$allowedAEs\"}}}";
        } else {
            if (aeId == null) aeId = "";
            if (!stringQuery.isEmpty()) stringQuery = stringQuery + ",";
            stringQuery = stringQuery + "{$expr:{ $regexMatch: { input: \"$allowedAEsString\", regex: /\\^" + aeId + ".*\\*/}}}";
        }
        if (!stringQuery.isEmpty()) {
            stringQuery = "{$and:[" + stringQuery + "," + "{serviceSubscribedNodeLinks:" +
                    "\"" + serviceSubscribedNodeId + "\"}]}";
        }

        Document query = Document.parse(stringQuery);
        Method doFind = MongoTemplate.class.getDeclaredMethod("doFind", String.class, Document.class,Document.class,Class.class);
        doFind.setAccessible(true);
        List<ServiceSubscribedAppRuleEntity> asarList = (List<ServiceSubscribedAppRuleEntity>) doFind.invoke(mongoTemplate,
                mongoTemplate.getCollectionName(ServiceSubscribedAppRuleEntity.class), query, new Document(), ServiceSubscribedAppRuleEntity.class);
        if (asarList != null && asarList.size() > 0) return asarList;
        return null;
    }

    public ServiceSubscribedAppRuleEntity getOneByParentIdAndName(String parentId, String name) {
        return serviceSubscribedAppRuleRepository.findFirstByNameIsAndParentIDIs(name, parentId);
    }

    public void provisionedAppRuleForAE(String aeid) {
        /** Generate service subscribed app rule **/
        if (isCertificateReleaseVersion) {
            ServiceSubscribedAppRuleEntity asarEntity = asarService.getOneByParentIdAndName("/" + vn.vnpt.oneiot.common.constants.Constants.CSE_ID,
                    ShortName.ASAR + vn.vnpt.oneiot.common.constants.Constants.PREFIX_SEPERATOR + aeid);
            if (asarEntity == null) {
                asarEntity = new ServiceSubscribedAppRuleEntity();
                asarEntity.setName(ShortName.ASAR + vn.vnpt.oneiot.common.constants.Constants.PREFIX_SEPERATOR + aeid);
                asarEntity.setParentID("/" + vn.vnpt.oneiot.common.constants.Constants.CSE_ID);
                asarEntity.setHierarchicalURI(vn.vnpt.oneiot.common.constants.Constants.CSE_NAME + "/" + asarEntity.getName());
                asarEntity.setResourceType(ResourceType.SERVICE_SUBSCRIBED_APP_RULE);
            }
            asarEntity.setAllowedAEs(new BsonRegularExpression("^" + aeid + "$"));
            asarEntity.setAllowedAEsString(asarEntity.getAllowedAEs().getPattern());
            asarEntity.setAllowedAppIDs(new BsonRegularExpression(".*"));
            asarEntity.setApplicableCredIDs(new BsonRegularExpression("^None$"));
            asarEntity.setServiceSubscribedNodeLinks(Arrays.asList(new String[] {CSEInitialize.csebaseSvsnId}));
            if (asarEntity.getResourceID() == null) {
                asarEntity = asarService.create(asarEntity);
            } else {
                asarEntity = asarService.update(asarEntity);
            }

            ServiceSubscribedNodeEntity svsnEntity = svsnService.get(CSEInitialize.csebaseSvsnId);
            if (svsnEntity.getRuleLinks() != null) svsnEntity.getRuleLinks().add(asarEntity.getResourceID());
            else svsnEntity.setRuleLinks(Arrays.asList(new String[] {asarEntity.getResourceID()}));
            svsnEntity = svsnService.update(svsnEntity);
        }
    }
}
