package vn.vnpt.oneiot.core.mongo.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;


@Document(collection = "ROLEBYRELATIONENTITY")
public class RoleByRelationEntity {
    @Id
    String roleId;
    @Field
    String originatorAeid;
    @DBRef(lazy = true)
    RelationTypeEntity relationTypeEntity;
    @Field
    String roleName;

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getOriginatorAeid() {
        return originatorAeid;
    }

    public void setOriginatorAeid(String originatorAeid) {
        this.originatorAeid = originatorAeid;
    }

    public RelationTypeEntity getRelationTypeEntity() {
        return relationTypeEntity;
    }

    public void setRelationTypeEntity(RelationTypeEntity relationTypeEntity) {
        this.relationTypeEntity = relationTypeEntity;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }
}
