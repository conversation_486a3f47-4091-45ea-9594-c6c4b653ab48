package vn.vnpt.oneiot.core.mongo.repositories;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.core.mongo.entity.MappingAcpIdsResourceEntity;

import java.util.List;

@Service
public interface MappingAcpIdsResourceRepository extends MongoRepository<MappingAcpIdsResourceEntity, String> {
    public MappingAcpIdsResourceEntity findFirstByResourceIdIs(String resourceId);
    public List<MappingAcpIdsResourceRepository> findAllByResourceIdIn(List<String> resourceIds);
    void deleteAllByHuriIsOrHuriStartingWith(String huri, String huriWithSlash);
}
