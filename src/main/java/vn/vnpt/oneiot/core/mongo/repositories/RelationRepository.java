package vn.vnpt.oneiot.core.mongo.repositories;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.mongodb.repository.Query;
import vn.vnpt.oneiot.core.mongo.entity.RelationEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCustomRepository;

import java.util.List;

public interface RelationRepository extends MgCustomRepository<RelationEntity,String> {

    @Query(value = "{'targetName': {$regex : ?0,$options: 'i'},'originatorId' : ?1 , 'targetType' : ?2}")
    List<RelationEntity> getAllRelationByTargetName(String regex,String appId,int type);
    RelationEntity getEntityRelationById(String id);
    List<RelationEntity> getEntityRelationsByOriginatorIdAndOrigitorType(Long id, String type);

    List<RelationEntity> getEntityRelationsByTargetIdAndTargetType(Long id, String type);

    //    List<RelationEntity> getEntityRelationsByOriginatorIdAndOrigitorTypeAndDirection(Long id, String type, String direction);
    void deleteAllByOrigitorTypeAndOriginatorIdIn(String type, List<Long> ids);

    void deleteAllByTargetTypeAndTargetIdIn(String type, List<Long> ids);

    void deleteAllByOrigitorTypeAndOriginatorIdIs(String type, Long id);

//    void deleteAllByTargetTypeAndTargetIdIs(String type, Long id);

    List<RelationEntity> getAllByOrigitorTypeAndOriginatorIdAndTargetTypeOrTargetTypeAndTargetIdAndOrigitorType(String oriType, Long oriId, String tarType, String targetType, Long TarId, String origiType);

    List<RelationEntity> getAllByOrigitorTypeAndOriginatorIdOrTargetTypeAndTargetId(String oriType, Long oriId, String targetType, Long TarId);

    @Query(value = "{$and: [{target_type: 'Device'}, {target_name: {$regex: ?0, $options: 'i'}}, {$or: [{origitor_id: ?1}, {origitor_name: ?2}]}]}, " +
            "{$group: {_id: '$target_name', count: {$sum: {$cond: [{$eq: ['$origitor_type', 'App']}, 1, 0]}}}}, " +
            "{$match: {count: 1}}, " +
            "{$project: {_id: 0, target_name: '$_id'}}",
            count = true)
    List<String> getDeviceNameToAddGroup(String likeDevice, String appId, String groupName);

    @Query("{'active': true, 'origitorName': ?2, 'origitorType': ?3}")
    @Modifying
    void updateNameRelationByOrigitor(String newName, String oldName, String type);

    @Query("{active: ?1, targetName: ?2, targetType: ?3}")
    @Modifying
    void updateNameRelationByTarget(String newName, String oldName, String type);

    @Query("{$and: [ { active: true }, { $or: [ { targetName: { $in: ?0 }, targetType: ?1 }, { origitorName: { $in: ?0 }, origitorType: ?1 } ] } ] }")
    List<RelationEntity> getListByListNameAndType(List<String> listName, String type);

    @Query("{$and: [ { active: 1 }, { $or: [ { target_uuid: ?0, target_type: ?1 }, { origitor_uuid: ?0, origitor_type: ?1 } ] } ] }")
    List<RelationEntity> getRelationByUuidAndType(String uuid, String type);

    List<RelationEntity> findAllByOriginatorIdAndRelationTypeAndActive(String originatorId, int relationType, int active);

    List<RelationEntity> findAllByRelationTypeAndActiveAndTargetId(int relationType, int active, String targetId);

    List<RelationEntity> findAllByRelationTypeAndTargetIdIn(int relationType, List<String> targetIds);

    void deleteByOriginatorIdAndTargetIdInAndRelationType(String originatorId, List<String> targetId, int relationType);

    List<RelationEntity> getEntityRelationByOriginatorIdAndTargetTypeAndActive(String id , int type, int status);

    List<RelationEntity> findAllByOriginatorIdAndTargetId(String oriId, String targetId);

    List<RelationEntity> findAllByOriginatorIdAndTargetIdInAndRelationType(String oriId, List<String> targetIds, int relationType);

    RelationEntity findFirstByTargetIdAndRelationType(String targetId, int relationType);

    List<RelationEntity> findAllByOriginatorIdAndTargetIdIn(String oriId, List<String> targetId);
}