package vn.vnpt.oneiot.core.mongo.repositories;

import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.common.entities.TokenEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCustomRepository;

import java.util.List;

/**
 * Author: kiendt
 * Date: 4/17/2020
 * Contact: <EMAIL>
 */
@Service
public interface TokenRepository extends MgCustomRepository<TokenEntity, String> {
    TokenEntity findFirstByTokenIdAndActive(String tokenId, Integer active);
    TokenEntity findFirstByTokenId(String tokenId);
    TokenEntity findFirstByHolderIsAndActiveIs(String holder, Integer active);
    TokenEntity findFirstByHolder(String holder);
    TokenEntity findFirstByTokenObject(String tokenObject);
    TokenEntity findFirstByExtensionIs(String tokenExtension);
    TokenEntity findFirstByTokenObjectIsAndUserNameIs(String tokenObject, String userName);
    TokenEntity findFirstByTokenObjectIsAndHolderIsAndActiveIs(String tokenObject, String holder, Integer active);
    void deleteAllByHierarchicalURIStartsWith(String hierarchicalWithSlash);
    List<TokenEntity> findAllByHolderIsAndActiveIsAndUserNameIs(String holder, Integer active, String userName );
    List<TokenEntity> findAllByHolderIs(String holder);
    List<TokenEntity> findAllByHolderInAndActive(List<String> holders, Integer active);
    TokenEntity findFirstByAuthCode(String authCode);
    TokenEntity findFirstByAuthCodeStartingWithAndActive(String authCode, Integer active);
    TokenEntity findFirstByUserNameIsAndActiveIs(String userName, Integer active, Sort sort);
}
