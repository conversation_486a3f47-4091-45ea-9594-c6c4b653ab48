package vn.vnpt.oneiot.core.mongo.services;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.common.constants.Constants;
import vn.vnpt.oneiot.common.constants.ResourceType;
import vn.vnpt.oneiot.common.entities.AccessControlPolicyEntity;
import vn.vnpt.oneiot.common.entities.M2MServiceSubscriptionProfileEntity;
import vn.vnpt.oneiot.common.utils.ResourceUtils;
import vn.vnpt.oneiot.core.CSEInitialize;
import vn.vnpt.oneiot.core.mongo.generic.MgCrudService;
import vn.vnpt.oneiot.core.mongo.repositories.AccessControlPolicyRepository;
import vn.vnpt.oneiot.core.mongo.repositories.M2MServiceSubscriptionProfileRepository;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class M2MServiceSubscriptionProfileService extends MgCrudService<M2MServiceSubscriptionProfileEntity, String> {

    protected static final Logger logger = LoggerFactory.getLogger(M2MServiceSubscriptionProfileService.class);

    private M2MServiceSubscriptionProfileRepository m2MServiceSubscriptionProfileRepository;
    private AccessControlPolicyRepository accessControlPolicyRepository;

    @Autowired
    public void setAccessControlPolicyRepository(AccessControlPolicyRepository accessControlPolicyRepository) {
        this.accessControlPolicyRepository = accessControlPolicyRepository;
    }

    @Autowired
    public M2MServiceSubscriptionProfileService(M2MServiceSubscriptionProfileRepository repository) {
        super(M2MServiceSubscriptionProfileEntity.class);
        logger.info("init M2MServiceSubscriptionProfileService");
        this.repository = this.m2MServiceSubscriptionProfileRepository = repository;
    }

    public M2MServiceSubscriptionProfileEntity createBaseMssp() {
        M2MServiceSubscriptionProfileEntity msspEntity = m2MServiceSubscriptionProfileRepository.findFirstByNameIsAndParentIDIs(Constants.MSSP_BASE_NAME, "/" + Constants.CSE_ID);
        if (msspEntity != null) {
            logger.info("M2M Service subscription profile " + Constants.MSSP_BASE_NAME + " is existed");
            return msspEntity;
        }
        M2MServiceSubscriptionProfileEntity subscriptionProfileEntity = new M2MServiceSubscriptionProfileEntity();
        subscriptionProfileEntity.setResourceID(ResourceUtils.generateResourceId(ResourceType.M2M_SERVICE_SUBSCRIPTION_PROFILE));
        subscriptionProfileEntity.setName(Constants.MSSP_BASE_NAME);
        subscriptionProfileEntity.setResourceType(ResourceType.M2M_SERVICE_SUBSCRIPTION_PROFILE);
        subscriptionProfileEntity.setParentID("/" + Constants.CSE_ID);
        subscriptionProfileEntity.setHierarchicalURI(Constants.CSE_NAME + "/" + subscriptionProfileEntity.getName());
        if (CSEInitialize.acpAdminId != null) {
            List<String> acpIds = new ArrayList<>();
            acpIds.add(CSEInitialize.acpAdminId);
            subscriptionProfileEntity.setAccessControlPolicyIds(acpIds);
        }
        return this.repository.save(subscriptionProfileEntity);
    }

    public M2MServiceSubscriptionProfileEntity getOneByParentIdAndName(String parentId, String name) {
        return m2MServiceSubscriptionProfileRepository.findFirstByNameIsAndParentIDIs(name, parentId);
    }
}
