package vn.vnpt.oneiot.core.mongo.entity;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

//Entity dinh nghia thong tin cho viec khai bao app cho ben thu 3 uy quyen call api  (permission dich vu)
@Document(collection = "BASE_APPLICATIONS")
public class Application extends MgAbstractEntity{
    @Field
    private String name;
    @Field
    private String clientId;
    @Field
    private String secretId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getSecretId() {
        return secretId;
    }

    public void setSecretId(String secretId) {
        this.secretId = secretId;
    }
}
