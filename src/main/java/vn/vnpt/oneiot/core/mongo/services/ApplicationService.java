package vn.vnpt.oneiot.core.mongo.services;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.base.constants.Constants;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.core.mongo.entity.Application;
import vn.vnpt.oneiot.core.mongo.entity.TenantEntity;
import vn.vnpt.oneiot.core.mongo.entity.UserEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCrudAdminService;
import vn.vnpt.oneiot.core.mongo.repositories.ApplicationRepository;

import javax.transaction.Transactional;
import java.util.UUID;



@Service
@Transactional
public class ApplicationService extends MgCrudAdminService<Application,String> {

    private static Logger logger = LoggerFactory.getLogger(ApplicationService.class);

    private ApplicationRepository applicationRepository;

    public ApplicationService(ApplicationRepository applicationRepository) {
        super(Application.class);
        this.repository = this.applicationRepository = applicationRepository;
    }


    @Override
    protected void beforeCreate(Application entity) {
        super.beforeCreate(entity);
        //generate clientId - SecretId
        entity.setClientId( UUID.randomUUID().toString());
        entity.setSecretId( UUID.randomUUID().toString());
        entity.setActive(Constants.EntityStatus.REGISTER);
        entity.setCreated(System.currentTimeMillis());
    }

}
