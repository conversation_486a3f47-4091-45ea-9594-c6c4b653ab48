package vn.vnpt.oneiot.core.mongo.generic;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.support.DefaultConversionService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.core.query.Query;
import vn.vnpt.oneiot.base.rsql.mongo.ComparisonToCriteriaConverter;
import vn.vnpt.oneiot.base.rsql.mongo.RsqlMongoAdapter;

import javax.persistence.EntityNotFoundException;
import javax.transaction.Transactional;
import java.util.List;

/**
 * Created by huyvv
 * Date: 06/03/2020
 * Time: 11:13 PM
 * for all issues, contact me: <EMAIL>
 **/

@Transactional
@SuppressWarnings({"Duplicates", "unchecked"})
public class MgNoneCrudService<T, ID>{
    private static Logger logger = LoggerFactory.getLogger(MgNoneCrudService.class);
    protected MgCustomRepository<T,ID> repository;
    protected final Class<T> typeEntityClass;

    public MgNoneCrudService(Class<T> typeEntityClass) {
        logger.info(typeEntityClass == null ? " MgNoneCrudService nul" : typeEntityClass.getName());
        this.typeEntityClass = typeEntityClass;
    }

    public Class<T> getTypeEntityClass() {
        return typeEntityClass;
    }

    @Autowired
    private MongoOperations mongoOperations;

    @Autowired
    private MongoMappingContext mongoMappingContext;

    public String addMultipleTenantQuery(String query){
        return query;
    }

    /**
     * function create, update, ...
     **/

    public T create(T entity){
        beforeCreate(entity);
        repository.save(entity);
        afterCreate(entity);
        return entity;
    }

    public T get(ID id){
        return repository.findById(id).orElse(null);
    }

    public Page<T> search(String query, Pageable pageable){
        if(pageable == null){
            pageable = PageRequest.of(0, 20);
        }
        pageable =PageRequest.of(0,20);
        query = addMultipleTenantQuery(query);
        ComparisonToCriteriaConverter converter = new ComparisonToCriteriaConverter(new DefaultConversionService(), mongoMappingContext);
        RsqlMongoAdapter adapter = new RsqlMongoAdapter(converter);
        Query queryMongo;
        if(query == null || query.isEmpty()){
            queryMongo = new Query().with(pageable);
        }else{
            queryMongo = Query.query(adapter.getCriteria(query, typeEntityClass)).with(pageable);
        }

        long total = mongoOperations.count(queryMongo, typeEntityClass);
        List<T> list = mongoOperations.find(queryMongo, typeEntityClass);
        return new PageImpl<>(list, pageable, total);
    }

    public long count(String query){
        ComparisonToCriteriaConverter converter = new ComparisonToCriteriaConverter(new DefaultConversionService(), mongoMappingContext);
        RsqlMongoAdapter adapter = new RsqlMongoAdapter(converter);
        Query queryMongo;
        if(query == null || query.isEmpty()){
            return repository.count();
        }else{
            queryMongo = Query.query(adapter.getCriteria(query, typeEntityClass));
            return mongoOperations.count(queryMongo, typeEntityClass);
        }
    }

    public List<T> findByQuery(Query query){
        return mongoOperations.find(query, typeEntityClass);
    }

    public T update(ID id, T entity){
        beforeUpdate(entity);
        T old = get(id);
        if(old == null) {
            throw new EntityNotFoundException("No entity with id " + id);
        }
        entity = repository.save(entity);
        afterUpdate(old,entity);
        return entity;
    }

    public void delete(ID id){
        T entity = get(id);
        beforeDelete(entity);
        repository.delete(entity);
        afterDelete(entity);
    }

    public void softDelete(ID id){
        T entity = get(id);
        beforeSoftDelete(entity);
        repository.save(entity);
        afterSoftDelete(entity);
    }

    public void deleteAllByList(List<T> entitys){
        beforeDeleteAll(entitys);
        repository.deleteAll(entitys);
        afterDeleteAll(entitys);
    }

    /**
     * functions before, after
     **/
    protected void beforeCreate(T entity) {
        //do something
    }

    protected void afterCreate(T entity) {
        //do something after create
    }

    protected void beforeUpdate(T entity) {
        //do something
    }

    protected void afterUpdate(T old, T updated) {
        //do something after update
    }

    protected void beforeDelete(T entity) {
        //do something before delete
    }

    protected void afterDelete(T entity) {
        //do something after delete
    }

    protected void beforeSoftDelete(T entity){
        //do something
    }

    protected void afterSoftDelete(T entity){
        //do something after soft delete
    }

    protected void beforeDeleteAll(List<T> entitys){

    }

    protected void afterDeleteAll(List<T> entitys){

    }
}
