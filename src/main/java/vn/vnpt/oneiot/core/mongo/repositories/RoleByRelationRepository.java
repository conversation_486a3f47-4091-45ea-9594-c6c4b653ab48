package vn.vnpt.oneiot.core.mongo.repositories;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.core.mongo.entity.RoleByRelationEntity;

@Service
public interface RoleByRelationRepository extends MongoRepository<RoleByRelationEntity, String> {
    RoleByRelationEntity findFirstByOriginatorAeidAndAndRelationTypeEntityName(String orginatorId, String relationType);
}
