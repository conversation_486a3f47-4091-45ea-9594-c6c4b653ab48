package vn.vnpt.oneiot.core.mongo.services;

import com.google.common.base.Strings;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.base.constants.AMQPConstant;
import vn.vnpt.oneiot.base.constants.Constants;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.event.EventBus;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.common.constants.Operation;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.common.entities.DeviceInfoEntity;
import vn.vnpt.oneiot.common.entities.NodeEntity;
import vn.vnpt.oneiot.common.entities.TokenEntity;
import vn.vnpt.oneiot.common.exceptions.BadRequestException;
import vn.vnpt.oneiot.common.exceptions.ConflictException;
import vn.vnpt.oneiot.common.exceptions.PlatformException;
import vn.vnpt.oneiot.common.exceptions.ResourceNotFoundException;
import vn.vnpt.oneiot.common.resource.RequestPrimitive;
import vn.vnpt.oneiot.common.resource.ResponsePrimitive;
import vn.vnpt.oneiot.core.api.nbi.models.*;
import vn.vnpt.oneiot.core.api.nbi.models.request.*;
import vn.vnpt.oneiot.core.api.nbi.models.response.*;
import vn.vnpt.oneiot.core.generic.InitialDataLoader;
import vn.vnpt.oneiot.core.generic.dto.GetListGroupResponse;
import vn.vnpt.oneiot.core.generic.dto.GroupRequest;
import vn.vnpt.oneiot.core.mongo.entity.IdentityEntity;
import vn.vnpt.oneiot.core.mongo.entity.RelationEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCrudAdminService;
import vn.vnpt.oneiot.core.mongo.repositories.IdentityRepository;
import vn.vnpt.oneiot.core.mongo.repositories.RelationRepository;
import vn.vnpt.oneiot.core.mongo.repositories.TenantRepository;
import vn.vnpt.oneiot.core.mongo.repositories.TokenRepository;
import vn.vnpt.oneiot.core.utils.SecurityUtils;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

import static vn.vnpt.oneiot.base.constants.AMQPConstant.*;
import static vn.vnpt.oneiot.base.constants.Constants.EntityStatus.*;
import static vn.vnpt.oneiot.core.generic.Constants.APP.*;
import static vn.vnpt.oneiot.core.generic.Constants.APP_DOMAIN.AUTHORITY_TYPE.NON_REGISTRERED;
import static vn.vnpt.oneiot.core.generic.Constants.GROUP.*;
import static vn.vnpt.oneiot.core.generic.Constants.IDENTITY_SUB_TYPE.*;
import static vn.vnpt.oneiot.core.generic.Constants.IDENTITY_TYPE.*;
import static vn.vnpt.oneiot.core.generic.Constants.Method.*;
import static vn.vnpt.oneiot.core.generic.Constants.RELATION_TYPE.OWNER;
import static vn.vnpt.oneiot.core.generic.Constants.RELATION_TYPE.PARENT;


@Service
@Transactional
public class IdentityService extends MgCrudAdminService<IdentityEntity, String> {

    private static Logger logger = LoggerFactory.getLogger(IdentityService.class);

    private static String deviceStatusRoutingKey = vn.vnpt.oneiot.core.generic.Constants.ROUTING_KEY_DEVICE_STATUS;

    private static String deviceRoutingKeyM2M = ROUTING_KEY_ONEM2M_PRIMITIVE_DEVICEMGMT;

    private IdentityRepository identityRepository;

    @Autowired
    private RelationRepository relationRepository;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private TokenRepository tokenRepository;

    @Autowired
    TenantRepository tenantRepository;

    @Autowired
    RelationService relationService;

    @Autowired
    CategoryService categoryService;

    @Autowired
    MongoTemplate mongoTemplate;

    @Autowired
    EventBus eventBus;

    InitialDataLoader initialDataLoader;

    @Autowired
    public void setInitialDataLoader(InitialDataLoader initialDataLoader) {
        this.initialDataLoader = initialDataLoader;
    }

    public IdentityService(IdentityRepository identityRepository) {
        super(IdentityEntity.class);
        this.repository = this.identityRepository = identityRepository;
    }

    public Event process(Event event, Integer identity_type) {
        if (identity_type == null) return process(event);
        switch (identity_type) {
            case DEVICE_IDENTITY_TYPE:
                return processDevice(event);
            case APP_IDENTITY_TYPE:
                return processApp(event);
            case APP_DOMAIN_IDENTITY_TYPE:
                return processAppDomain(event);
            case GROUP_IDENTITY_TYPE:
                return processGroup(event);
            default:
                return process(event);
        }
    }

    private Event processDevice(Event event) {
        switch (event.method) {
            case vn.vnpt.oneiot.core.generic.Constants.Method.CREATE_DEVICE:
                return processCreateDeviceEx(event);
            case Constants.Method.CREATE:
                return processCreateDevice(event);
            case vn.vnpt.oneiot.core.generic.Constants.Method.GET_DEVICE_BY_ID:
            case Constants.Method.GET_ONE:
                return processGetDeviceById(event);
            case vn.vnpt.oneiot.core.generic.Constants.Method.SEARCH_DEVICE:
            case Constants.Method.SEARCH:
                return processSearchDevice(event);
            case vn.vnpt.oneiot.core.generic.Constants.Method.DELETE_DEVICE:
            case Constants.Method.DELETE:
                return processDelete(event);
            case vn.vnpt.oneiot.core.generic.Constants.Method.GET_DEVICE_BY_NAME:
                return processGetDeviceByName(event);
            case vn.vnpt.oneiot.core.generic.Constants.Method.GET_LIST_GATEWAY_BY_APPDOMAIN:
                return processGetListGatewayByAppDomain(event);
            case vn.vnpt.oneiot.core.generic.Constants.Method.BULK_REGISTER_DEVICE:
                return processBulkRegisterDevice(event);
            case  vn.vnpt.oneiot.core.generic.Constants.Method.GET_ALL_CATEGORIES:
                return categoryService.processGetAll(event);
            case  vn.vnpt.oneiot.core.generic.Constants.Method.SEARCH_SUB_DEVICE_TO_BIND:
                return processSearchSubDeviceToBind(event);
            case  vn.vnpt.oneiot.core.generic.Constants.Method.GET_DEVICE_BY_DEVICEID_AND_USERID:
                return processGetDeviceByDeviceIdAndUserId(event);
            case  vn.vnpt.oneiot.core.generic.Constants.Method.GET_DEVICE_LIST:
                return processGetDeviceList(event);
            case  vn.vnpt.oneiot.core.generic.Constants.Method.GET_DEVICE_INFO:
                return processGetDeviceInfo(event);
            case  vn.vnpt.oneiot.core.generic.Constants.Method.GET_DEVICE_DETAIL_INFO:
                return processGetDeviceDetailInfo(event);
            case  COUNT_DEVICE_BY_TENANT:
                return countDeviceByTenant(event);
            default:
                return super.process(event);
        }
    }

    private Event processGetDeviceByDeviceIdAndUserId(Event event) {
        StringIdInfo idInfo = new Gson().fromJson(event.payload, StringIdInfo.class);
        IdentityEntity deviceIdentity = identityRepository.findFirstByIdAndUserId(idInfo.getToken(),idInfo.getId());
        event = setResultSuccess(event, ObjectMapperUtil.toJsonString(convertToDeviceModel(deviceIdentity)));
        return event;
    }

    private Event processSearchSubDeviceToBind(Event event) {
        SearchSubDeviceRequest searchSubDeviceRequest = ObjectMapperUtil.objectMapper(event.payload, SearchSubDeviceRequest.class);
        IdentityEntity appDomain = identityRepository.findFirstByIdAndType(searchSubDeviceRequest.getAppDomainId(), APP_DOMAIN_IDENTITY_TYPE);
        AggregationOperation matchStage = Aggregation.match(Criteria.where("type").is(DEVICE_IDENTITY_TYPE).and("subType").is(SUB_DEVICE_TYPE).and("mainAppId").is(appDomain.getMainAppId())
                .and("active").nin(DELETED, IN_ACTIVE));
        AggregationOperation lookupStage = Aggregation.lookup("BASE_RELATION_ENTITY", "_id", "targetId", "matched_relation");
        AggregationOperation secondMatchStage = Aggregation.match(
                new Criteria().orOperator(
                        Criteria.where("matched_relation.relationType").ne(PARENT),
                        Criteria.where("matched_relation").is(new ArrayList<>())
                )
        );
        AggregationOperation caseInsensitiveSort = Aggregation.project()
                .andInclude("name").andInclude("_id")
                .andExpression("{'$toLower': '$name'}").as("lowerName");
        AggregationOperation sort = Aggregation.sort(Sort.Direction.ASC,"lowerName");
        AggregationOperation skipStage = Aggregation.skip(searchSubDeviceRequest.getOffset());
        AggregationOperation limitStage = Aggregation.limit(searchSubDeviceRequest.getLimit());
        Aggregation aggregation = Aggregation.newAggregation(matchStage, lookupStage, secondMatchStage,caseInsensitiveSort, sort,  skipStage, limitStage);
        List<IdentityEntity> identityEntities =  mongoTemplate.aggregate(aggregation, "IDENTITY", IdentityEntity.class).getMappedResults();
        setResultSuccess(event,ObjectMapperUtil.toJsonString(identityEntities.stream().map(this::convertToDeviceModel).collect(Collectors.toList())));
        return event;
    }

    private Event processBulkRegisterDevice(Event event) {
        List<Device> devices = ObjectMapperUtil.listMapper(event.payload, Device.class);
        List<Device> inValidDevices = new ArrayList<>();
        List<Device> validDevices = new ArrayList<>();
        validDevices.addAll(devices);
        List<IdentityEntity> deviceIdentites = new ArrayList<>();
        // tim ds domains roi cho vao map
        List<IdentityEntity> domains = identityRepository.findAllByTenantIdAndTypeAndActiveIn(devices.get(0).tenantId, APP_DOMAIN_IDENTITY_TYPE, Arrays.asList(ACTIVE,REGISTER));
        Map<String, IdentityEntity> domainMap = new HashMap<>();
        domains.stream().forEach(e -> domainMap.put(e.getName(),e));
        Map<String, Device> nameToDevice = new HashMap<>();
        for (Device d : validDevices) {
            nameToDevice.put(d.deviceName, d);
        }
        // Query device da ton tai theo name
        List<IdentityEntity> existedDevices = identityRepository.findAllByNameInAndTypeIn(nameToDevice.keySet(), Arrays.asList(new Integer[]{APP_IDENTITY_TYPE, DEVICE_IDENTITY_TYPE}));
        if (existedDevices != null) {
            for (IdentityEntity exit : existedDevices) {
                if(nameToDevice.get(exit.getName()).note == null)
                    nameToDevice.get(exit.getName()).note= "Existed Device Name, ";
                else
                    nameToDevice.get(exit.getName()).note+= "Existed Device Name, ";
                inValidDevices.add(nameToDevice.get(exit.getName()));
            }
        }
        validDevices.removeAll(inValidDevices);
        // Tao danh sach identity
        for (Device d : validDevices) {
            IdentityEntity entity = convertFromDeviceModel(d);
            entity.setId(generateDeviceId());
            IdentityEntity domainEntity = domainMap.get(d.appDomainName);
            if(domainMap == null) {
                if(d.note == null) {
                    d.note = "The app domain not existed, ";
                }else {
                    d.note += "The app domain not existed, ";
                }
                inValidDevices.add(d);
                continue;
            }else {
                entity.setDomain(domainEntity);
                entity.setMainAppId(domainEntity.getMainAppId());
            }
            entity.setCreated(System.currentTimeMillis());
            entity.setUpdated(entity.getCreated());
            if(entity.getCreatedBy() == null) {
                String currentUsername = SecurityUtils.getCurrentUserLogin();
                entity.setCreatedBy(currentUsername);
            }
            if (entity.getUpdatedBy() == null) {
                entity.setUpdatedBy(entity.getCreatedBy());
            }
            entity.setActive(REGISTER);
            deviceIdentites.add(entity);
        }

        // TODO: tao token
        long beforeCreateToken = System.currentTimeMillis();
        if(!deviceIdentites.isEmpty()) {
            Map<String, TokenEntity> accessTokens = tokenService.createTokenBulkForDevice(deviceIdentites);
            System.out.println("time create token:" + (System.currentTimeMillis() - beforeCreateToken));

            for (IdentityEntity identityEntity : deviceIdentites) {
                // set Expiry
                identityEntity.setExpiry(accessTokens.get(identityEntity.getId()).getNotAfter());
            }
            long beforeCreateDevice = System.currentTimeMillis();
            identityRepository.saveAll(deviceIdentites);
            System.out.println("time create device :" + (System.currentTimeMillis() - beforeCreateDevice));
        }
        BulkRegisterResponse response = new BulkRegisterResponse();
        response.setInvalidDevices(inValidDevices);
        response.setTotalRecord(devices.size());
        event.statusCode = ResponseStatusCode.OK.intValue();
        event.payload = ObjectMapperUtil.toJsonString(response);
        return event;
    }

    private Event processGetListGatewayByAppDomain(Event event) {
        StringIdInfo stringIdInfo = ObjectMapperUtil.objectMapper(event.payload, StringIdInfo.class);
        ListGatewayResponse response = new ListGatewayResponse();
        IdentityEntity appDomain = identityRepository.findFirstByIdAndType(stringIdInfo.getId(), APP_DOMAIN_IDENTITY_TYPE);
        Query query = new Query();
        query.with(Sort.by(Sort.Direction.ASC, "name"));
        query.collation(Collation.of("en").strength(Collation.ComparisonLevel.secondary()));
        query.addCriteria(
                new Criteria().andOperator(
                        Criteria.where("active").in(Arrays.asList(ACTIVE,REGISTER)),
                        Criteria.where("mainAppId").is(appDomain.getMainAppId()),
                        Criteria.where("type").is(DEVICE_IDENTITY_TYPE),
                        Criteria.where("subType").is(GATE_WAY_TYPE))
        );
        List<IdentityEntity> gateways = mongoTemplate.find(query, IdentityEntity.class);
        for (IdentityEntity e : gateways) {
            GatewayResponse gatewayResponse = new GatewayResponse();
            gatewayResponse.setId(e.getId());
            gatewayResponse.setName(e.getName());
            response.getGatewayResponseList().add(gatewayResponse);
        }
        event = setResultSuccess(event, ObjectMapperUtil.toJsonString(response));
        return event;
    }

    private Event processGetDeviceByName(Event event) {
        String deviceName = event.payload;
        List<IdentityEntity> deviceIdentitys = identityRepository.findByNameAndType(deviceName, DEVICE_IDENTITY_TYPE);
        List<Device> list = deviceIdentitys.stream().map(this::convertToDeviceModel).collect(Collectors.toList());
        event = setResultSuccess(event, ObjectMapperUtil.toJsonString(list));
        return event;
    }

    private Page<IdentityEntity> processSearchInfo(String searchInfoStr, Integer identity_type) {
        SearchInfo searchInfo = ObjectMapperUtil.objectMapper(searchInfoStr, SearchInfo.class);
        String orders = searchInfo.getOrders();
        Pageable pageable;
        if (orders == null || "".equals(orders)) {
            pageable = PageRequest.of(searchInfo.getPageNumber(), searchInfo.getPageSize());
        } else {
            pageable = PageRequest.of
                    (searchInfo.getPageNumber(), searchInfo.getPageSize(), vn.vnpt.oneiot.base.utils.StringUtils.toSort(orders));
        }
        String query = String.format("type==%d" + ";active!=%d" + ";active!=%d;%s",identity_type,IN_ACTIVE,DELETED,searchInfo.getQuery() == null? "" : searchInfo.getQuery());
        searchInfo.setQuery(query);
        Page<IdentityEntity> page = search(searchInfo.getQuery(), pageable);
        return page;
    }


    private Event processSearchDevice(Event event) {
        Page<IdentityEntity> identityEntities = processSearchInfo(event.payload, DEVICE_IDENTITY_TYPE);
        List<Device> list = identityEntities.stream().map(this::convertToDeviceModel).collect(Collectors.toList());
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(identityEntities.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(list));
        event.payload = ObjectMapperUtil.toJsonString(pageInfo);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    private Event processSearchApplicationDomain(Event event) {
        Page<IdentityEntity> identityEntities = processSearchInfo(event.payload, APP_DOMAIN_IDENTITY_TYPE);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(identityEntities.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(identityEntities.stream().map(this::convertIdentityToAplicationDomain).collect(Collectors.toList())));
        event.payload = ObjectMapperUtil.toJsonString(pageInfo);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    private Event processSearchApplication(Event event) {
        Page<IdentityEntity> identityEntities = processSearchInfo(event.payload, APP_IDENTITY_TYPE);
        if (identityEntities == null || identityEntities.getContent() == null || identityEntities.getContent().isEmpty()) {
            PageInfo pageInfo = new PageInfo();
            pageInfo.setTotalCount(0);
            pageInfo.setData(null);
            event.payload = ObjectMapperUtil.toJsonString(pageInfo);
            event.statusCode = ResponseStatusCode.OK.intValue();
            return event;
        }
        List<Application> applications = new ArrayList<>();
        HashMap<String, IdentityEntity> mainAppIdToAppDomain = new HashMap<>();
        /** Get ds appDomain thuoc ve tenant**/
        List<IdentityEntity> allAppDomainOfTenant;
        if (!event.payload.contains("tenantId==")) {
            allAppDomainOfTenant = identityRepository.findByType(APP_DOMAIN_IDENTITY_TYPE);
        } else
        {
            allAppDomainOfTenant = identityRepository.findAllByTenantIdAndType(identityEntities.getContent().get(0).getTenantId(), APP_DOMAIN_IDENTITY_TYPE);
        }
        if (!allAppDomainOfTenant.isEmpty()) {
            for (IdentityEntity domain : allAppDomainOfTenant)
                mainAppIdToAppDomain.put(domain.getMainAppId(), domain);
        }
        for (IdentityEntity entity : identityEntities) {
             applications.add(convertIdentityToAplication(entity, mainAppIdToAppDomain.get(entity.getMainAppId())));
        }
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(identityEntities.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(applications));
        event.payload = ObjectMapperUtil.toJsonString(pageInfo);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }


    private Event processGetDeviceById(Event event) {
        StringIdInfo idInfo = ObjectMapperUtil.objectMapper(event.payload, StringIdInfo.class);
        IdentityEntity deviceIdentity = get(idInfo.getId());
        if (deviceIdentity == null) {
            event.payload = null;
            event.statusCode = ResponseStatusCode.NOT_FOUND.intValue();
            event.errorText = vn.vnpt.oneiot.core.constants.Constants.ResultMessage.DEVICE_NOT_FOUND;
            return event;
        }
        List<TokenEntity> tokenEntities = tokenRepository.findAllByHolderIs(deviceIdentity.getId());
        if (tokenEntities != null) {
            for (TokenEntity tokenEntity : tokenEntities) {
                if (tokenEntity.getActive().equals(IN_ACTIVE)) deviceIdentity.setRefreshToken(tokenEntity.getTokenObject());
                if (tokenEntity.getActive().equals(ACTIVE)) deviceIdentity.setAccessToken(tokenEntity.getTokenObject());
            }
        }
        Device device = convertToDeviceModel(deviceIdentity);
        //get app domain name, app domain id
        IdentityEntity appDomain = identityRepository.findFirstByMainAppIdAndType(deviceIdentity.getMainAppId(), APP_DOMAIN_IDENTITY_TYPE);
        if (appDomain != null) {
            device.appDomainName = appDomain.getName();
            device.appDomainId = appDomain.getId();
        }
        // get gateway cua neu device la sub device
        if(deviceIdentity.getSubType().equals(SUB_DEVICE_TYPE)) {
            List<RelationEntity> relationEntity = relationRepository.findAllByRelationTypeAndActiveAndTargetId(PARENT, ACTIVE, deviceIdentity.getId());
            if(relationEntity != null && !relationEntity.isEmpty()) {
                IdentityEntity gateway = get(relationEntity.get(0).getOriginatorId());
                device.gatewayName = gateway.getName();
            }
        }
        event.payload = ObjectMapperUtil.toJsonString(device);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    private Event processCreateDevice(Event event) {
        Device device = ObjectMapperUtil.objectMapper(event.payload, Device.class);
        IdentityEntity deviceIdentity = convertFromDeviceModel(device);
        // get Info Main APP, Tenant Id
        IdentityEntity appDomain = identityRepository.findFirstByNameAndType(device.appDomainName, APP_DOMAIN_IDENTITY_TYPE);
        if (appDomain == null) {
            event.statusCode = ResponseStatusCode.NOT_FOUND.intValue();
            event.errorText = "Application domain not found";
            return event;
        }
        deviceIdentity.setMainAppId(appDomain.getMainAppId());
        deviceIdentity.setTenantId(appDomain.getTenantId());
        deviceIdentity.setActive(REGISTER);
        deviceIdentity.setDomain(appDomain);
        // Convert identity -> Device
        deviceIdentity = create(deviceIdentity);
        if (deviceIdentity.getSubType().equals(SUB_DEVICE_TYPE)) {
            IdentityEntity finalDeviceIdentity = deviceIdentity;
            identityRepository.findById(device.parentId).ifPresent(origin -> {
                relationService.createRelationByIdentity(origin, finalDeviceIdentity, PARENT);
            });
        }
        event.payload = ObjectMapperUtil.toJsonString(convertToDeviceModel(deviceIdentity));
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    private IdentityEntity convertFromDeviceModel(Device device) {
        IdentityEntity entity = new IdentityEntity();
        entity.setUserId(device.userId);
        entity.setTenantId(device.tenantId);
        entity.setName(device.deviceName);
        entity.setId(device.id);
        entity.setCategory(device.category);
        entity.setSubType(device.deviceType);
        entity.setType(DEVICE_IDENTITY_TYPE);
        entity.setAccessToken(device.accessToken);
        entity.setRefreshToken(device.refreshToken);
        entity.setDescription(device.description);
        return entity;
    }

    private String generateDeviceId() {
        return "S" + UUID.randomUUID();
    }

    // TODO:
    private Device convertToDeviceModel(IdentityEntity entity) {
        //find token
//        TokenEntity accessToken = tokenRepository.findFirstByHolderIsAndActiveIs(entity.getId(), ACTIVE);
//        TokenEntity refreshToken = tokenRepository.findFirstByHolderIsAndActiveIs(entity.getId(), IN_ACTIVE);
//        entity.setRefreshToken(accessToken.getTokenObject());
        Device device = new Device();
        device.active = entity.getActive();
        device.id = entity.getId();
        device.deviceName = entity.getName();
        device.deviceType = entity.getSubType();
        device.category = entity.getCategory();
        device.accessToken = entity.getAccessToken();
        device.refreshToken = entity.getRefreshToken();
        device.tenantId = entity.getTenantId();
        device.userId = entity.getUserId();
        device.description = entity.getDescription();
        device.created = entity.getCreated();
        device.updated = entity.getUpdated();
        device.createdBy = entity.getUpdatedBy();
        device.updatedBy = entity.getUpdatedBy();
        device.mainAppId = entity.getMainAppId();
        return device;
    }

    private Event processApp(Event event) {
        switch (event.method) {
            case Constants.Method.CREATE:
            case CREATE_APP:
                return processCreateApp(event);
            case DELETE_APP:
                return processDelete(event);
            case DELETE_RELATION_APP:
                return processDeleteRelationApp(event);
            case GET_APP_DETAILS:
                return processGetAppDetail(event);
            case GET_APP_LIST:
                return processGetAppList(event);
            case GET_LIST_RELATION_APP:
                return processGetListRelationApp(event);
            case ADD_ENTITY_TO_APP:
                return processAddEntityToApp(event);
            case Constants.Method.SEARCH:
                return processSearchApplication(event);
            case GET_DEVICE_GROUP_BY_APPDOMAIN:
                return processGetDeviceGroupByMainApp(event);
            case CREATE_APP_EX:
                return processCreateAppEx(event);
            case ADD_ENTITY_TO_APP_EX:
                return processAddEntityToAppEx(event);
            case DELETE_ENTITY_TO_APP_EX:
                return processDeleteEntityEx(event);
            case GET_APP_LIST_EX:
                return processGetAppListEx(event);
            default:
                return process(event);
        }
    }

    private Event processCreateApp(Event event) {
        Application application = ObjectMapperUtil.objectMapper(event.payload, Application.class);
        IdentityEntity appIdentity = convertFromAppModelToIdentity(application);
        // get info mainApp,tenantId
        IdentityEntity appDomain = identityRepository.findFirstByNameAndType(application.getNameAppDomain(), APP_DOMAIN_IDENTITY_TYPE);
        if (appDomain != null) {
            appIdentity.setTenantId(appDomain.getTenantId());
            appIdentity.setMainAppId(appDomain.getMainAppId());
            appIdentity.setDomain(appDomain);
            event.payload = ObjectMapperUtil.toJsonString(convertIdentityToAplication(create(appIdentity), appDomain));
            event.statusCode = ResponseStatusCode.OK.intValue();
        } else {
            throw new PlatformException("Application domain is not existed", ResponseStatusCode.NOT_FOUND);
        }
        return event;
    }

    private Event processCreateAppEx(Event event) {
        List<Application> applications = ObjectMapperUtil.listMapper(event.payload, Application.class);
        IdentityEntity domain = new IdentityEntity();
        domain.setId(applications.get(0).getAppDomainId());
        domain.setName(applications.get(0).getNameAppDomain());
        domain.setMainAppId(applications.get(0).getMainAppId());
        domain.setTenantId(applications.get(0).getTenantId());
        domain.setType(APP_DOMAIN_IDENTITY_TYPE);
        domain.setUserId(applications.get(0).getUserId());
        for (Application application : applications){
            IdentityEntity appIdentity = convertFromAppModelToIdentity(application);
            appIdentity.setDomain(domain);
            IdentityEntity entity = create(appIdentity);
            BeanUtils.copyProperties(entity, application);
            application.setAppType(entity.getSubType());
        }
        event.payload = ObjectMapperUtil.toJsonString(applications);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    private Event processGetAppDetail(Event event) {
        StringIdInfo idInfo = ObjectMapperUtil.objectMapper(event.payload, StringIdInfo.class);
        IdentityEntity identity = get(idInfo.getId());
        if (identity == null) {
            event.payload = null;
            event.statusCode = ResponseStatusCode.NOT_FOUND.intValue();
            event.errorText = vn.vnpt.oneiot.core.constants.Constants.ResultMessage.APPLICATION_NOT_FOUND;
        } else {
            IdentityEntity domain = identityRepository.findFirstByMainAppIdAndType(identity.getMainAppId(), APP_DOMAIN_IDENTITY_TYPE);
            List<TokenEntity> tokens = tokenRepository.findAllByHolderIs(identity.getId());
            TokenEntity accessToken = null, refreshToken = null;
            if (tokens == null || tokens.size() != 2) {
                throw new PlatformException("Error when create token for application id " + identity.getId(), ResponseStatusCode.INTERNAL_SERVER_ERROR);
            }
            for (TokenEntity e : tokens) {
                if (e.getActive().equals(ACTIVE)) accessToken = e;
                if (e.getActive().equals(IN_ACTIVE)) refreshToken = e;
            }
            if (accessToken == null || refreshToken == null) {
                throw new PlatformException("Error when create token for application id " + identity.getId(), ResponseStatusCode.INTERNAL_SERVER_ERROR);
            }
            identity.setAccessToken(accessToken.getTokenObject());
            identity.setRefreshToken(refreshToken.getTokenObject());
            identity.setExpiry(accessToken.getNotAfter());
            event.payload = ObjectMapperUtil.toJsonString(convertIdentityToAplication(identity, domain));
            event.statusCode = ResponseStatusCode.OK.intValue();
        }
        return event;
    }

    private Event processGetAppList(Event event) {
        SearchApp searchApp = ObjectMapperUtil.objectMapper(event.payload, SearchApp.class);
        List<IdentityEntity> identities = identityRepository.findAllByTenantIdAndType(searchApp.getTenantId(), APP_IDENTITY_TYPE);
        int size = 0;
        if (identities == null || (size = identities.size()) == 0) {
            ApplicationRespone applicationRespone = new ApplicationRespone();
            applicationRespone.setAppList(null);
            applicationRespone.setTotal(size);
            event.payload = ObjectMapperUtil.toJsonString(applicationRespone);
            event.statusCode = ResponseStatusCode.OK.intValue();
            return event;
        }
        HashMap<String, IdentityEntity> mainAppIdToAppDomain = new HashMap<>();
        /** Get ds appDomain thuoc ve tenant**/
        List<IdentityEntity> allAppDomainOfTenant = identityRepository.findAllByTenantIdAndType(identities.get(0).getTenantId(), APP_DOMAIN_IDENTITY_TYPE);
        if (!allAppDomainOfTenant.isEmpty()) {
            for (IdentityEntity domain: allAppDomainOfTenant) mainAppIdToAppDomain.put(domain.getMainAppId(), domain);
        }

        List<Application> applications = new ArrayList<>();
        for (IdentityEntity entity : identities) {
            applications.add(convertIdentityToAplication(entity, mainAppIdToAppDomain.get(entity.getMainAppId())));
        }
        ApplicationRespone applicationRespone = new ApplicationRespone();
        applicationRespone.setAppList(applications);
        applicationRespone.setTotal(size);
        event.payload = ObjectMapperUtil.toJsonString(applicationRespone);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }
    private Event processGetListRelationApp(Event event){
        SearchRelationApp searchRelationApp = new Gson().fromJson(event.payload, SearchRelationApp.class);
        String appId = searchRelationApp.getAppId();
        IdentityEntity identity = get(appId);
        if (identity != null && identity.getType() == APP_IDENTITY_TYPE && identity.getSubType() == MAIN_APP_TYPE){
            // mainApp
            searchRelationApp.setMainAppId(identity.getId());
            return processGetListRelationForMainApp(event,searchRelationApp);
        } else{
            // userApp
            return processGetListDeviceOrGroup(event,searchRelationApp);
        }
    }

    private Event processGetDeviceGroupByMainApp(Event event){
        SearchApp searchApp = ObjectMapperUtil.objectMapper(event.payload,SearchApp.class);
        String appId = searchApp.getAppId();
        IdentityEntity application = get(appId);
        List<RelationEntity> relation = relationRepository.getEntityRelationByOriginatorIdAndTargetTypeAndActive(appId, Integer.parseInt(searchApp.getAppType()), ACTIVE);
        List<String> ids = new ArrayList<>();
        for (RelationEntity r : relation){
            ids.add(r.getTargetId());
        }
        String query ="mainAppId=="+application.getMainAppId()+";type=="+searchApp.getAppType();
        if (ids.size()>0){
            String idDevice = ids.toString().replaceAll("\\[", "").replace("]", "");
            query=query+";id=out=("+idDevice+")";
        }
        Pageable pageable = PageRequest.of(0,1000,vn.vnpt.oneiot.base.utils.StringUtils.toSort("name:ASC"));
        List<IdentityEntity> entities =search(query,pageable).getContent();
//        List<IdentityEntity> entities = identityRepository.findByMainAppIdAndTypeAndIdNotInOrderByNameAsc(application.getMainAppId(), Integer.valueOf(searchApp.getAppType()), ids);
        GetListRelationApp respones = new GetListRelationApp();
        List<AppRelationRespone> entity = entities.stream().map(en -> {
            AppRelationRespone relationRespone = new AppRelationRespone();
                relationRespone.setId(en.getId());
                relationRespone.setOrigitorName(en.getName());
            return relationRespone;
        }).collect(Collectors.toList());
        respones.setAppList(entity);
        respones.setTotal(entity.size());
        respones.setErrorCode(ResponseStatusCode.OK.intValue());
        event.payload=ObjectMapperUtil.toJsonString(respones);
        event.statusCode= ResponseStatusCode.OK.intValue();
        return event;
    }


    private Event processAddEntityToApp(Event event){
        AddEntity entity =ObjectMapperUtil.objectMapper(event.payload,AddEntity.class);
        List<IdentityEntity> entities = identityRepository.findAllByIdIn(entity.getIdsEntity());
        IdentityEntity app = get(entity.getAppId());
        List<RelationEntity> relationEntities = new ArrayList<>();
        for (IdentityEntity identity : entities) {
            relationEntities.add(relationService.createRelationByIdentityNotSave(app, identity, OWNER));
        }
        relationRepository.saveAll(relationEntities);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    private Event processAddEntityToAppEx(Event event) {
        RelationResponse relationResponse = new RelationResponse();
        AddEntity entity = ObjectMapperUtil.objectMapper(event.payload, AddEntity.class);
        IdentityEntity app = get(entity.getAppId());
        if (Objects.isNull(app)) {
            throw new ResourceNotFoundException(entity.getAppId() + " not found");
        }
        // Loai bo id lap
        entity.setIdsEntity(new ArrayList<>(new HashSet<>(entity.getIdsEntity())));
        List<RelationEntity> relationEntities = relationRepository.findAllByOriginatorIdAndTargetIdInAndRelationType(app.getId(), entity.getIdsEntity(), OWNER);
        List<String> relationEntitiesExist = relationEntities.stream()
                .map(RelationEntity::getTargetId)
                .collect(Collectors.toList());

        if (Objects.nonNull(relationEntitiesExist) && !relationEntitiesExist.isEmpty()) {
            throw new ConflictException("Resource " + relationEntitiesExist + " exist.");
        }
        List<IdentityEntity> entities = identityRepository.findAllByIdIn(entity.getIdsEntity());
        if (entities == null || entities.size() < entity.getIdsEntity().size()) {
            List<String> notFound = new ArrayList<>(entity.getIdsEntity());
            if (entities != null) {
                List<String> exists = new ArrayList<>();
                entities.forEach(e -> {
                    exists.add(e.getId());
                });
                notFound.removeAll(exists);
            }
            throw new ResourceNotFoundException(notFound + " are not found");
        }
        entities.forEach( e -> {relationEntities.add(relationService.createRelationByIdentityNotSave(app, e, OWNER));});
        relationRepository.saveAll(relationEntities);
        relationResponse.setRelationList(entity.getIdsEntity());
        event.payload = ObjectMapperUtil.toJsonString(relationResponse);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    private Event processDeleteEntityEx(Event event) {
        AddEntity entity = ObjectMapperUtil.objectMapper(event.payload, AddEntity.class);
        IdentityEntity app = get(entity.getAppId());
        if (Objects.isNull(app)) {
            throw new ResourceNotFoundException("Resource " + entity.getAppId() + " not found.");
        }
        if (app.getType() == APP_DOMAIN_IDENTITY_TYPE) {
            throw new BadRequestException("Resource " + app.getId() + " is required User App");
        }
        List<RelationEntity> relationEntities = relationRepository.findAllByOriginatorIdAndTargetIdInAndRelationType(app.getId(), entity.getIdsEntity(), OWNER);
        List<String> targetIds = relationEntities.stream()
                .map(RelationEntity::getTargetId)
                .collect(Collectors.toList());
        List<String> nonExistingStrings = getNonExistingStrings(entity.getIdsEntity(), targetIds);
        if (Objects.nonNull(nonExistingStrings) && !nonExistingStrings.isEmpty()) {
            throw new ResourceNotFoundException("Resource " + nonExistingStrings + " not found.");
        }
        relationService.deleteAllByList(relationEntities);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    private Event processDeleteRelationApp(Event event){
        String id =event.payload;
        RelationEntity entity = relationService.get(id);
        if(Objects.nonNull(entity)){
            relationRepository.delete(entity);
        }
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    private Event processGetListRelationForMainApp(Event event, SearchRelationApp searchRelationApp) {
        SearchInfo searchInfo = ObjectMapperUtil.objectMapper(searchRelationApp.getSearchInfo(), SearchInfo.class);
        String query = searchInfo.getQuery().replace("targetName", "name");
        query = query + (Strings.isNullOrEmpty(query) ? "" : ";" ) + "active=in=(" + ACTIVE+ "," +REGISTER+ ");" + "mainAppId==" + searchRelationApp.getMainAppId() + ";type==" + searchRelationApp.getType() + (searchRelationApp.getType()== APP_IDENTITY_TYPE ? ";subType==" + USER_APP_TYPE : "");
        Pageable pageable;
        if (searchInfo.getOrders() == null || "".equals(searchInfo.getOrders())) {
            pageable = PageRequest.of(searchInfo.getPageNumber(), searchInfo.getPageSize());
        } else {
            String order =searchInfo.getOrders().replace("targetName", "name");
            pageable = PageRequest.of
                    (searchInfo.getPageNumber(), searchInfo.getPageSize(), vn.vnpt.oneiot.base.utils.StringUtils.toSort(order));
        }
        Page<IdentityEntity> page = search(query, pageable);
        List<AppRelationRespone> appRelationRespones = new ArrayList<>();
        for (IdentityEntity entity : page.getContent()) {
            appRelationRespones.add(convertFromIdentityToAppRelation(entity, searchRelationApp.getAppId()));
        }
        GetListRelationApp getListRelationApp = new GetListRelationApp();
        getListRelationApp.setTotal(page.getTotalElements());
        getListRelationApp.setAppList(appRelationRespones);

        event.payload = ObjectMapperUtil.toJsonString(getListRelationApp);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    private Event processGetListDeviceOrGroup(Event event,SearchRelationApp searchRelationApp){
        // mainApp
        SearchInfo searchInfo = ObjectMapperUtil.objectMapper(searchRelationApp.getSearchInfo(), SearchInfo.class);
        String query = searchInfo.getQuery();
        String queryRelation = "targetType=="+searchRelationApp.getType() + ";originatorId =="+ searchRelationApp.getAppId();
        List<RelationEntity> relationEntities = relationService.searchAllByQuery(queryRelation);
        long total = 0;
        List<AppRelationRespone> result = new ArrayList<>();
        if(relationEntities != null && relationEntities.size() > 0){
            Map<String, RelationEntity> mapRelation = new HashMap<>();
            query = (Strings.isNullOrEmpty(query) ? "" : (query.replace("targetName", "name")+";"))  + String.format("id=in=(%s)", relationEntities.stream().map(n -> {
                mapRelation.put(n.getTargetId(), n);
                return n.getTargetId();
            }).collect(Collectors.joining(",")));
            searchInfo.setQuery(query);
            String orders = searchInfo.getOrders().replace("targetName", "name");
            Pageable pageable;
            if(orders == null || "".equals(orders)){
                pageable = PageRequest.of(searchInfo.getPageNumber(), searchInfo.getPageSize());
            }else {
                pageable = PageRequest.of
                        (searchInfo.getPageNumber(), searchInfo.getPageSize(), vn.vnpt.oneiot.base.utils.StringUtils.toSort(orders));
            }
            Page<IdentityEntity> page = search(searchInfo.getQuery(), pageable);
            if(page != null){
                total = page.getTotalElements();
                if(page.getContent() != null){
                    for(IdentityEntity identityEntity : page.getContent()){
                        result.add(convertFromRelationEntityToAppRelation(mapRelation.get(identityEntity.getId()),searchRelationApp,identityEntity));
                    }
                }
            }
        }

        GetListRelationApp getListRelationApp = new GetListRelationApp();
        getListRelationApp.setTotal(total);
        getListRelationApp.setAppList(result);

        event.payload = ObjectMapperUtil.toJsonString(getListRelationApp);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
//        return ResponeEventDeviceOrGroup(event,searchInfo, searchRelationApp);
    }

    private Event ResponeEventDeviceOrGroup(Event event ,SearchInfo searchInfo, SearchRelationApp searchRelationApp){
        String orders = searchInfo.getOrders();
        Pageable pageable;
        if(orders == null || "".equals(orders)){
            pageable = PageRequest.of(searchInfo.getPageNumber(), searchInfo.getPageSize());
        }else {
            pageable = PageRequest.of
                    (searchInfo.getPageNumber(), searchInfo.getPageSize(), vn.vnpt.oneiot.base.utils.StringUtils.toSort(orders));
        }
        Page<RelationEntity> page = relationService.search(searchInfo.getQuery(),pageable);
        List<AppRelationRespone> appRelationRespones = new ArrayList<>();
        List<String> tagetIds = page.getContent().stream()
                .map(RelationEntity::getTargetId)
                .collect(Collectors.toList());
        List<IdentityEntity> identityEntities = identityRepository.findAllByIdIn(tagetIds);
        Map<String, IdentityEntity> mapSubType = identityEntities.stream()
                .collect(Collectors.toMap(
                        IdentityEntity::getId,
                        object -> object
                ));
        for (RelationEntity entity : page.getContent()){
            appRelationRespones.add(convertFromRelationEntityToAppRelation(entity,searchRelationApp, mapSubType.get(entity.getTargetId())));
        }
        GetListRelationApp getListRelationApp = new GetListRelationApp();
        getListRelationApp.setTotal(page.getTotalElements());
        getListRelationApp.setAppList(appRelationRespones);

        event.payload = ObjectMapperUtil.toJsonString(getListRelationApp);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    private AppRelationRespone convertFromIdentityToAppRelation(IdentityEntity identity,String appId){
        IdentityEntity maiApp =identityRepository.findFirstByIdAndType(identity.getMainAppId(),APP_IDENTITY_TYPE);
        AppRelationRespone appRelationRespone = new AppRelationRespone();
        appRelationRespone.setId(identity.getId());
        appRelationRespone.setAppId(appId);
        appRelationRespone.setOrigitorName(maiApp.getName());
        appRelationRespone.setTargetName(identity.getName());
        appRelationRespone.setTargetId(identity.getId());
        appRelationRespone.setAppType(USER_APP_TYPE);
        appRelationRespone.setSubType(identity.getSubType());
        return appRelationRespone;
    }
    private AppRelationRespone convertFromRelationEntityToAppRelation(RelationEntity entity,SearchRelationApp searchRelationApp, IdentityEntity identityEntity){
        AppRelationRespone appRelationRespone = new AppRelationRespone();
        appRelationRespone.setTargetName(entity.getTargetName());
        appRelationRespone.setTargetId(entity.getTargetId());
        appRelationRespone.setId(entity.getId());
        appRelationRespone.setOrigitorName(entity.getOrigitorName());
        appRelationRespone.setAppId(searchRelationApp.getAppId());
        appRelationRespone.setAppType(searchRelationApp.getType());
        appRelationRespone.setSubType(identityEntity.getSubType());
        return appRelationRespone;
    }

    private IdentityEntity convertFromAppModelToIdentity(Application appRequest) {
        IdentityEntity identityEntity = new IdentityEntity();
        BeanUtils.copyProperties(appRequest, identityEntity);
        identityEntity.setType(APP_IDENTITY_TYPE);
        identityEntity.setSubType(USER_APP_TYPE);
        return identityEntity;
    }
    private Application convertIdentityToAplication(IdentityEntity identity, IdentityEntity appDomain){
        Application application = new Application();
        BeanUtils.copyProperties(identity, application);
        if (appDomain != null) {
            application.setAppDomainId(appDomain.getId());
            application.setNameAppDomain(appDomain.getName());
        }
        application.setAppType(identity.getSubType());
        return application;
    }

    private IdentityEntity createMainAppForApplicationDomain(IdentityEntity appDomainIdentity) {
        IdentityEntity mainApp = new IdentityEntity();
        mainApp.type(APP_IDENTITY_TYPE)
                .name("main-" + appDomainIdentity.getName())
                .subType(MAIN_APP_TYPE)
                .userId(appDomainIdentity.getUserId())
                .tenantId(appDomainIdentity.getTenantId());
        mainApp.setDomain(appDomainIdentity);
        beforeCreate(mainApp);
        appDomainIdentity.setMainAppId(mainApp.getId());
        identityRepository.saveAll(Arrays.asList(new IdentityEntity[] {appDomainIdentity, mainApp}));
        afterCreateApplication(mainApp);
        return mainApp;
    }

    @Override
    public void beforeSoftDelete(IdentityEntity entity) {
        switch (entity.getActive()) {
            case REGISTER:
                entity.setActive(DELETED);
                break;
            case ACTIVE:
                entity.setActive(IN_ACTIVE);
                break;
            default:
                super.beforeSoftDelete(entity);

        }
        entity.setUpdated(System.currentTimeMillis());
        entity.setUpdatedBy(SecurityUtils.getCurrentUserLogin());
    }

    protected void afterSoftDelete(IdentityEntity entity) {
        if (entity.getType().equals(APP_DOMAIN_IDENTITY_TYPE)) {
            softDelete(entity.getMainAppId());
        } else if (entity.getType().equals(DEVICE_IDENTITY_TYPE)){
            closeConnectionMqtt(entity);
        }
    }

    private void closeConnectionMqtt(IdentityEntity entity) {
        Event event = new Event();
        event.payload = entity.getId();
        event.method = "UNREGISTER_DEVICE";
        event.type = EVENTTYPE_REQUEST;
        eventBus.publish(AMQPConstant.getExchangeFromRoutingKey(ROUTING_KEY_INTERNAL_MQTT_ADAPTER), ROUTING_KEY_INTERNAL_MQTT_ADAPTER, event);
    }

    @Override
    protected void beforeCreate(IdentityEntity entity) {
        super.beforeCreate(entity);
        Integer entityType = entity.getType();
        switch (entityType) {
            case APP_IDENTITY_TYPE:
                beforeCreateApplication(entity);
                break;
            case DEVICE_IDENTITY_TYPE:
                beforeCreateDevice(entity);
                break;
            case APP_DOMAIN_IDENTITY_TYPE:
                beforeCreateApplicationDomain(entity);
                break;
            default:
                break;
        }
    }

    private void beforeCreateApplicationDomain(IdentityEntity entity) {
        // Check khong có device va app nao trung ten moi cho tao
        if (identityRepository.findFirstByNameAndType(entity.getName(), APP_DOMAIN_IDENTITY_TYPE) != null) {
            throw new ConflictException("Name is already used");
        }
        entity.setId(generateApplicationDomainId(entity));
        entity.setActive(REGISTER);
    }

    private void beforeCreateApplication(IdentityEntity entity) {
        if (entity.getName() == null || entity.getName().equals("")){
            throw new ConflictException("Name is Mandatory");
        }
        // Check khong có device va app nao trung ten moi cho tao
        if (identityRepository.findFirstByNameAndTypeIn(entity.getName(), Arrays.asList(new Integer[] {APP_IDENTITY_TYPE, DEVICE_IDENTITY_TYPE})) != null) {
            throw new ConflictException("Name is already used");
        }
        entity.setId(generateApplicationId());
        if (entity.getSubType().intValue() == MAIN_APP_TYPE) {
            entity.setMainAppId(entity.getId());
        }
        String domainId = null, domainName = null;
        if (entity.getDomain() != null) {
            domainId = entity.getDomain().getId();
            domainName = entity.getDomain().getName();
        }
        CreateTokenDTO createTokenDTO = tokenService.createToken(entity.getMainAppId(), domainId, domainName, entity.getUserId(), entity.getId(), entity.getName(), entity.getTenantId(), TokenService.TOKEN_TYPE_APP, entity.getSubType());
        entity.setAccessToken(createTokenDTO.getAccessToken());
        entity.setRefreshToken(createTokenDTO.getRefreshToken());
        entity.setExpiry(createTokenDTO.getExpiry());
        entity.setActive(REGISTER);
    }

    private void beforeCreateDevice(IdentityEntity entity) {
        // Check khong có device va app nao trung ten moi cho tao
        if (identityRepository.findFirstByNameAndTypeIn(entity.getName(), Arrays.asList(new Integer[] {APP_IDENTITY_TYPE, DEVICE_IDENTITY_TYPE})) != null) {
            throw new ConflictException("Name is already used");
        }
        entity.setId(generateDeviceId());
        // TODO: Generate token va gan identity
        String domainId = null, domainName = null;
        if (entity.getDomain() != null) {
            domainId = entity.getDomain().getId();
            domainName = entity.getDomain().getName();
        }
        CreateTokenDTO createTokenDTO = tokenService.createToken(entity.getMainAppId(), domainId, domainName, entity.getUserId(), entity.getId(), entity.getName(), entity.getTenantId(), TokenService.TOKEN_TYPE_DEVICE, entity.getSubType());
        entity.setAccessToken(createTokenDTO.getAccessToken());
        entity.setRefreshToken(createTokenDTO.getRefreshToken());
        entity.setExpiry(createTokenDTO.getExpiry());
        entity.setActive(REGISTER);
    }

    @Override
    protected void afterCreate(IdentityEntity entity) {
        Integer entityType = entity.getType();
        switch (entityType) {
            case APP_IDENTITY_TYPE:
                afterCreateApplication(entity);
                break;
            case DEVICE_IDENTITY_TYPE:
                afterCreateDevice(entity);
                break;
            case APP_DOMAIN_IDENTITY_TYPE:
                afterCreateApplicationDomain(entity);
                break;
        }
    }

    public void afterCreateApplication() {
        // Tao token cho app
    }

    private String generateApplicationId() {
        return "S" + UUID.randomUUID();
    }
    private String generateGroup(){
        return "grp-"+ UUID.randomUUID();
    }
    private String generateApplicationDomainId(IdentityEntity entity) {
        if (entity.getAuthorityType().equals(NON_REGISTRERED)) {
            return "N" + UUID.randomUUID();
        }
        return entity.getId();
    }

    private Event processAppDomain(Event event) {
        switch (event.method) {
            case CREATE_APP_DOMAIN:
            case Constants.Method.CREATE:
                return processCreateAppDomain(event);
            case GET_APP_DOMAIN_DETAILS:
                return processGetAppDomainDetail(event);
            case DELETE_APP_DOMAIN:
                //TODO delete mainapp
                return processDelete(event);
            case GET_APP_DOMAIN_LIST:
            case Constants.Method.SEARCH:
                return processSearchApplicationDomain(event);
            default:
                return process(event);
        }
    }

    private Event processGetAppDomainDetail(Event event) {
        StringIdInfo idInfo = ObjectMapperUtil.objectMapper(event.payload,StringIdInfo.class);
        IdentityEntity identity = get(idInfo.getId());
        if (identity == null) {
            event.payload = null;
            event.statusCode = ResponseStatusCode.NOT_FOUND.intValue();
            event.errorText = vn.vnpt.oneiot.core.constants.Constants.ResultMessage.APPLICATION_NOT_FOUND;
        } else {
            event.payload = ObjectMapperUtil.toJsonString(convertIdentityToAplicationDomain(identity));
            event.statusCode = ResponseStatusCode.OK.intValue();
        }
        return event;
    }

    private ApplicationDomain convertIdentityToAplicationDomain(IdentityEntity identity) {
        ApplicationDomain applicationDomain = new ApplicationDomain();
        IdentityEntity mainApp = get(identity.getMainAppId());
        applicationDomain.setMainAppName(mainApp.getName());
        applicationDomain.setId(identity.getId());
        applicationDomain.setName(identity.getName());
        applicationDomain.setTenantId(identity.getTenantId());
        applicationDomain.setDescription(identity.getDescription());
        applicationDomain.setUserId(identity.getUserId());
        applicationDomain.setAuthorityType(identity.getAuthorityType());
        applicationDomain.setMainAppId(identity.getMainAppId());
        applicationDomain.setCreated(identity.getCreated());
        applicationDomain.setCreatedBy(identity.getCreatedBy());
        applicationDomain.setUpdated(identity.getUpdated());
        applicationDomain.setUpdatedBy(identity.getUpdatedBy());
        applicationDomain.setActive(identity.getActive());
        return applicationDomain;
    }


    private Event processGroup(Event event) {
        switch (event.method) {
            case GET_LIST_GROUP_SIMPLE:
                return processGetListGroupSimple(event);
            case GET_LIST_GROUP:
                return processGetListGroup(event);
            case GET_GROUP_DETAIL:
                return processgetGroupDetail(event);
            case Constants.Method.DELETE:
                return processDelete(event);
            case GET_LIST_DEVICE_TO_ADD:
                return processgetListDeviceToAdd(event);
            case Constants.Method.SEARCH:
                return processSearchGroup(event);
            case GET_LIST_DEVICE_IDS_BY_GROUP_IDS:
                return getDeviceIdsFromGroupIds(event);
            default:
                return process(event);
        }
    }

    private Event processCreateAppDomain(Event event) {
        IdentityEntity appDomainIdentity = convertFromAppDomainModel(event.payload);
        return setResultSuccess(event, ObjectMapperUtil.toJsonString(convertIdentityToAplicationDomain(create(appDomainIdentity))));
    }

    private IdentityEntity convertFromAppDomainModel(String payload) {
        IdentityEntity identityEntity = ObjectMapperUtil.objectMapper(payload, IdentityEntity.class);
        identityEntity.setType(APP_DOMAIN_IDENTITY_TYPE);
        return identityEntity;
    }

    private void afterCreateApplicationDomain(IdentityEntity entity) {
        createMainAppForApplicationDomain(entity);
    }

    public void afterCreateDevice(IdentityEntity entity) {
    }

    public void afterCreateApplication(IdentityEntity entity) {

    }

    private Event processGetListGroupSimple(Event event) {
        GroupRequest request = ObjectMapperUtil.objectMapper(event.payload, GroupRequest.class);
        String query = "mainAppId==" + request.getMainAppId();
        if (!Strings.isNullOrEmpty(request.getGroupName())) {
            query = query + ";name==\"*" + request.getGroupName() + "*\"";
        }
        Pageable pageable = PageRequest.of(request.getOffset() / request.getLimit(), request.getLimit(), new Sort("created"));
        SearchInfo searchInfo = new SearchInfo(query, pageable);
        Page<IdentityEntity> identityEntities = processSearchInfo(ObjectMapperUtil.toJsonString(searchInfo), GROUP_IDENTITY_TYPE);
        List<GetListGroupResponse> list = identityEntities.stream().map(this::convertToGroupList).collect(Collectors.toList());
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(identityEntities.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(list));
        event.payload = ObjectMapperUtil.toJsonString(pageInfo);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    private Event processSearchGroup(Event event) {
        Page<IdentityEntity> identityEntities = processSearchInfo(event.payload, GROUP_IDENTITY_TYPE);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(identityEntities.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(identityEntities.getContent()));
        event.payload = ObjectMapperUtil.toJsonString(pageInfo);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    private GetListGroupResponse convertToGroupList(IdentityEntity entity) {
        GetListGroupResponse group = new GetListGroupResponse();
        group.setGroupId(entity.getId());
        group.setGroupName(entity.getName());
        group.setCreated(entity.getCreated());
        group.setUpdated(entity.getUpdated());
        group.setMemberIds(entity.getMemberIds());
        return group;
    }
    private Group convertIdentityToGroup(IdentityEntity entity, IdentityEntity appDomain){
        Group group = new Group();
        group.setId(entity.getId());
        group.setName(entity.getName());
        group.setNameAppDomain(appDomain == null ? null : appDomain.getName());
        group.setUserId(entity.getUserId());
        group.setMemberIds(entity.getMemberIds());
        group.setDescription(entity.getDescription());
        group.setCreated(entity.getCreated());
        group.setCreatedBy(entity.getCreatedBy());
        group.setUpdated(entity.getUpdated());
        group.setUpdatedBy(entity.getUpdatedBy());
        group.setActive(entity.getActive());
        group.setMainAppId(entity.getMainAppId());
        group.setTenantId(entity.getTenantId());
        return group;
    }

    private Event processGetListGroup(Event event){
        SearchInfo searchInfo = ObjectMapperUtil.objectMapper(event.payload,SearchInfo.class);
        String orders = searchInfo.getOrders();
        boolean isPlatformUser = false;
        Pageable pageable;
        if(orders == null || "".equals(orders)){
            pageable = PageRequest.of(searchInfo.getPageNumber(), searchInfo.getPageSize());
        }else {
            pageable = PageRequest.of
                    (searchInfo.getPageNumber(), searchInfo.getPageSize(), vn.vnpt.oneiot.base.utils.StringUtils.toSort(orders));
        }
        if (searchInfo.getQuery() == null) searchInfo.setQuery("");
        String query = "type==" + GROUP_IDENTITY_TYPE + ";" + searchInfo.getQuery();

        Page<IdentityEntity> pageInfo = search(query, pageable);
        List<IdentityEntity> identities = pageInfo.getContent();
        if (identities == null || identities.isEmpty()) {
            ApplicationRespone applicationRespone = new ApplicationRespone();
            applicationRespone.setAppList(null);
            applicationRespone.setTotal(0);
            event.payload = ObjectMapperUtil.toJsonString(applicationRespone);
            event.statusCode = ResponseStatusCode.OK.intValue();
            return event;
        }
        HashMap<String, IdentityEntity> mainAppIdToAppDomain = new HashMap<>();
        /** Get ds appDomain thuoc ve tenant**/
        List<IdentityEntity> allAppDomainOfTenant;
        if (!query.contains("tenantId=="))
            allAppDomainOfTenant = identityRepository.findByType(APP_DOMAIN_IDENTITY_TYPE);
        else
            allAppDomainOfTenant = identityRepository.findAllByTenantIdAndType(identities.get(0).getTenantId(), APP_DOMAIN_IDENTITY_TYPE);
        if (allAppDomainOfTenant != null && !allAppDomainOfTenant.isEmpty()) {
            for (IdentityEntity domain: allAppDomainOfTenant) mainAppIdToAppDomain.put(domain.getMainAppId(), domain);
        }

        List<Group> groups = new ArrayList<>();
        for (IdentityEntity entity : identities) {
            groups.add(convertIdentityToGroup(entity, mainAppIdToAppDomain.get(entity.getMainAppId())));
        }
        GroupRespone groupRespone = new GroupRespone();
        groupRespone.setGroups(groups);
        groupRespone.setTotal(Long.valueOf(pageInfo.getTotalElements()).intValue());
        event.payload = ObjectMapperUtil.toJsonString(groupRespone);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }
    private Event processgetGroupDetail(Event event){
        SearchInfo searchInfo = ObjectMapperUtil.objectMapper(event.payload,SearchInfo.class);
        IdentityEntity group = identityRepository.findFirstByNameAndType(searchInfo.getResultField(), GROUP_IDENTITY_TYPE);
        IdentityEntity appDomain = identityRepository.findFirstByMainAppIdAndType(group.getMainAppId(),APP_DOMAIN_IDENTITY_TYPE);
        GroupDetailRespone groupDetailRespone = new GroupDetailRespone();
        groupDetailRespone.setId(group.getId());
        groupDetailRespone.setName(group.getName());
        groupDetailRespone.setNameAppDomain(appDomain.getName());
        if (group.getMemberIds().size()>0){
            String ids = group.getMemberIds().toString();
            ids = ids.replaceAll("\\[", "").replace("]", "");
            String query =searchInfo.getQuery();
            query = (searchInfo.getQuery().equals("")? "type==" + DEVICE_IDENTITY_TYPE : (query+";type==" + DEVICE_IDENTITY_TYPE)) + ";id=in=(" + ids + ")" + ";active=in=(" + ACTIVE + "," + REGISTER + ")";
            String orders = searchInfo.getOrders();
            Pageable pageable;
            if(orders == null || "".equals(orders)){
                pageable = PageRequest.of(searchInfo.getPageNumber(), searchInfo.getPageSize());
            }else {
                pageable = PageRequest.of
                        (searchInfo.getPageNumber(), searchInfo.getPageSize(), vn.vnpt.oneiot.base.utils.StringUtils.toSort(orders));
            }
            List<IdentityEntity> identityEntities = search(query,pageable).getContent();
            if (identityEntities.size()>0){
                List<Device> list = identityEntities.stream().map(this::convertToDeviceModel).collect(Collectors.toList());
                groupDetailRespone.setDevices(list);
            }
            groupDetailRespone.setTotal(group.getMemberIds().size());
        }
        event.statusCode = ResponseStatusCode.OK.intValue();
        event.payload=ObjectMapperUtil.toJsonString(groupDetailRespone);
        return event;
    }
    private Event processgetListDeviceToAdd(Event event){
        AddDevice addDevice = ObjectMapperUtil.objectMapper(event.payload,AddDevice.class);
        IdentityEntity appDomain = identityRepository.findFirstByNameAndType(addDevice.getAppDomain(),APP_DOMAIN_IDENTITY_TYPE);
        IdentityEntity group =identityRepository.findFirstByNameAndTypeAndMainAppId(addDevice.getNameGroup(),GROUP_IDENTITY_TYPE, appDomain.getMainAppId());
        String query = "tenantId=="+ addDevice.getTenantId()+";type==" + DEVICE_IDENTITY_TYPE + ";mainAppId==" + appDomain.getMainAppId();
        if (group.getMemberIds().size()>0){
            String ids = group.getMemberIds().toString();
            ids = ids.replaceAll("\\[", "").replace("]", "");
            query=query+";id=out=("+ids+")" + ";active=in=(" + ACTIVE + "," + REGISTER + ")";
        }
        Pageable pageable = PageRequest.of(0,1000,vn.vnpt.oneiot.base.utils.StringUtils.toSort("name:ASC"));
        List<IdentityEntity> devices =search(query,pageable).getContent();
//        List<IdentityEntity> devices = identityRepository.findByTenantIdAndTypeAndMainAppIdAndIdNotInOrderByNameAsc(group.getTenantId(),DEVICE_IDENTITY_TYPE,appDomain.getMainAppId(),group.getMemberIds());
        AddGroupRespone addGroupRespone = new AddGroupRespone();
        addGroupRespone.setDevices(devices.stream().map(this::convertToDeviceModel).collect(Collectors.toList()));
        event.payload=ObjectMapperUtil.toJsonString(addGroupRespone);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    public void updateActive(String id, int status) {
        IdentityEntity identityEntity = identityRepository.findById(id).get();
        Update update = new Update();
        if (status == ACTIVE) {
            update.set("active", status).set("activationTime", System.currentTimeMillis());
        } else {
            update.set("active", status);
        }
        // If is main app, active app domain too
        if (identityEntity.getType().intValue() == APP_IDENTITY_TYPE && identityEntity.getSubType().intValue() == MAIN_APP_TYPE) {
            updateByQuery(Query.query(new Criteria().orOperator(
                            Criteria.where("mainAppId").is(id).and("type").is(APP_DOMAIN_IDENTITY_TYPE),
                            Criteria.where("_id").is(id))),
                    update);
        } else {
            updateByQuery(Query.query(Criteria.where("_id").is(id)),
                    update);
        }
    }

    public List<String> getNonExistingStrings(List<String> stringList, List<String> targetList) {
        return stringList.stream()
                .filter(str -> !targetList.contains(str))
                .collect(Collectors.toList());
    }

    private Event processGetAppListEx(Event event) {
        GetAppListRequest getAppListRequest = ObjectMapperUtil.objectMapper(event.payload, GetAppListRequest.class);

        // neu holderId == mainAppId -> La main app
        if(getAppListRequest.getHolderId().equals(getAppListRequest.getMainAppId())) {
            IdentityEntity appDomain = identityRepository.findFirstByMainAppIdAndType(getAppListRequest.getMainAppId(), APP_DOMAIN_IDENTITY_TYPE);
            Query query = new Query();
            Criteria criteria = new Criteria();
            if(getAppListRequest.getFromDate() != null && getAppListRequest.getToDate() != null) {
                criteria.and("created").gte(getAppListRequest.getFromDate()).lte(getAppListRequest.getToDate());
            } else if(getAppListRequest.getToDate() != null) {
                criteria.and("created").lte(getAppListRequest.getToDate());
            }
            else if(getAppListRequest.getFromDate() != null) {
                criteria.and("created").gte(getAppListRequest.getFromDate());
            }
            if(getAppListRequest.getActive() != null) {
                criteria.and("active").is(getAppListRequest.getActive());
            }
            if(getAppListRequest.getLimit() != null) {
                query.limit(getAppListRequest.getLimit());
            }else {
                query.limit(1000);
            }
            if(getAppListRequest.getOffset() != null) {
                query.skip(getAppListRequest.getOffset());
            }else {
                query.skip(0);
            }
            criteria.andOperator(
                    Criteria.where("mainAppId").is(getAppListRequest.getMainAppId()),
                    Criteria.where("type").is(APP_IDENTITY_TYPE));
            query.addCriteria(criteria);
            List<IdentityEntity> appList = mongoTemplate.find(query, IdentityEntity.class);
            GetAppListResponseEx getAppListResponseEx = convertToGetAppListResponseEx(appList, appDomain);
            event.statusCode = ResponseStatusCode.OK.intValue();
            event.payload = ObjectMapperUtil.toJsonString(getAppListResponseEx);
        }else {
            // case la user app
            event.statusCode = ResponseStatusCode.OK.intValue();
            event.payload = ObjectMapperUtil.toJsonString(new GetAppListResponseEx());
        }
        return event;
    }

    private GetAppListResponseEx convertToGetAppListResponseEx(List<IdentityEntity> appList, IdentityEntity appDomain) {
        GetAppListResponseEx getAppListResponseEx = new GetAppListResponseEx();
        getAppListResponseEx.setErrorCode(ResponseStatusCode.OK.intValue());
        getAppListResponseEx.setErrorMsg(vn.vnpt.oneiot.core.constants.Constants.ResultMessage.SUCCESS);
        getAppListResponseEx.setTotal(appList.size());
        List<AppListResponseEx> appInfoList = new ArrayList<>();
        for (IdentityEntity app : appList) {
            AppListResponseEx appListResponseEx = new AppListResponseEx();
            appListResponseEx.setAppName(app.getName());
            appListResponseEx.setAppId(app.getId());
            appListResponseEx.setAppType(app.getSubType());
            appListResponseEx.setAppDomainName(appDomain.getName());
            appListResponseEx.setActive(app.getActive());
            appListResponseEx.setCreated(app.getCreated());
            appListResponseEx.setUpdated(app.getUpdated());
            appListResponseEx.setTenantId(app.getTenantId());
            appListResponseEx.setAppDomainId(appDomain.getId());
            appInfoList.add(appListResponseEx);
        }
        getAppListResponseEx.setAppList(appInfoList);
        return getAppListResponseEx;
    }

    private Event processCreateDeviceEx(Event event){
        CreateDeviceRequest createDeviceRequest = ObjectMapperUtil.objectMapper(event.payload, CreateDeviceRequest.class);
        CreateDeviceResponse createDeviceInfo = new CreateDeviceResponse();
        List<DeviceCreateResponse> deviceResponsesList = new ArrayList<>();
        List<String> gatewayIds = new ArrayList<>();
        List<RelationEntity> relations = new ArrayList<>();
        List<IdentityEntity> entities = new ArrayList<>();
        Map<String, AbstractMap.SimpleEntry<String, IdentityEntity>> subDeviceMap = new HashMap<>(); // <DeviceId, <GatewayId, DeviceEntity>>
        IdentityEntity domain = identityRepository.findFirstByMainAppIdAndType(createDeviceRequest.getMainAppId(),APP_DOMAIN_IDENTITY_TYPE);
        if (createDeviceRequest.getDeviceList() != null && createDeviceRequest.getDeviceList().size() > 0 && createDeviceRequest.getUserId() != null && createDeviceRequest.getTenantId() != null) {
            List<DeviceRequest> deviceRequestList = createDeviceRequest.getDeviceList();
            List<String> deviceNames = new ArrayList<>();
            for (DeviceRequest deviceRequest : deviceRequestList) {
                if (!deviceNames.contains(deviceRequest.getDeviceName())) deviceNames.add(deviceRequest.getDeviceName());
                IdentityEntity deviceIdentity = new IdentityEntity();
                deviceIdentity.setId(generateDeviceId());
                deviceIdentity.setName(deviceRequest.getDeviceName());
                deviceIdentity.setType(DEVICE_IDENTITY_TYPE);
                deviceIdentity.setSubType(deviceRequest.getDeviceType());
                deviceIdentity.setUserId(createDeviceRequest.getUserId());
                deviceIdentity.setTenantId(createDeviceRequest.getTenantId());
                deviceIdentity.setMainAppId(createDeviceRequest.getMainAppId());
                deviceIdentity.setDomain(domain);
                deviceIdentity.setCategory(deviceRequest.getCategory()!=null ? deviceRequest.getCategory() : "Other");
                deviceIdentity.setActive(REGISTER);
                super.beforeCreate(deviceIdentity);
                entities.add(deviceIdentity);
                if (deviceRequest.getDeviceType().intValue() == SUB_DEVICE_TYPE && StringUtils.isNotBlank(deviceRequest.getGatewayId())) {
                    subDeviceMap.put(deviceIdentity.getId(), new AbstractMap.SimpleEntry<>(deviceRequest.getGatewayId(), deviceIdentity));
                    gatewayIds.add(deviceRequest.getGatewayId());
                }
            }

            // Validate name is existed
            if (identityRepository.countByNameInAndTypeIn(deviceNames, Arrays.asList(DEVICE_IDENTITY_TYPE, APP_IDENTITY_TYPE)) > 0) {
                throw new ConflictException("Existed device name");
            }
            // TODO: tao token
            if(!entities.isEmpty()) {
                // Xu ly relation gateway - subdevice
                Map<String, IdentityEntity> parentIdentities = new HashMap<>();
                // Get All gateway
                if (!gatewayIds.isEmpty()) {
                    List<IdentityEntity> temp = identityRepository.findAllByIdIn(gatewayIds);
                    temp.stream().forEach(en -> parentIdentities.put(en.getId(), en));
                }
                // Remove invalid subdevice - gatewayId is wrong
                for (AbstractMap.SimpleEntry<String, IdentityEntity> subEn : subDeviceMap.values()) {
                    if (parentIdentities.get(subEn.getKey()) == null) entities.remove(subEn.getValue());
                    try {
                        RelationEntity relationEntity = relationService.createRelationByIdentityNotSave(parentIdentities.get(subEn.getKey()), subEn.getValue(), PARENT);
                        relations.add(relationEntity);
                    } catch (PlatformException e) {
                        entities.remove(subEn.getValue());
                        e.printStackTrace();
                    }
                }

                if (entities.isEmpty()) {
                    if (!subDeviceMap.isEmpty())
                        throw new BadRequestException(subDeviceMap.values().stream().findAny().get().getKey() + " is invalid gateway device");
                    else
                        throw new BadRequestException("Invalid device information");
                }
                tokenService.createTokenBulkForDevice(entities);
                identityRepository.saveAll(entities);
                if (!relations.isEmpty()) {
                    relationRepository.saveAll(relations);
                }
                for(IdentityEntity e : entities) {
                    deviceResponsesList.add(new DeviceCreateResponse(e.getName(), e.getId(),
                            e.getSubType(), e.getAccessToken(), e.getRefreshToken(), e.getExpiry(), e.getCategory(),
                            e.getSubType().intValue() == SUB_DEVICE_TYPE? subDeviceMap.get(e.getId()).getKey() : null));
                }
            }
            createDeviceInfo.setTotal(Long.valueOf(deviceResponsesList.size()));
            createDeviceInfo.setDeviceList(deviceResponsesList);
        } else {
            createDeviceInfo.setTotal(0L);
        }
        createDeviceInfo.setDeviceList(deviceResponsesList);
        event.payload = ObjectMapperUtil.toJsonString(createDeviceInfo);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    void beforeCreateRelation(RelationEntity relationEntity) {
        relationEntity.setCreated(System.currentTimeMillis());
        relationEntity.setUpdated(relationEntity.getCreated());
        if(relationEntity.getCreatedBy() == null) {
            String currentUsername = SecurityUtils.getCurrentUserLogin();
            relationEntity.setCreatedBy(currentUsername);
        }
        if (relationEntity.getUpdatedBy() == null) {
            relationEntity.setUpdatedBy(relationEntity.getCreatedBy());
        }
        if(relationEntity.getActive() == null) {
            relationEntity.setActive(Constants.EntityStatus.ACTIVE);
        }
    }

    public Event processGetDeviceList(Event event) {
        GetDeviceListRequest getDeviceListRequest = ObjectMapperUtil.objectMapper(event.payload, GetDeviceListRequest.class);
        GetListDeviceResponse getListDeviceResponse = new GetListDeviceResponse();
        IdentityEntity appDomain = identityRepository.findFirstByMainAppIdAndType(getDeviceListRequest.getMainAppId(), APP_DOMAIN_IDENTITY_TYPE);
        getListDeviceResponse.setAppDomain(appDomain.getName());
        getListDeviceResponse.setAppDomainID(appDomain.getId());
        List<DeviceListResponse> deviceListResponses = new ArrayList<>();
        Query query = new Query();
        Criteria criteria = new Criteria();
        if(getDeviceListRequest.getDeviceType() != null) {
            criteria.and("subType").is(Integer.valueOf(getDeviceListRequest.getDeviceType()));
        }
        if(getDeviceListRequest.getFromDate() != null && getDeviceListRequest.getToDate() != null) {
            criteria.and("created").gte(getDeviceListRequest.getFromDate()).lte(getDeviceListRequest.getToDate());
        } else if(getDeviceListRequest.getToDate() != null) {
            criteria.and("created").lte(getDeviceListRequest.getToDate());
        }
        else if(getDeviceListRequest.getFromDate() != null) {
            criteria.and("created").gte(getDeviceListRequest.getFromDate());
        }
        if(getDeviceListRequest.getLimit() != null) {
            query.limit(getDeviceListRequest.getLimit());
        }else {
            query.limit(1000);
        }
        if(getDeviceListRequest.getOffset() != null) {
            query.skip(getDeviceListRequest.getOffset());
        }else {
            query.skip(0);
        }
        // holderId = mainAppId -> main app
        if(getDeviceListRequest.getHolderId().equals(getDeviceListRequest.getMainAppId())) {
            criteria.andOperator(
                    Criteria.where("mainAppId").is(getDeviceListRequest.getMainAppId()),
                    Criteria.where("type").is(DEVICE_IDENTITY_TYPE));
            query.addCriteria(criteria);
            List<IdentityEntity> deviceIdentityList = mongoTemplate.find(query, IdentityEntity.class);
            // build event get device status
            List<String> ids = new ArrayList<>();
            List<String> listDeviceId = new ArrayList<>();
            deviceIdentityList.stream().forEach(e -> {
                if(e.getActive() == ACTIVE) ids.add(e.getId());
                listDeviceId.add(e.getId());
            });
            // get device status from ids
            List<RelationService.DeviceStatus> deviceStatusList = getDeviceStatus(ids);
            deviceListResponses = deviceIdentityList.stream().map(this::convertDeviceToDeviceListResponse).collect(Collectors.toList());
            // get access token from ids
            List<TokenEntity> tokenEntities = tokenRepository.findAllByHolderInAndActive(listDeviceId, ACTIVE);
            for (DeviceListResponse device : deviceListResponses) {
                // map status to device resp
                for (RelationService.DeviceStatus status : deviceStatusList) {
                    if(device.getDeviceId().equals(status.deviceId)) {
                        device.setState(status.deviceStatus);
                    }
                }
                tokenEntities.stream().forEach(e -> {
                    if(e.getHolder().equals(device.getDeviceId())) {
                        device.setAccessToken(e.getTokenObject());
                    }
                });
            }
        }else {
            // nếu là user app
            Criteria subQueryCriteria = new Criteria();
            subQueryCriteria.and("originatorId").is(getDeviceListRequest.getHolderId()).and("targetType").is(DEVICE_IDENTITY_TYPE);
            AggregationOperation subQueryMatch = Aggregation.match(subQueryCriteria);
            Aggregation subQueryAggregation = Aggregation.newAggregation(subQueryMatch);

            List<RelationEntity> subQueryResults = mongoTemplate
                    .aggregate(subQueryAggregation, "BASE_RELATION_ENTITY", RelationEntity.class)
                    .getMappedResults();

            List<String> targetIds = subQueryResults.stream().map(e -> e.getTargetId()).collect(Collectors.toList());
            criteria.and("_id").in(targetIds);
            query.addCriteria(criteria);
            List<IdentityEntity> deviceIdentityList = mongoTemplate.find(query, IdentityEntity.class);
            // build event get device status
            List<String> ids = new ArrayList<>();
            List<String> listDeviceId = new ArrayList<>();
            deviceIdentityList.stream().forEach(e -> {
                if(e.getActive() == ACTIVE) ids.add(e.getId());
                listDeviceId.add(e.getId());
            });
            // get device status from ids
            List<RelationService.DeviceStatus> deviceStatusList = getDeviceStatus(ids);
            deviceListResponses = deviceIdentityList.stream().map(this::convertDeviceToDeviceListResponse).collect(Collectors.toList());
            // get access token from ids
            List<TokenEntity> tokenEntities = tokenRepository.findAllByHolderInAndActive(listDeviceId, ACTIVE);
            for (DeviceListResponse device : deviceListResponses) {
                // map status to device resp
                for (RelationService.DeviceStatus status : deviceStatusList) {
                    if(device.getDeviceId().equals(status.deviceId)) {
                        device.setState(status.deviceStatus);
                    }
                }
                tokenEntities.stream().forEach(e -> {
                    if(e.getHolder().equals(device.getDeviceId())) {
                        device.setAccessToken(e.getTokenObject());
                    }
                });
            }
        }
        getListDeviceResponse.setTotal((long) deviceListResponses.size());
        getListDeviceResponse.setDeviceInfoList(deviceListResponses);
        setResultSuccess(event, ObjectMapperUtil.toJsonString(getListDeviceResponse));
        return event;
    }

    private DeviceListResponse convertDeviceToDeviceListResponse(IdentityEntity deviceEntity) {
        DeviceListResponse device = new DeviceListResponse();
        device.setDeviceId(deviceEntity.getId());
        device.setDeviceName(deviceEntity.getName());
        device.setDeviceType(String.valueOf(deviceEntity.getSubType()));
        device.setCategory(deviceEntity.getCategory());
        device.setCreated(deviceEntity.getCreated());
        device.setUpdated(deviceEntity.getUpdated());
        device.setTenantId(deviceEntity.getTenantId());
        if(deviceEntity.getActive() == REGISTER) {
            device.setState(REGISTER);
        }
        return device;
    }

    private List<RelationService.DeviceStatus> getDeviceStatus(List<String> ids) {
        AeStatusRequest aeStatusRequest = new AeStatusRequest();
        aeStatusRequest.ids = ids;
        aeStatusRequest.type = 0;
        aeStatusRequest.from = 1;
        MessageProperties messageProperties = new MessageProperties();
        messageProperties.setReplyTo(ROUTING_KEY_API_AUTHORIZATION);
        String exchange = getExchangeFromRoutingKey(deviceStatusRoutingKey);
        Event event1 = new Event();
        event1.id = UUID.randomUUID().toString();
        event1.payload = new Gson().toJson(aeStatusRequest);
        event1.method = vn.vnpt.oneiot.core.generic.Constants.Method.GET_STATUS_AE;
        event1.type = AMQPConstant.EVENTTYPE_REQUEST;
        event1 = eventBus.publishAndReceiveSynch(exchange,deviceStatusRoutingKey, event1, messageProperties, 30000);
        List<RelationService.DeviceStatus> deviceStatusList = new Gson().fromJson(event1.payload, new TypeToken<List<RelationService.DeviceStatus>>(){}.getType());
        return deviceStatusList;
    }

    private Event processGetDeviceInfo(Event event){
        StringIdInfo stringIdInfo = new Gson().fromJson(event.payload, StringIdInfo.class);
        IdentityEntity deviceIdentity = identityRepository.findFirstByIdAndType(stringIdInfo.getId(), DEVICE_IDENTITY_TYPE);
        if(deviceIdentity != null) {
            List<String> ids = new ArrayList<>();
            DeviceCommonResponse deviceCommonResponse = convertFromDeviceIdentityToDeviceDetailResponse(deviceIdentity);
            IdentityEntity appDomain = identityRepository.findFirstByMainAppIdAndType(deviceIdentity.getMainAppId(), APP_DOMAIN_IDENTITY_TYPE);
            deviceCommonResponse.setAppDomain(appDomain.getName());
            deviceCommonResponse.setAppDomainID(appDomain.getId());
            if(deviceIdentity.getSubType() == SUB_DEVICE_TYPE) {
                RelationEntity relationEntity = relationRepository.findFirstByTargetIdAndRelationType(deviceIdentity.getId(), PARENT);
                if(relationEntity != null)
                    deviceCommonResponse.setGatewayId(relationEntity.getOriginatorId());
            }
            if(deviceIdentity.getActive() == ACTIVE) {
                ids.add(deviceIdentity.getId());
                List<RelationService.DeviceStatus> deviceStatusList = getDeviceStatus(ids);
                deviceCommonResponse.setState(deviceStatusList.get(0).deviceStatus);
            }
            deviceCommonResponse.setErrorCode(ResponseStatusCode.OK.intValue());
            deviceCommonResponse.setErrorMsg(vn.vnpt.oneiot.core.constants.Constants.ResultMessage.SUCCESS);
            event.payload = ObjectMapperUtil.toJsonString(deviceCommonResponse);
            event.statusCode = ResponseStatusCode.OK.intValue();
        }else {
            event.statusCode = ResponseStatusCode.NOT_FOUND.intValue();
            event.errorText = "Device not found";
        }
        return event;
    }

    private DeviceCommonResponse convertFromDeviceIdentityToDeviceDetailResponse(IdentityEntity deviceIdentity) {
        List<TokenEntity> tokenEntities = tokenRepository.findAllByHolderIs(deviceIdentity.getId());
        DeviceCommonResponse deviceCommonResponse = new DeviceCommonResponse();
        deviceCommonResponse.setState(deviceIdentity.getActive());
        deviceCommonResponse.setDeviceId(deviceIdentity.getId());
        deviceCommonResponse.setDeviceName(deviceIdentity.getName());
        deviceCommonResponse.setCategory(deviceIdentity.getCategory());
        deviceCommonResponse.setDeviceType(String.valueOf(deviceIdentity.getSubType()));
        deviceCommonResponse.setAccessToken(tokenEntities!=null ? tokenEntities.get(0).getTokenObject() : null);
        deviceCommonResponse.setRefreshToken(tokenEntities!=null ? tokenEntities.get(1).getTokenObject() : null);
        deviceCommonResponse.setExpiry(deviceIdentity.getExpiry());
        deviceCommonResponse.setDescription(deviceIdentity.getDescription());
        deviceCommonResponse.setTenantId(deviceIdentity.getTenantId());
        deviceCommonResponse.setUserId(deviceIdentity.getUserId());
        deviceCommonResponse.setCreated(deviceIdentity.getCreated());
        deviceCommonResponse.setUpdated(deviceIdentity.getUpdated());
        return deviceCommonResponse;
    }

    private Event processDeleteDevice(Event event) {
        StringIdInfo idInfo = new Gson().fromJson(event.payload, StringIdInfo.class);
        IdentityEntity deviceIdentity = identityRepository.findFirstByIdAndType(idInfo.getId(), DEVICE_IDENTITY_TYPE);
        deviceIdentity.setActive(DELETED);
        identityRepository.save(deviceIdentity);
        event = setResultSuccess(event, new Gson().toJson(idInfo.getId()));
        return event;
    }

    private Event processGetDeviceDetailInfo(Event event) {
        StringIdInfo idInfo = new Gson().fromJson(event.payload, StringIdInfo.class);
        DeviceDetailInfoResponse deviceDetailInfoResponse = new DeviceDetailInfoResponse();
        IdentityEntity deviceIdentity = identityRepository.findFirstByIdAndType(idInfo.getId(), DEVICE_IDENTITY_TYPE);
        if(deviceIdentity != null){
            IdentityEntity appDomain = identityRepository.findFirstByMainAppIdAndType(deviceIdentity.getMainAppId(), APP_DOMAIN_IDENTITY_TYPE);
            TokenEntity accessToken = tokenRepository.findFirstByHolderIsAndActiveIs(deviceIdentity.getId(), ACTIVE);
            TokenEntity refreshToken = tokenRepository.findFirstByHolderIsAndActiveIs(deviceIdentity.getId(), IN_ACTIVE);
            deviceDetailInfoResponse.setState(deviceIdentity.getActive());
            deviceDetailInfoResponse.setDeviceId(deviceIdentity.getId());
            deviceDetailInfoResponse.setDeviceName(deviceIdentity.getName());
            deviceDetailInfoResponse.setCategory(deviceIdentity.getCategory());
            deviceDetailInfoResponse.setDeviceType(String.valueOf(deviceIdentity.getSubType()));
            deviceDetailInfoResponse.setAccessToken(accessToken!=null ? accessToken.getTokenObject() : null);
            deviceDetailInfoResponse.setRefreshToken(refreshToken!=null ? refreshToken.getTokenObject() : null);
            deviceDetailInfoResponse.setExpiry(deviceIdentity.getExpiry());
            deviceDetailInfoResponse.setDescription(deviceIdentity.getDescription());
            deviceDetailInfoResponse.setTenantId(deviceIdentity.getTenantId());
            deviceDetailInfoResponse.setUserId(deviceIdentity.getUserId());
            deviceDetailInfoResponse.setCreated(deviceIdentity.getCreated());
            deviceDetailInfoResponse.setUpdated(deviceIdentity.getUpdated());
            deviceDetailInfoResponse.setAppDomain(appDomain.getName());
            deviceDetailInfoResponse.setAppDomainID(appDomain.getId());
            if(deviceIdentity.getSubType() == SUB_DEVICE_TYPE) {
                RelationEntity relationEntity = relationRepository.findFirstByTargetIdAndRelationType(deviceIdentity.getId(), PARENT);
                if(relationEntity != null)
                    deviceDetailInfoResponse.setGatewayId(relationEntity.getOriginatorId());
            }
            List<String> ids = new ArrayList<>();
            // active thi lay status va thong tin node
            if(deviceIdentity.getActive() == ACTIVE) {
                ids.add(deviceIdentity.getId());
                List<RelationService.DeviceStatus> deviceStatusList = getDeviceStatus(ids);
                deviceDetailInfoResponse.setState(deviceStatusList.get(0).deviceStatus);
                // get device info
                RequestPrimitive requestPrimitive = new RequestPrimitive();
                requestPrimitive.setFrom(vn.vnpt.oneiot.common.constants.Constants.CSE_ID_URI);
                requestPrimitive.setTo("nod-" + deviceIdentity.getId());
                requestPrimitive.setOperation(Operation.RETRIEVE);
                MessageProperties messageProperties = new MessageProperties();
                messageProperties.setReplyTo(ROUTING_KEY_API_AUTHORIZATION);
                String exchange = getExchangeFromRoutingKey(deviceRoutingKeyM2M);
                Event event1 = new Event();
                event1.id = UUID.randomUUID().toString();
                event1.payload = new Gson().toJson(requestPrimitive);
                event1.type = AMQPConstant.EVENTTYPE_REQUEST;
                event1 = eventBus.publishAndReceiveSynch(exchange,deviceRoutingKeyM2M, event1, messageProperties, 30000);
                ResponsePrimitive responsePrimitive = ObjectMapperUtil.objectMapper(event1.payload, ResponsePrimitive.class);
                NodeEntity nodeEntity = ObjectMapperUtil.objectMapper(responsePrimitive.getResourceEntity(), NodeEntity.class);
                if(nodeEntity != null) {
                    DeviceInfoResponse deviceInfoResponse = convertFromNodeEntityToDeviceInfoResponse(nodeEntity);
                    deviceDetailInfoResponse.setDeviceDetail(Arrays.asList(deviceInfoResponse));
                }
            }
            deviceDetailInfoResponse.setErrorCode(ResponseStatusCode.OK.intValue());
            deviceDetailInfoResponse.setErrorMsg(vn.vnpt.oneiot.core.constants.Constants.ResultMessage.SUCCESS);
            event.payload = ObjectMapperUtil.toJsonString(deviceDetailInfoResponse);
            event.statusCode = ResponseStatusCode.OK.intValue();
        }else {
            event.statusCode = ResponseStatusCode.NOT_FOUND.intValue();
            event.errorText = "Device not found";
        }
        return event;
    }
    private DeviceInfoResponse convertFromNodeEntityToDeviceInfoResponse(NodeEntity nodeEntity) {
        DeviceInfoEntity deviceInfo = nodeEntity.getChildDeviceInfoEntities().get(0);
        DeviceInfoResponse deviceInfoResponse = new DeviceInfoResponse();
        deviceInfoResponse.setDeviceLabel(deviceInfo.getDeviceLabel());
        deviceInfoResponse.setLocalName(deviceInfo.getDeviceName());
        deviceInfoResponse.setProductType(deviceInfo.getDeviceType());
        deviceInfoResponse.setProtocol(deviceInfo.getProtocol());
        deviceInfoResponse.setManufacturer(deviceInfo.getManufacturer());
        deviceInfoResponse.setManufactureDate(deviceInfo.getManufacturingDate());
        deviceInfoResponse.setModel(deviceInfo.getModel());
        deviceInfoResponse.setSubModel(deviceInfo.getSubModel());
        deviceInfoResponse.setFirmVersion(deviceInfo.getFwVersion());
        deviceInfoResponse.setSoftVersion(deviceInfo.getSwVersion());
        deviceInfoResponse.setHardVersion(deviceInfo.getHwVersion());
        deviceInfoResponse.setOsVersion(deviceInfo.getOsVersion());
        deviceInfoResponse.setCountry(deviceInfo.getCountry());
        deviceInfoResponse.setLocation(deviceInfo.getLocation());
        deviceInfoResponse.setImei(deviceInfo.getImei());
        deviceInfoResponse.setImsi(nodeEntity.getImsi());
        return deviceInfoResponse;
    }

    private Event countDeviceByTenant(Event event) {
        logger.info("[countDeviceByTenant] receive event : {}",ObjectMapperUtil.toJsonString(event));
        String tenantCode = event.payload ;
        if(StringUtils.isEmpty(tenantCode)){
            event.statusCode = ResponseStatusCode.BAD_REQUEST.intValue();
            logger.error("[countDeviceByTenant] tenantCode empty , event : {}",ObjectMapperUtil.toJsonString(event));
            return event;
        }
        Long deviceTotal = identityRepository.countByTenantIdAndType(tenantCode,DEVICE_IDENTITY_TYPE);
        event.statusCode = ResponseStatusCode.OK.intValue();
        event.payload = deviceTotal.toString();
        logger.info("[countDeviceByTenant] send event : {}",ObjectMapperUtil.toJsonString(event));
        return event ;
    }
    private Event getDeviceIdsFromGroupIds (Event event) {
        List<String> groupIds = new Gson().fromJson(event.payload, new TypeToken<List<String>>(){}.getType());
        Set<String> deviceIds = new HashSet<>();
        List<IdentityEntity> groupList = identityRepository.findAllByIdIn(groupIds);
        for (IdentityEntity group : groupList) {
            deviceIds.addAll(group.getMemberIds());
        }
        List<String> deviceIdsList = new ArrayList<>(deviceIds);
        event.payload = new Gson().toJson(deviceIdsList);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

}
