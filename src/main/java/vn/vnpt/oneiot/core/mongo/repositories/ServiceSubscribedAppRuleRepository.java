package vn.vnpt.oneiot.core.mongo.repositories;

import org.springframework.data.mongodb.repository.Query;
import vn.vnpt.oneiot.common.entities.ServiceSubscribedAppRuleEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCustomRepository;

import java.util.List;

public interface ServiceSubscribedAppRuleRepository extends MgCustomRepository<ServiceSubscribedAppRuleEntity, String> {
    ServiceSubscribedAppRuleEntity findFirstByNameIsAndParentIDIs(String name, String parentId);

    @Query("?1")
    List<ServiceSubscribedAppRuleEntity> nativeQuery(String query);
}
