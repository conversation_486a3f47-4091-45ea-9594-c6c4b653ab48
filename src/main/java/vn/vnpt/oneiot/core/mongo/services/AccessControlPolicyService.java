package vn.vnpt.oneiot.core.mongo.services;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.common.entities.*;
import vn.vnpt.oneiot.common.resource.AccessControlPolicy;
import vn.vnpt.oneiot.core.mongo.generic.MgCrudService;
import vn.vnpt.oneiot.core.mongo.repositories.AccessControlContextRepository;
import vn.vnpt.oneiot.core.mongo.repositories.AccessControlOriginatorRepository;
import vn.vnpt.oneiot.core.mongo.repositories.AccessControlPolicyRepository;
import vn.vnpt.oneiot.core.mongo.repositories.AccessControlRuleRepository;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by huyvv
 * Date: 09/03/2020
 * Time: 5:51 PM
 * for all issues, contact me: <EMAIL>
 **/
@Service
@Transactional
public class AccessControlPolicyService extends MgCrudService<AccessControlPolicyEntity, String> {
    @SuppressWarnings("unused")
    private static Logger logger = LoggerFactory.getLogger(AccessControlPolicyService.class);

    private AccessControlPolicyRepository accessControlPolicyRepository;
    private AccessControlRuleRepository accessControlRuleRepository;
    private AccessControlOriginatorRepository accessControlOriginatorRepository;
    private AccessControlContextRepository accessControlContextRepository;
    private DynamicRoleService dynamicRoleService;

    @Autowired
    public void setAccessControlContextRepository(AccessControlContextRepository accessControlContextRepository) {
        this.accessControlContextRepository = accessControlContextRepository;
    }

    public AccessControlPolicyService(AccessControlPolicyRepository repository) {
        super(AccessControlPolicyEntity.class);
        logger.info("init AccessControlPolicyService");
        this.repository = this.accessControlPolicyRepository = repository;
    }

    @Autowired
    public void setDynamicRoleService(DynamicRoleService dynamicRoleService) {
        this.dynamicRoleService = dynamicRoleService;
    }

    @Autowired
    public void setAccessControlOriginatorRepository(AccessControlOriginatorRepository accessControlOriginatorRepository) {
        this.accessControlOriginatorRepository = accessControlOriginatorRepository;
    }

    @Autowired
    public void setAccessControlRuleRepository(AccessControlRuleRepository accessControlRuleRepository) {
        this.accessControlRuleRepository = accessControlRuleRepository;
    }

    public AccessControlPolicyEntity findByTargetName(String targetName){
        return accessControlPolicyRepository.findFirstByNameEndingWith(targetName);
    }

    public AccessControlOriginatorEntity saveAccessControlOriginatorEntity (AccessControlOriginatorEntity entity) {
        return accessControlOriginatorRepository.save(entity);
    }

    public AccessControlRuleEntity saveAccessControlRuleEntity(AccessControlRuleEntity entity) {
        return accessControlRuleRepository.save(entity);
    }

    public RoleEntity createRole (RoleEntity entity) {
        return dynamicRoleService.create(entity);
    }

    public RoleEntity findRoleByRoleIdAndHolder (String roleId, String holder) {
        return dynamicRoleService.findByRoleIdAndHolder(roleId, holder);
    }

    public AccessControlPolicyEntity findByNameAndParentId(String name, String parentId) {
        return accessControlPolicyRepository.findFirstByNameAndParentID(name, parentId);
    }


    /** Cascade create require all DbRef element already set id value **/
    public AccessControlPolicyEntity cascadeCreate(AccessControlPolicyEntity acpEntity) {
        if (acpEntity == null) return null;
        cascadeSaveDbRefField(acpEntity);
        return create(acpEntity);
    }

    /** Cascade update require all DbRef element already set id value **/
    public AccessControlPolicyEntity cascadeUpdate(AccessControlPolicyEntity acpEntity) {
        if (acpEntity == null) return null;
        cascadeSaveDbRefField(acpEntity);
        return update(acpEntity);
    }

    /** Require acpEntity != null**/
    private void cascadeSaveDbRefField(AccessControlPolicyEntity acpEntity) {
        List<AccessControlRuleEntity> acrEntities = new ArrayList<>();
        List<AccessControlOriginatorEntity> acoEntities = new ArrayList<>();
        List<AccessControlContextEntity> accoEntities = new ArrayList<>();
        if (acpEntity.getPrivileges() != null && acpEntity.getPrivileges().size() > 0) {
            acrEntities.addAll(acpEntity.getPrivileges());
        }
        if (acpEntity.getSelfPrivileges() != null && acpEntity.getSelfPrivileges().size() > 0) {
            acrEntities.addAll(acpEntity.getSelfPrivileges());
        }
        if (acrEntities.size() > 0) {
            for (AccessControlRuleEntity acrEntity:acrEntities) {
                if (acrEntity.getAccessControlOriginators() != null && acrEntity.getAccessControlOriginators().size() > 0) {
                    acoEntities.addAll(acrEntity.getAccessControlOriginators());
                }
                if (acrEntity.getAccessControlContexts() != null && acrEntity.getAccessControlContexts().size() > 0) {
                    accoEntities.addAll(acrEntity.getAccessControlContexts());
                }
            }
        }
        if (acoEntities.size() > 0) accessControlOriginatorRepository.saveAll(acoEntities);
        if (accoEntities.size() > 0) accessControlContextRepository.saveAll(accoEntities);
        if (acrEntities.size() > 0) accessControlRuleRepository.saveAll(acrEntities);
    }

    @Override
    protected void afterDelete(AccessControlPolicyEntity entity) {
        List<AccessControlRuleEntity> tobeDelete = new ArrayList<>();
        if (entity.getPrivileges() != null && entity.getPrivileges().size() > 0) {
            tobeDelete.addAll(entity.getSelfPrivileges());
        }
        if (entity.getSelfPrivileges() != null && entity.getSelfPrivileges().size() > 0) {
            tobeDelete.addAll(entity.getSelfPrivileges());
        }
        if (tobeDelete.size() > 0) {
            List<AccessControlContextEntity> accoToBeDelete = new ArrayList<>();
            for (AccessControlRuleEntity acrEntity: tobeDelete) {
                if (acrEntity.getAccessControlContexts() != null && acrEntity.getAccessControlContexts().size() > 0)
                    accoToBeDelete.addAll(acrEntity.getAccessControlContexts());
            }
            if (accoToBeDelete.size() > 0) accessControlContextRepository.deleteAll(accoToBeDelete);
            accessControlRuleRepository.deleteAll(tobeDelete);
        }
    }

    public void deleteAllByHierarchicalURIStartsWith(String prefix) {
        List<AccessControlPolicyEntity> acpEntityList = accessControlPolicyRepository.findAllByHierarchicalURIStartsWith(prefix);
        if (acpEntityList != null && acpEntityList.size() > 0) {
            List<AccessControlRuleEntity> acrToBeDelete = new ArrayList<>();
            for (AccessControlPolicyEntity acpEntity: acpEntityList) {
                if (acpEntity.getPrivileges() != null && acpEntity.getPrivileges().size() > 0) {
                    acrToBeDelete.addAll(acpEntity.getSelfPrivileges());
                }
                if (acpEntity.getSelfPrivileges() != null && acpEntity.getSelfPrivileges().size() > 0) {
                    acrToBeDelete.addAll(acpEntity.getSelfPrivileges());
                }
                if (acrToBeDelete.size() > 0) {
                    List<AccessControlContextEntity> accoToBeDelete = new ArrayList<>();
                    List<AccessControlOriginatorEntity> acorToBeDelete = new ArrayList<>();
                    for (AccessControlRuleEntity acrEntity: acrToBeDelete) {
                        if (acrEntity.getAccessControlContexts() != null && acrEntity.getAccessControlContexts().size() > 0)
                            accoToBeDelete.addAll(acrEntity.getAccessControlContexts());
                        if (acrEntity.getAccessControlOriginators() != null && acrEntity.getAccessControlOriginators().size() > 0)
                            acorToBeDelete.addAll(acrEntity.getAccessControlOriginators());
                    }
                    if (accoToBeDelete.size() > 0) accessControlContextRepository.deleteAll(accoToBeDelete);
                    if (acorToBeDelete.size() > 0) accessControlOriginatorRepository.deleteAll(acorToBeDelete);
                    accessControlRuleRepository.deleteAll(acrToBeDelete);
                }
            }
            accessControlPolicyRepository.deleteAll(acpEntityList);
        }
    }
}
