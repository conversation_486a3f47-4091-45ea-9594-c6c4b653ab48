package vn.vnpt.oneiot.core.mongo.repositories;

import vn.vnpt.oneiot.common.entities.ServiceSubscribedNodeEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCustomRepository;

public interface ServiceSubscribedNodeRepository extends MgCustomRepository<ServiceSubscribedNodeEntity, String> {
    ServiceSubscribedNodeEntity findFirstByCseidEquals(String cseId);
    ServiceSubscribedNodeEntity findFirstByNameIsAndParentIDIs(String name, String parentId);
}
