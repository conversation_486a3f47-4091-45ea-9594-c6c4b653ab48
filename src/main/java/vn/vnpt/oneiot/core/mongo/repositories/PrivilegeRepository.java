package vn.vnpt.oneiot.core.mongo.repositories;

import org.springframework.stereotype.Repository;
import vn.vnpt.oneiot.core.mongo.entity.PrivilegeEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCustomRepository;

import java.util.List;

@Repository
public interface PrivilegeRepository extends MgCustomRepository<PrivilegeEntity,String> {
    PrivilegeEntity findFirstByName(String name);
    List<PrivilegeEntity> findAllByNameIn(List<String> name);
    List<PrivilegeEntity> findAllByIdIn(List<String> ids);
}
