package vn.vnpt.oneiot.core.mongo.services;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.core.mongo.entity.CountryEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCrudAdminService;
import vn.vnpt.oneiot.core.mongo.generic.MgCrudService;
import vn.vnpt.oneiot.core.mongo.generic.MgNoneCrudService;
import vn.vnpt.oneiot.core.mongo.repositories.CountryRepository;

import javax.transaction.Transactional;

/**
 * Created by hoangpd
 * Date: 04/11/2023
 * Time: 3:37 PM
 * for all issues, contact me: <EMAIL>
 **/
@Service
@Transactional
public class CountryService extends MgCrudAdminService<CountryEntity, String> {
    private final Logger log = LoggerFactory.getLogger(CountryService.class);

    private CountryRepository countryRepository;

    public CountryService(CountryRepository countryRepository) {
        super(CountryEntity.class);
        this.repository = this.countryRepository = countryRepository;
    }
}
