package vn.vnpt.oneiot.core.mongo.services;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.base.constants.Constants;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.core.generic.dto.CategoriesListResponse;
import vn.vnpt.oneiot.core.mongo.entity.Category;
import vn.vnpt.oneiot.core.mongo.repositories.CategoryRepository;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class CategoryService {
    private static Logger logger = LoggerFactory.getLogger(CategoryService.class);
    @Autowired
    private CategoryRepository categoryRepsoitory;

    @Autowired
    MongoTemplate mongoTemplate;

    public Event processGetAll(Event event) {
        List<Category> categories = categoryRepsoitory.findAll();
        logger.info(String.valueOf(categories.size()));
        Map<String, String> resultsMap = new HashMap<>();
        for (Category category : categories) {
            resultsMap.put(category.getId(), category.getName());
        }
        List<CategoriesListResponse> resp = new ArrayList<>();

        for (Category category : categories) {
            if (category.getParentId().equals("-1")) {
                continue;
            }
            CategoriesListResponse categoriesListResponse = new CategoriesListResponse();
            BeanUtils.copyProperties(category, categoriesListResponse);
            categoriesListResponse.setParentId(resultsMap.get(category.getParentId()));
            resp.add(categoriesListResponse);
        }
        event.payload = ObjectMapperUtil.toJsonString(resp);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }
}
