package vn.vnpt.oneiot.core.mongo.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by huyvv
 * Date: 16/01/2020
 * Time: 11:00 AM
 * for all issues, contact me: <EMAIL>
 **/
@Document(collection = "BASE_USERS")
public class UserEntity extends MgAbstractEntity {

    private static final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    private String firstName;
    private String lastName;
    private Long activated; //timestamp activated
//    @JsonIgnore
    private String activationToken;
//    @JsonIgnore
    private Long activationTokenCreated; //timestamp
    @JsonIgnore
    private String forgotPasswordToken;
    @JsonIgnore
    private Long forgotPasswordTokenCreated; //timestamp
    private String langKey;
    @Transient
    private String domain;

    private String email;

    private String password;

    @Transient
    private String confirmPassword;

    @Transient
    private String newPassword;
    private List<String> roleIds;

    @Transient
    private Set<String> authorities;
    private String fullName;

    private String address;
    private String phone;
    private String positions;


    private String userAvatar;

    @Transient
    private String registerOrganization;

    //@Transient
    private String jwtToken;

    private Integer type;
    private String countryId;
    private String companyName;
    private String tenantId;
    @Transient
    private String authCode;


    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getUserAvatar() {
        return userAvatar;
    }

    public void setUserAvatar(String userAvatar) {
        this.userAvatar = userAvatar;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPositions() {
        return positions;
    }

    public void setPositions(String positions) {
        this.positions = positions;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public Long getActivated() {
        return activated;
    }

    public void setActivated(Long activated) {
        this.activated = activated;
    }

    public String getActivationToken() {
        return activationToken;
    }

    public void setActivationToken(String activationToken) {
        this.activationToken = activationToken;
    }

    public Long getActivationTokenCreated() {
        return activationTokenCreated;
    }

    public void setActivationTokenCreated(Long activationTokenCreated) {
        this.activationTokenCreated = activationTokenCreated;
    }

    public String getForgotPasswordToken() {
        return forgotPasswordToken;
    }

    public void setForgotPasswordToken(String forgotPasswordToken) {
        this.forgotPasswordToken = forgotPasswordToken;
    }

    public Long getForgotPasswordTokenCreated() {
        return forgotPasswordTokenCreated;
    }

    public void setForgotPasswordTokenCreated(Long forgotPasswordTokenCreated) {
        this.forgotPasswordTokenCreated = forgotPasswordTokenCreated;
    }

    public String getLangKey() {
        return langKey;
    }

    public void setLangKey(String langKey) {
        this.langKey = langKey;
    }



    public void setAuthorities(Set<String> authorities) {
        this.authorities = authorities;
    }

    public Set<String> getAuthorities() {
        if (authorities == null) authorities = new HashSet<>();
        return authorities;
    }

    public UserEntity() {
//        this.roles = new HashSet<RoleEntity>();
        this.authorities = new HashSet<>();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

//    @JsonIgnore
    public void setEncryptedPassword(String password) {
        this.password = passwordEncoder.encode(password);
    }


    public Boolean authenticate(String password) {
        return passwordEncoder.matches(password,this.password);
    }

    public List<String> getRoleIds() {
        if (roleIds == null) roleIds = new ArrayList<>();
        return roleIds;
    }

    public void setRoleIds(List<String> roleIds) {
        this.roleIds = roleIds;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getConfirmPassword() {
        return confirmPassword;
    }

    public void setConfirmPassword(String confirmPassword) {
        this.confirmPassword = confirmPassword;
    }

    public String getNewPassword() {
        return newPassword;
    }

    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }

    public String getRegisterOrganization() {
        return registerOrganization;
    }

    public void setRegisterOrganization(String registerOrganization) {
        this.registerOrganization = registerOrganization;
    }

    public String getJwtToken() {
        return jwtToken;
    }

    public void setJwtToken(String jwtToken) {
        this.jwtToken = jwtToken;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getCountryId() {
        return countryId;
    }

    public void setCountryId(String countryId) {
        this.countryId = countryId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    public String getAuthCode() {
        return this.authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }
}
