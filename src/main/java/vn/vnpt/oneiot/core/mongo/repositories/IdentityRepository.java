package vn.vnpt.oneiot.core.mongo.repositories;

import io.swagger.models.auth.In;
import vn.vnpt.oneiot.core.mongo.entity.IdentityEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCustomRepository;

import javax.persistence.Id;
import java.util.Collection;
import java.util.List;

public interface IdentityRepository extends MgCustomRepository<IdentityEntity, String> {
    IdentityEntity findFirstByNameAndTypeAndMainAppId(String name, Integer type, String mainAppId);
    IdentityEntity findFirstByNameAndType(String name, Integer type);
    IdentityEntity findFirstByNameAndTypeIn(String name, List<Integer> type);
    IdentityEntity findFirstByMainAppIdAndType(String mainAppId, Integer type);
    List<IdentityEntity> findByMainAppIdAndType(String mainAppId, Integer type);
    List<IdentityEntity> findByMainAppIdAndTypeAndSubType(String mainAppId, Integer type, Integer subType);
    List<IdentityEntity> findByNameAndType(String name, Integer type);
    List<IdentityEntity> findAllByIdIn(List<String> ids);
    List<IdentityEntity> findAllByNameInAndTypeIn(Collection<String> name, Collection<Integer> types);
    List<IdentityEntity> findByType(Integer type);
    List<IdentityEntity> findByIdInAndSubType(Collection<String> ids, Integer subType);
    IdentityEntity findFirstById(String id);
    List<IdentityEntity> findByUserIdAndType(String userId,Integer type);
    List<IdentityEntity> findAllByTenantIdAndType(String tenantId, Integer type);
    List<IdentityEntity> findByMainAppIdAndTypeAndIdNotInOrderByNameAsc(String mainAppId, Integer type, List<String> ids);
    IdentityEntity findFirstByIdAndType(String id , int type);
    List<IdentityEntity> findByTenantIdAndTypeAndMainAppIdAndIdNotInOrderByNameAsc(String tenantId, Integer type,String mainAppId, List<String> ids);
    IdentityEntity findFirstByIdAndUserId(String id, String userId);
    List<IdentityEntity> findAllByTenantIdAndTypeAndActiveIn(String tenantId, Integer type, List<Integer> actives);
    List<IdentityEntity> findAllByTypeAndMainAppId(Integer type, String mainAppId);
    Integer countByNameInAndTypeIn(List<String> names, List<Integer> types);
    Long countByTenantIdAndType(String tenantId, Integer type);
    Long countByTenantIdAndTypeAndActiveNotIn(String tenantId, Integer type, List<Integer> actives);
    Long countByTenantIdAndTypeAndSubTypeAndActiveNotIn(String tenantId, Integer type, Integer supType, List<Integer> actives);
}
