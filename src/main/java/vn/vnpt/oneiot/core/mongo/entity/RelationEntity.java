package vn.vnpt.oneiot.core.mongo.entity;

import org.springframework.data.mongodb.core.mapping.Document;

import static vn.vnpt.oneiot.core.generic.Constants.RELATION_TYPE.COMMON;
import static vn.vnpt.oneiot.core.generic.Constants.RELATION_TYPE.ADMIN;
import static vn.vnpt.oneiot.core.generic.Constants.RELATION_TYPE.OWNER;
import static vn.vnpt.oneiot.core.generic.Constants.RELATION_TYPE.PARENT;

@Document(value = "BASE_RELATION_ENTITY")
public class RelationEntity extends MgAbstractEntity {
    private String originatorId;
    private Integer origitorType;
    private String origitorName;
    private String targetId;
    private Integer targetType;
    private String targetName;
    private Integer relationType;

    public Integer getOrigitorType() {
        return origitorType;
    }

    public void setOrigitorType(Integer origitorType) {
        this.origitorType = origitorType;
    }

    public String getOrigitorName() {
        return origitorName;
    }

    public void setOrigitorName(String origitorName) {
        this.origitorName = origitorName;
    }

    public Integer getTargetType() {
        return targetType;
    }

    public void setTargetType(Integer targetType) {
        this.targetType = targetType;
    }

    public String getTargetName() {
        return targetName;
    }

    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }

    public String getRelationTypeStr() {
        switch (relationType) {
            case OWNER:
                return "owner";
            case PARENT:
                return "parent";
            case COMMON:
                return "common";
            case ADMIN:
                return "admin";
        }
        return null;
    }

    public Integer getRelationType() {
        return relationType;
    }

    public void setRelationType(Integer relationType) {
        this.relationType = relationType;
    }

    public String getOriginatorId() {
        return originatorId;
    }

    public void setOriginatorId(String originatorId) {
        this.originatorId = originatorId;
    }

    public String getTargetId() {
        return targetId;
    }

    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "RelationEntity{" +
                ", originatorId='" + getOriginatorId() + "'" +
                ", origitorType='" + getOrigitorType() + "'" +
                ", origitorName='" + getOrigitorName() + "'" +
                ", targetId='" + getTargetId() + "'" +
                ", targetType='" + getTargetType() + "'" +
                ", targetName='" + getTargetName() + "'" +
                ", relationType='" + getRelationTypeStr() + "'" +
                "}";
    }
}
