package vn.vnpt.oneiot.core.mongo.entity;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * Created by hoangpd
 * Date: 04/12/2023
 * Time: 3:30 PM
 * for all issues, contact me: <EMAIL>
 **/
@Document(collection = "BASE_PRIVILEGES")
public class PrivilegeEntity extends MgAbstractEntity {
    private String name;
    private String description;
    @Field("display_name")
    private String displayName;

    @Field("category_name")
    private String categoryName;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public String getName() {
        return this.name;
    }

    public PrivilegeEntity name(String name) {
        this.setName(name);
        return this;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return this.description;
    }

    public PrivilegeEntity description(String description) {
        this.setDescription(description);
        return this;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDisplayName() {
        return this.displayName;
    }

    public PrivilegeEntity displayName(String displayName) {
        this.setDisplayName(displayName);
        return this;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getCategoryName() {
        return this.categoryName;
    }

    public PrivilegeEntity categoryName(String categoryName) {
        this.setCategoryName(categoryName);
        return this;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }


    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "PrivilegeEntity{" +
                ", name='" + getName() + "'" +
                ", description='" + getDescription() + "'" +
                ", displayName='" + getDisplayName() + "'" +
                ", categoryName='" + getCategoryName() + "'" +
                "}";
    }
}
