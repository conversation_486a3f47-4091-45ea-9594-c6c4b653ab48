package vn.vnpt.oneiot.core.mongo.repositories;

import vn.vnpt.oneiot.core.mongo.entity.TenantEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCustomRepository;

import java.util.List;
import java.util.Set;


public interface TenantRepository extends MgCustomRepository<TenantEntity, String> {
    TenantEntity findFirstByNameAndCreatedBy(String name, String email);
    TenantEntity findFirstByCreatedBy(String email);
    void deleteAllByCodeIn(Set<String> ids);
    List<TenantEntity> findAllByActiveInAndUpdatedLessThan(int[] active, long time);
    List<TenantEntity> findAllByActiveInAndCreatedLessThan(int[] active, long time);
    TenantEntity findFirstByCode(String code);
    List<TenantEntity> findAllByCodeIn(List<String> id);
    TenantEntity findFirsByIotMarketPlaceUUID(String iotMarketPlaceUUID);
}
