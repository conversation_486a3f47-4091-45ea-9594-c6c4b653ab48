package vn.vnpt.oneiot.core.mongo.repositories;

import org.springframework.stereotype.Repository;
import vn.vnpt.oneiot.common.entities.ResourceEntity;
import vn.vnpt.oneiot.core.mongo.entity.CountryEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCustomRepository;

import java.io.Serializable;

/**
 * Created by hoangpd
 * Date: 04/11/2023
 * Time: 3:30 PM
 * for all issues, contact me: <EMAIL>
 **/

@Repository
@SuppressWarnings({"unused", "unused"})
public interface CountryRepository extends MgCustomRepository<CountryEntity, String> {
}