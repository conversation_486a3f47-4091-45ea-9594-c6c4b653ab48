package vn.vnpt.oneiot.core.mongo.services;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.common.entities.AccessControlRuleEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgNoneCrudService;
import vn.vnpt.oneiot.core.mongo.repositories.AccessControlRuleRepository;

import javax.transaction.Transactional;

/**
 * Created by huyvv
 * Date: 09/03/2020
 * Time: 5:51 PM
 * for all issues, contact me: <EMAIL>
 **/
@Service
@Transactional
public class AccessControlRuleService extends MgNoneCrudService<AccessControlRuleEntity, String> {
    @SuppressWarnings("unused")
    private static Logger logger = LoggerFactory.getLogger(AccessControlRuleService.class);

    private AccessControlRuleRepository accessControlRuleRepository;

    public AccessControlRuleService(AccessControlRuleRepository repository) {
        super(AccessControlRuleEntity.class);
        this.repository = this.accessControlRuleRepository = repository;
    }
}
