package vn.vnpt.oneiot.core.mongo.services;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.mongodb.QueryBuilder;
import com.mongodb.client.result.DeleteResult;
import io.jsonwebtoken.*;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.BasicQuery;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.base.constants.Constants;
import vn.vnpt.oneiot.base.dto.Auth2Request;
import vn.vnpt.oneiot.base.dto.Auth2Response;
import vn.vnpt.oneiot.base.dto.UserAuth2Info;
import vn.vnpt.oneiot.base.errors.ErrorKey;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.event.EventBus;
import vn.vnpt.oneiot.base.redis.ObjectCache;
import vn.vnpt.oneiot.base.utils.DateUtils;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.common.constants.ShortName;
import vn.vnpt.oneiot.common.entities.PermissionEntity;
import vn.vnpt.oneiot.common.entities.TokenEntity;
import vn.vnpt.oneiot.common.exceptions.PlatformException;
import vn.vnpt.oneiot.common.resource.ResponsePrimitive;
import vn.vnpt.oneiot.common.utils.Util;
import vn.vnpt.oneiot.core.CSEInitialize;
import vn.vnpt.oneiot.core.api.internal.service.ConfigService;
import vn.vnpt.oneiot.core.api.nbi.models.*;
import vn.vnpt.oneiot.core.configuration.ApplicationProperties;
import vn.vnpt.oneiot.core.constants.TransactionState;
import vn.vnpt.oneiot.core.generic.Constants.UserType;
import vn.vnpt.oneiot.core.mongo.entity.Application;
import vn.vnpt.oneiot.core.mongo.entity.IdentityEntity;
import vn.vnpt.oneiot.core.mongo.entity.TenantEntity;
import vn.vnpt.oneiot.core.mongo.entity.UserEntity;
import vn.vnpt.oneiot.core.mongo.repositories.*;
import vn.vnpt.oneiot.core.mongo.generic.MgCrudService;
import vn.vnpt.oneiot.core.utils.AuthByPlatformAlgorithm;
import vn.vnpt.oneiot.core.utils.ErrorUtils;
import vn.vnpt.oneiot.core.redis.dto.Transaction;
import vn.vnpt.oneiot.core.utils.SecurityUtils;

import javax.transaction.Transactional;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static vn.vnpt.oneiot.base.constants.AMQPConstant.*;
import static vn.vnpt.oneiot.core.constants.Constants.*;
import static vn.vnpt.oneiot.core.constants.Constants.AUTHEN_ENDPOINT_ERROR_KEY.*;
import static vn.vnpt.oneiot.core.generic.Constants.IDENTITY_TYPE.*;
import static vn.vnpt.oneiot.core.generic.Constants.Method.*;

/**
 * Author: kiendt
 * Date: 4/17/2020
 * Contact: <EMAIL>
 */
@Service
@Transactional
public class TokenService extends MgCrudService<TokenEntity, String> {

    public static final int TOKEN_TYPE_USER = 1;
    public static final int TOKEN_TYPE_APP = 2;
    public static final int TOKEN_TYPE_DEVICE = 3;
    private static final long EXPIRED_DATE = 3153600000L; //100 years in seconds

    //check token include accesstoken + holdername
    // Cache constants
    private static final String IDENTITY_CACHE_PREFIX = "identity:";
    private static final String CHECK_TOKEN_CACHE_PREFIX = "check_token_cache:";
    private static final String TOKEN_CACHE_PREFIX = "token:";
    private static final String TENANT_CACHE_PREFIX = "tenant:";
    private static final int CACHE_TTL = 300000; // 5 minutes in seconds

    @SuppressWarnings("unused")
    private static Logger logger = LoggerFactory.getLogger(TokenService.class);

    private TokenRepository tokenRepository;
    @Autowired
    private ApplicationRepository applicationRepository;
    @Autowired
    private UserRepository userRepository;

    @Autowired
    ObjectCache systemCache;

    @Autowired
    EventBus eventBus;

    @Value("${sendRefreshTokenToDevice: false}")
    Boolean sendRefreshTokenToDevice;

    @Autowired
    MongoTemplate mongoTemplate;

    public TokenService(TokenRepository repository) {
        super(TokenEntity.class);
        this.repository = this.tokenRepository = repository;
    }

    private UserService userService;
    private ApplicationProperties applicationProperties;
    private TenantRepository tenantRepository;
    private AuthByPlatformAlgorithm authByPlatformAlgorithm;
    private PermissionRepository permissionRepository;
    private ServiceSubscribedAppRuleService asarService;
    private ConfigService configService;
    private IdentityRepository identityRepository;

    @Autowired
    public void setIdentityRepository(IdentityRepository identityRepository) {
        this.identityRepository = identityRepository;
    }

    @Autowired
    public void setAsarService(ServiceSubscribedAppRuleService asarService) {
        this.asarService = asarService;
    }


    //<editor-fold desc="Khoi tao bean">

    @Autowired
    public void setPermissionRepository(PermissionRepository permissionRepository) {
        this.permissionRepository = permissionRepository;
    }

    @Autowired
    public void setApplicationProperties(ApplicationProperties applicationProperties) {
        this.applicationProperties = applicationProperties;
    }

    @Autowired
    public void setTenantRepository(TenantRepository tenantRepository) {
        this.tenantRepository = tenantRepository;
    }

    @Autowired
    public void setUserService(UserService userService) {
        this.userService = userService;
    }

    @Autowired
    public void setAuthByPlatformAlgorithm(AuthByPlatformAlgorithm authByPlatformAlgorithm) {
        this.authByPlatformAlgorithm = authByPlatformAlgorithm;
    }

    @Autowired
    public void setConfigService(ConfigService configService) {
        this.configService = configService;
    }
    //</editor-fold>

    //<editor-fold desc="tao token moi cho device/app/user">
    public TokenEntity generateToken(String mainAppId, String domainId, String domainName, String tenantName, String subject, Date expireTime, String userId, String username, String tenantId, String holderId, Date notBefore, PermissionEntity tokenPermission, Integer holderType, Integer subType, String authCode) {
        return create(generateTokenNoSave(mainAppId, domainId, domainName, tenantName, subject, expireTime, userId, username, tenantId, holderId, notBefore, tokenPermission, holderType, subType, authCode));
    }

    public TokenEntity generateTokenNoSave(String mainAppId, String domainId, String domainName, String tenantName, String subject, Date expireTime, String userId, String username, String tenantId, String holderId, Date notBefore, PermissionEntity tokenPermission, Integer holderType, Integer subType, String authCode) {
        // build Token
        TokenEntity tokenEntity = new TokenEntity();
        tokenEntity.setTokenName(subject);
        tokenEntity.setNotAfter(expireTime.getTime());
        tokenEntity.setIssuer(vn.vnpt.oneiot.common.constants.Constants.CSE_ID);
        tokenEntity.setHolder(holderId);
        tokenEntity.setNotBefore(notBefore.getTime());
        JsonObject extensionObject = new JsonObject();
        extensionObject.addProperty("tenantId", tenantId);
        extensionObject.addProperty("holder", holderId);
        if (userId != null) extensionObject.addProperty("userId", userId);
        if (mainAppId != null) extensionObject.addProperty("mainAppId", mainAppId);
        if (domainId != null) extensionObject.addProperty("appDomainId", domainId);
        if (domainName != null) extensionObject.addProperty("appDomainName", domainName);
        if (tenantName != null) extensionObject.addProperty("tenantName", tenantName);
        if (subType != null) extensionObject.addProperty("subType", subType);
        tokenEntity.setUserName(username);
        tokenEntity.setExtension(extensionObject.toString());
        tokenEntity.setAudience(vn.vnpt.oneiot.common.constants.Constants.CSE_ID);
        tokenEntity.setTokenId(ShortName.TOKEN + vn.vnpt.oneiot.common.constants.Constants.PREFIX_SEPERATOR + UUID.randomUUID());
        tokenEntity.setResourceID(tokenEntity.getTokenId());
        tokenEntity.setHolderType(holderType);
        tokenEntity.setTokenObject(jwtAccessToken(tokenEntity));
        if (tokenPermission != null) {
            tokenEntity.setPermissions(new ArrayList<>());
            tokenEntity.getPermissions().add(tokenPermission);
        }
        tokenEntity.setCreationTime(DateUtils.nowByLong());
        tokenEntity.setLastModifiedTime(tokenEntity.getCreationTime());
        tokenEntity.setActive(Constants.EntityStatus.ACTIVE);
        tokenEntity.setAuthCode(authCode);
        return tokenEntity;
    }
    //</editor-fold

    //<editor-fold desc="update token moi cho device/app/user">
    public TokenEntity updateAccessToken(TokenEntity tokenEntity, Date expireTime, Date notBefore) {
        tokenEntity.setNotAfter(expireTime.getTime());
        tokenEntity.setNotBefore(notBefore.getTime());
        tokenEntity.setTokenId(ShortName.TOKEN + vn.vnpt.oneiot.common.constants.Constants.PREFIX_SEPERATOR + UUID.randomUUID());
        tokenEntity.setTokenObject(jwtAccessToken(tokenEntity));
        return tokenEntity;
    }

    public TokenEntity updateRefreshToken(TokenEntity tokenEntity, String accessToken, Date notBefore) {
        // build Token
        long now = (new Date()).getTime();
        Date validity = new Date(now + this.applicationProperties.getTokenTime().getRefreshToken() * 1000);
        tokenEntity.setNotAfter(validity.getTime());
        tokenEntity.setNotBefore(notBefore.getTime());
        tokenEntity.setExtension(accessToken);
        tokenEntity.setTokenId(ShortName.TOKEN + vn.vnpt.oneiot.common.constants.Constants.PREFIX_SEPERATOR + UUID.randomUUID().toString());
        tokenEntity.setTokenObject(jwtRefreshToken(tokenEntity));
        return tokenEntity;
    }
    //</editor-fold>


    /**
     * Generate a token using JWT
     *
     * @param entity
     * @return
     */
    public String jwtAccessToken(TokenEntity entity) {
        long now = (new Date()).getTime();
        Date validity = new Date(now + EXPIRED_DATE * 1000);
        if (entity.getNotAfter() != null) validity = new Date(entity.getNotAfter());
        if (entity.getHolderType() == TOKEN_TYPE_DEVICE) {
            return Jwts.builder()
                    .setId(entity.getTokenId())
                    .setExpiration(validity)
                    .signWith(SignatureAlgorithm.HS256, vn.vnpt.oneiot.core.constants.Constants.JWT_SECRET)
                    .compact();
        }
        return Jwts.builder()
                .setSubject(entity.getTokenName())
                .setId(entity.getTokenId())
                .setExpiration(validity)
                .setNotBefore(new Date(entity.getNotBefore()))
                .setIssuer(entity.getIssuer())
                .setAudience(entity.getAudience())
                .claim(ShortName.HOLDER, entity.getHolder())
                .claim(ShortName.TOKEN_NAME, entity.getTokenName())
                .claim(ShortName.PERMISSIONS, entity.getPermissions())
                .claim(ShortName.EXTENSION, entity.getExtension())
                .claim(ShortName.USER_NAME, entity.getUserName())
                .claim(ShortName.TYPE, entity.getHolderType())
                .signWith(SignatureAlgorithm.HS256, vn.vnpt.oneiot.core.constants.Constants.JWT_SECRET)
                .compact();
    }

    /**
     * Generate a token using JWT
     *
     * @param entity
     * @return
     */
    public String jwtRefreshToken(TokenEntity entity) {
        long now = (new Date()).getTime();
        Date validity = new Date(now + this.applicationProperties.getTokenTime().getRefreshToken() * 1000);
        if (entity.getNotAfter() != null) validity = new Date(entity.getNotAfter());
        String refreshToken;
        if (entity.getHolderType() == TOKEN_TYPE_DEVICE) {
            refreshToken = Jwts.builder()
                    .setId(entity.getTokenId())
                    .setExpiration(validity)
                    .signWith(SignatureAlgorithm.HS256, vn.vnpt.oneiot.core.constants.Constants.JWT_SECRET)
                    .compact();
        } else {
            refreshToken = Jwts.builder()
                    .setSubject(entity.getTokenName())
                    .setId(entity.getTokenId())
                    .setExpiration(validity)
                    .setNotBefore(new Date(entity.getNotBefore()))
                    .setIssuer(entity.getIssuer())
                    .setAudience(entity.getAudience())
                    .claim(ShortName.HOLDER, entity.getHolder())
                    .claim(ShortName.TOKEN_NAME, entity.getTokenName())
                    .claim(ShortName.PERMISSIONS, entity.getPermissions())
                    .claim(ShortName.EXTENSION, entity.getExtension())
                    .claim(ShortName.USER_NAME, entity.getUserName())
                    .signWith(SignatureAlgorithm.HS256, vn.vnpt.oneiot.core.constants.Constants.JWT_SECRET)
                    .compact();
        }

//        logger.info("Generate refresh token #{} with expireTime #{}", refreshToken, validity.getTime());
        return refreshToken;
    }

    public Event deleteTokenForUser(Event event) {
        try {
            JsonObject payload = new Gson().fromJson(event.payload, JsonObject.class);
            if (payload != null && !payload.isJsonNull()) {
                String userName = "";
                if (payload.has("userName")) userName = payload.get("userName").getAsString();
                String token = "";
                if (payload.has("token")) token = payload.get("token").getAsString();
                if (!com.google.common.base.Strings.isNullOrEmpty(userName) && !com.google.common.base.Strings.isNullOrEmpty(token)) {
                    TokenEntity tokenEntity = tokenRepository.findFirstByTokenObjectIsAndUserNameIs(token, userName);
                    if (tokenEntity != null) delete(tokenEntity.getResourceID());
                    event.statusCode = ResponseStatusCode.OK.intValue();
                }
            }
        } catch (Exception e) {
            event.statusCode = ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue();
            event.errorText = e.getMessage();
            logger.error(e.getMessage(), e);
        }
        return event;
    }

    /**
     * event.payload = Class LoginInfo
     *
     * @param event
     * @return
     */
    public Event createTokenForUser(Event event) {
//        LoginInfo loginInfo = ObjectMapperUtil.objectMapper(event.payload, LoginInfo.class);
        LoginInfo loginInfo = Util.getInstanceGsonCustom().fromJson(event.payload, LoginInfo.class);
        String clientId = null;
        String responseType = null;
        String redirectUri = null;
        if (loginInfo.getParams() != null) {
            clientId = loginInfo.getParams().getOrDefault("client_id", "").toString();
            responseType = loginInfo.getParams().getOrDefault("response_type", "").toString();
            redirectUri = loginInfo.getParams().getOrDefault("redirect_uri", "").toString();
        }
        Application application = null;
        String authCode = String.format("%s.%s", UUID.randomUUID().toString(), Base64.getEncoder().encode("name=ONEIOT_PLATFORM".getBytes(StandardCharsets.UTF_8)));
        if (clientId != null && responseType != null && redirectUri != null) {
            application = applicationRepository.findById(clientId).orElse(null);
            if (application != null) {
                if (loginInfo.getCookie() != null) {
                    authCode = String.format("%s.%s", UUID.randomUUID().toString(), Base64.getEncoder().encode(loginInfo.getCookie().getBytes(StandardCharsets.UTF_8)));
                }
            }
        }
        UserEntity userEntity = userService.authenticate(loginInfo.getEmail(), loginInfo.getPassword());
        if (userEntity == null || !userEntity.getActive().equals(Constants.EntityStatus.ACTIVE)) {
            return handlerLoginError(event, loginInfo);
        }
        TenantEntity tenantEntity = tenantRepository.findFirstByCode(userEntity.getTenantId());
        if (tenantEntity == null || !Integer.valueOf(Constants.EntityStatus.ACTIVE).equals(tenantEntity.getActive())) {
            return handlerLoginError(event, loginInfo);
        }
        Date validity, notBefore = new Date();
        long now = (new Date()).getTime();
        if (Boolean.TRUE.equals(loginInfo.getRememberMe())) {
            validity = new Date(now + this.applicationProperties.getTokenTime().getRemember() * 1000);
        } else {
            validity = new Date(now + this.applicationProperties.getTokenTime().getNoRemember() * 1000);
        }

        TokenEntity tokenEntity = generateToken(null, null, null, tenantEntity.getName(), userEntity.getEmail(), validity, userEntity.getId(), userEntity.getEmail(), userEntity.getTenantId(),
                String.valueOf(userEntity.getId()), notBefore, null, TOKEN_TYPE_USER, userEntity.getType(), authCode);
        TokenEntity refreshToken = generateRefreshToken(tokenEntity, userEntity.getEmail(), userEntity.getEmail(), userEntity.getTenantId(), String.valueOf(userEntity.getId()), notBefore, authCode);
        CreateTokenDTO response = new CreateTokenDTO();
        response.setAccessToken(tokenEntity.getTokenObject());
        response.setRefreshToken(refreshToken.getTokenObject());
        if (application != null) {
            response.setUrlRedirect(genUrlRedirect(redirectUri, clientId, responseType, authCode, loginInfo.getParams()));
        }
        event.payload = ObjectMapperUtil.toJsonString(response);
//        event.payload = tokenEntity.getTokenObject();
        event.statusCode = ResponseStatusCode.OK.intValue();
        logger.info("Generated token for user: #{}, with Id: #{}, token: #{}", userEntity.getEmail(), userEntity.getId(), event.payload);
        return event;
    }

    private String genUrlRedirect(String redirectUri, String clientId, String responseType, String authCode, Map<String, Object> params) {
        List<String> arrayExcludes = Arrays.asList(new String[]{"client_id", "response_type", "redirect_uri"});
        String uri = String.format("%s?%s=%s&%s=%s", redirectUri, responseType, authCode, "client_id", clientId);
        for (String key : params.keySet()) {
            if (!arrayExcludes.contains(key)) {
                uri += String.format("&%s=%s", key, params.getOrDefault(key, "").toString());
            }
        }
        return uri;
    }

    public CreateTokenDTO createToken(String mainAppId, String domainId, String domainName, String userId, String holderId, String holderName, String tenantId, Integer holderType, Integer subType) {
        logger.debug("create token for holderId #{}, holderName #{}, userId #{}, tenant #{}", holderId, holderName, userId, tenantId);
        UserEntity userEntity = userService.get(userId);
        TenantEntity tenantEntity = tenantRepository.findFirstByCode(tenantId);
        if (tenantEntity == null || !Integer.valueOf(Constants.EntityStatus.ACTIVE).equals(tenantEntity.getActive())) {
            throw new PlatformException("Tenant not found when create token, tenantId " + tenantId, ResponseStatusCode.NOT_FOUND);
        }
        Date validity, notBefore = new Date();
        long now = (new Date()).getTime();
        validity = new Date(now + this.applicationProperties.getTokenTime().getNoRemember() * 1000);
        PermissionEntity permissionEntity = null;
        if (CSEInitialize.isCertificateRelease()) {
            permissionEntity.setRoleIDs(new ArrayList<>());
            permissionEntity.getRoleIDs().add("DefaultRole" +
                    vn.vnpt.oneiot.common.constants.Constants.PREFIX_SEPERATOR + tenantId);
            permissionEntity = permissionRepository.save(permissionEntity);
        } else {
            permissionEntity = new PermissionEntity();
            permissionEntity.setRoleIDs(new ArrayList<>());
            if (holderId.equals(mainAppId)) {
                permissionEntity.getRoleIDs().add(ROLE_MAIN_APP);
            } else if (holderType == TOKEN_TYPE_APP) {
                permissionEntity.getRoleIDs().add(ROLE_USER_APP);
            } else if (holderType == TOKEN_TYPE_DEVICE) {
                permissionEntity.getRoleIDs().add(ROLE_DEVICE);
            }
        }
        clearTokenIfExistBeforeCreateNew(holderId);
        TokenEntity tokenEntity = generateToken(mainAppId, domainId, domainName, tenantEntity.getName(), holderName, validity, userId, userEntity != null ? userEntity.getEmail() : null,
                tenantId, holderId, notBefore, permissionEntity, holderType, subType, null);
        TokenEntity refreshToken = generateRefreshToken(tokenEntity, holderName, userEntity != null ? userEntity.getEmail() : null, tenantId, holderId, notBefore, null);
        /** Provisioned AE-ID authorization**/
        asarService.provisionedAppRuleForAE(tokenEntity.getHolder());
        CreateTokenDTO response = new CreateTokenDTO();
        response.setAccessToken(tokenEntity.getTokenObject());
        response.setRefreshToken(refreshToken.getTokenObject());
        response.setExpiry(tokenEntity.getNotAfter());
        response.setHolderId(holderId);
        response.setType(holderType);
        return response;
    }


    public Map<String, TokenEntity> createTokenBulkForDevice(List<IdentityEntity> devices) {
        UserEntity userEntity = userService.get(devices.get(0).getUserId());
        Date validity, notBefore = new Date();
        long now = (new Date()).getTime();
        validity = new Date(now + this.applicationProperties.getTokenTime().getNoRemember() * 1000);
        Map<String, TokenEntity> tokenEntities = new HashMap<>();
        List<TokenEntity> allToken = new ArrayList<>();
        TenantEntity tenantEntity = tenantRepository.findFirstByCode(devices.get(0).getTenantId());
        for (IdentityEntity d : devices) {
            TokenEntity tokenEntity = generateTokenNoSave(d.getMainAppId(), d.getDomain().getId(), d.getDomain().getName(), tenantEntity.getName(),
                    d.getName(), validity, d.getUserId(), userEntity.getEmail(),
                    d.getTenantId(), d.getId(), notBefore, null, TOKEN_TYPE_DEVICE, d.getSubType(), null);
            TokenEntity refreshToken = generateRefreshTokenNoSave(tokenEntity, d.getName(), d.getUserId(), d.getTenantId(), d.getId(), notBefore, null);
            tokenEntities.put(d.getId(), tokenEntity);
            allToken.add(tokenEntity);
            allToken.add(refreshToken);
            d.setAccessToken(tokenEntity.getTokenObject());
            d.setRefreshToken(refreshToken.getTokenObject());
            d.setExpiry(tokenEntity.getNotAfter());
        }
        if (!tokenEntities.isEmpty()) tokenRepository.saveAll(allToken);
        return tokenEntities;
    }

    private void clearTokenIfExistBeforeCreateNew(String holder) {
        try {
            List<TokenEntity> tokenEntities = tokenRepository.findAllByHolderIs(holder);
            if (tokenEntities != null && !tokenEntities.isEmpty())
                tokenRepository.deleteAll(tokenEntities);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    //<editor-fold desc="validate token va parse token">

    public boolean validateToken(String authToken) {
        try {
            if (tokenRepository.findFirstByTokenObject(authToken) != null) {
                Jwts.parser().setSigningKey(vn.vnpt.oneiot.core.constants.Constants.JWT_SECRET).parseClaimsJws(authToken);

                return true;
            }
            return false;
        } catch (SignatureException e) {
            logger.info("Invalid JWT signature.");
            logger.trace("Invalid JWT signature trace: {}", e);
        } catch (MalformedJwtException e) {
            logger.info("Invalid JWT token.");
            logger.trace("Invalid JWT token trace: {}", e);
        } catch (ExpiredJwtException e) {
            logger.info("Expired JWT token.");
            //log.trace("Expired JWT token trace: {}", e);
        } catch (UnsupportedJwtException e) {
            logger.info("Unsupported JWT token.");
            logger.trace("Unsupported JWT token trace: {}", e);
        } catch (IllegalArgumentException e) {
            logger.info("JWT token compact of handler are invalid.");
            logger.trace("JWT token compact of handler are invalid trace: {}", e);
        }
        return false;
    }

    public TokenEntity parseToken(String token) {
        try {
            Jwts.parser()
                    .setSigningKey(vn.vnpt.oneiot.core.constants.Constants.JWT_SECRET)
                    .parseClaimsJws(token)
                    .getBody();
            return tokenRepository.findFirstByTokenObject(token);
        } catch (Exception e) {
            return null;
        }
    }
    //</editor-fold>

    //<editor-fold desc="Xu ly loi">

    private Event handlerLoginError(Event event, LoginInfo loginInfo) {
        logger.info("Invalid User login: #{}", loginInfo.getEmail());
        return ErrorUtils.handleErrorResponse(ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue(), event, "invalidCredentials");
    }
    //</editor-fold>

    /**
     * receive event
     * event.payload = tokenInfo
     *
     * @param event
     * @return
     */
    public Event getToken(Event event) {
        TokenInfoDTO tokenInfo = ObjectMapperUtil.objectMapper(event.payload, TokenInfoDTO.class);
        String holderId = tokenInfo.holderId;
        TokenEntity tokenEntity = get(holderId);
        event.payload = tokenEntity.getTokenObject();
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    //<editor-fold desc="refresh Token ">
    public TokenEntity generateRefreshToken(TokenEntity accessToken, String subject, String email, String tenantId, String holderId, Date notBefore, String authCode) {
        return create(generateRefreshTokenNoSave(accessToken, subject, email, tenantId, holderId, notBefore, authCode));
    }

    public TokenEntity generateRefreshTokenNoSave(TokenEntity accessToken, String subject, String email, String tenantId, String holderId, Date notBefore, String authCode) {
        // build Token
        long now = (new Date()).getTime();
        Date validity = new Date(now + this.applicationProperties.getTokenTime().getRefreshToken() * 1000);
        TokenEntity tokenEntity = new TokenEntity();
        tokenEntity.setTokenName(subject);
        tokenEntity.setIssuer(vn.vnpt.oneiot.common.constants.Constants.CSE_ID);
        tokenEntity.setHolder(holderId);
        tokenEntity.setNotAfter(validity.getTime());
        tokenEntity.setNotBefore(notBefore.getTime());
        tokenEntity.setExtension(accessToken.getTokenObject());
        tokenEntity.setAudience(vn.vnpt.oneiot.common.constants.Constants.CSE_ID);
        tokenEntity.setTokenId(ShortName.TOKEN + vn.vnpt.oneiot.common.constants.Constants.PREFIX_SEPERATOR + UUID.randomUUID());
        tokenEntity.setResourceID(tokenEntity.getTokenId());
        tokenEntity.setUserName(email);
        tokenEntity.setHolderType(accessToken.getHolderType());
        tokenEntity.setTokenObject(jwtRefreshToken(tokenEntity));
        tokenEntity.setActive(Constants.EntityStatus.IN_ACTIVE);
        tokenEntity.setCreationTime(DateUtils.nowByLong());
        tokenEntity.setLastModifiedTime(tokenEntity.getCreationTime());
        tokenEntity.setAuthCode(authCode);
        return tokenEntity;
    }

    public Event selfRefreshToken(Event event) {
        CreateTokenDTO tokenInfo = ObjectMapperUtil.objectMapper(event.payload, CreateTokenDTO.class);
        logger.debug("self refresh token for #{}", tokenInfo.getHolderId());
        if (Strings.isEmpty(tokenInfo.accessToken))
            return ErrorUtils.handleErrorResponse(ResponseStatusCode.BAD_REQUEST.intValue(), event, MISSING_ACCESS_TOKEN);
        if (Strings.isEmpty(tokenInfo.refreshToken))
            return ErrorUtils.handleErrorResponse(ResponseStatusCode.BAD_REQUEST.intValue(), event, MISSING_REFRESH_TOKEN);
        TokenEntity accessToken = tokenRepository.findFirstByTokenObject(tokenInfo.accessToken);
        TokenEntity refreshToken = tokenRepository.findFirstByTokenObject(tokenInfo.refreshToken);
        if (accessToken == null || refreshToken == null || !accessToken.getHolder().equals(tokenInfo.getHolderId())) {
            return ErrorUtils.handleErrorResponse(ResponseStatusCode.BAD_REQUEST.intValue(), event, ACCESS_TOKEN_INVALID);
        }
        if (StringUtils.isBlank(refreshToken.getExtension()) || !refreshToken.getExtension().equals(tokenInfo.accessToken)) {
            return ErrorUtils.handleErrorResponse(ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue(), event, REFRESH_TOKEN_NOT_CONTAIN_ACCESS_TOKEN);
        }

        // check refresh Token invalid or not
        if (authByPlatformAlgorithm.isValidToken(tokenInfo.refreshToken) != null) {
            event.payload = ObjectMapperUtil.toJsonString(updateToken(accessToken, refreshToken));
            event.statusCode = ResponseStatusCode.OK.intValue();
            return event;
        } else {
            return ErrorUtils.handleErrorResponse(ResponseStatusCode.BAD_REQUEST.intValue(), event, TOKEN_EXPIRED);
        }
    }


    public Event refreshToken(Event event) {
        CreateTokenDTO tokenInfo = ObjectMapperUtil.objectMapper(event.payload, CreateTokenDTO.class);
        if (!tokenInfo.getType().equals(TOKEN_TYPE_DEVICE) && !tokenInfo.getType().equals(TOKEN_TYPE_APP)) {
            throw new PlatformException("Must call SELF_REFRESH_TOKEN", ResponseStatusCode.BAD_REQUEST);
        }
        List<TokenEntity> tokens = tokenRepository.findAllByHolderIs(tokenInfo.getHolderId());
        TokenEntity accessToken = null, refreshToken = null;

        // Check and delete tokens if the creation is wrong
        if (tokens != null) {
            int size = tokens.size();
            if (size > 0 && size != 2)
                // delete all tokens were create wrong
                tokenRepository.deleteAll(tokens);
            else if (size == 2) {
                for (TokenEntity tokenEntity : tokens) {
                    if (tokenEntity.getActive().intValue() == Constants.EntityStatus.ACTIVE) accessToken = tokenEntity;
                    else refreshToken = tokenEntity;
                }
                if (accessToken == null || refreshToken == null) {
                    accessToken = null;
                    refreshToken = null;
                    tokenRepository.deleteAll(tokens);
                }
            }
        }
        IdentityEntity identityEntity = identityRepository.findFirstById(tokenInfo.getHolderId());
        String domainId = null, domainName = null;
        if (accessToken != null) {
            TokenInfoDTO tokenInfoDTO = getTokenInfo(accessToken);
            domainId = tokenInfoDTO.appDomainId;
            domainName = tokenInfoDTO.appDomainName;
        } else {
            IdentityEntity domain = identityRepository.findFirstByMainAppIdAndType(identityEntity.getMainAppId(), APP_DOMAIN_IDENTITY_TYPE);
            domainId = domain.getId();
            domainName = domain.getName();
        }
        CreateTokenDTO createTokenDTO;
        // Recreate tokens if the creation is wrong
        if (accessToken == null) {
            if (identityEntity.getType().equals(DEVICE_IDENTITY_TYPE)) {
                createTokenDTO = createToken(identityEntity.getMainAppId(), domainId, domainName, identityEntity.getUserId(), identityEntity.getId(), identityEntity.getName(),
                        identityEntity.getTenantId(), TOKEN_TYPE_DEVICE, identityEntity.getSubType());
            } else {
                createTokenDTO = createToken(identityEntity.getMainAppId(), domainId, domainName, identityEntity.getUserId(), identityEntity.getId(), identityEntity.getName(),
                        identityEntity.getTenantId(), TOKEN_TYPE_APP, identityEntity.getSubType());
            }
        }
        // update token
        else {
            if (accessToken.getHolderType().intValue() == TOKEN_TYPE_DEVICE && sendRefreshTokenToDevice && identityEntity.getActive().intValue() == Constants.EntityStatus.ACTIVE) {
                createTokenDTO = updateTokenNoSave(accessToken, refreshToken);
                createTokenDTO = updateToken(accessToken, refreshToken);
//                configService.sendTokenPlatformConfig(event, accessToken.getHolder(), accessToken.getTokenName(), buildConfigContent(createTokenDTO), accessToken, refreshToken);
                return null;
            } else {
                createTokenDTO = updateToken(accessToken, refreshToken);
            }
        }
        event.payload = ObjectMapperUtil.toJsonString(createTokenDTO);
        identityEntity.setExpiry(createTokenDTO.getExpiry());
        identityEntity.setUpdated(System.currentTimeMillis());
        identityEntity.setUpdatedBy(SecurityUtils.getCurrentUserLogin());
        identityEntity.setAccessToken(createTokenDTO.getAccessToken());
        identityEntity.setRefreshToken(createTokenDTO.getRefreshToken());
        identityRepository.save(identityEntity);
        event.statusCode = ResponseStatusCode.OK.intValue();
        // delete refreshToken
        return event;
    }

    private CreateTokenDTO updateTokenNoSave(TokenEntity accessToken, TokenEntity refreshToken) {
        logger.debug("update access Token #{} and refresh Token #{}", accessToken.getTokenId(), refreshToken.getTokenId());
        Date validity, notBefore = new Date();
        long now = (new Date()).getTime();
        validity = new Date(now + this.applicationProperties.getTokenTime().getNoRemember() * 1000);
        accessToken = updateAccessToken(accessToken, validity, notBefore);
        refreshToken = updateRefreshToken(refreshToken, accessToken.getTokenObject(), notBefore);
        CreateTokenDTO response = new CreateTokenDTO();
        response.setAccessToken(accessToken.getTokenObject());
        response.setRefreshToken(refreshToken.getTokenObject());
        response.setExpiry(accessToken.getNotAfter());
        response.setHolderId(accessToken.getHolder());
        response.setType(accessToken.getHolderType());
        return response;
    }

    private CreateTokenDTO updateToken(TokenEntity accessToken, TokenEntity refreshToken) {
        CreateTokenDTO response = updateTokenNoSave(accessToken, refreshToken);
        tokenRepository.saveAll(Arrays.asList(new TokenEntity[]{accessToken, refreshToken}));
        return response;
    }
    //</editor-fold>

    //<editor-fold desc="Get Detail Token ">
    public Event getDetailTokenFromStrToken(Event event) {
        try {
            String accessToken = event.payload;
            if (Strings.isEmpty(accessToken)) {
                return ErrorUtils.handleErrorResponse(ResponseStatusCode.BAD_REQUEST.intValue(), event, MISSING_ACCESS_TOKEN);
            }
            TokenInfoDTO tokenInfoDTO = getTokenInfo(authByPlatformAlgorithm.isValidToken(accessToken));
            event.payload = ObjectMapperUtil.toJsonString(tokenInfoDTO);
            event.statusCode = ResponseStatusCode.OK.intValue();
            return event;
        } catch (SignatureException | MalformedJwtException | ExpiredJwtException | UnsupportedJwtException |
                 IllegalArgumentException e) {
            logger.error("Invalid Token", e);
            return ErrorUtils.handleErrorResponse(ResponseStatusCode.BAD_REQUEST.intValue(), event, ErrorKey.AuthErrorKey.INVALID_TOKEN);
        } catch (Exception e) {
            logger.error(UNEXPECTED_ERROR + ", #event = " + event.toString(), e);
            return ErrorUtils.handleErrorResponse(ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue(), event, UNEXPECTED_ERROR);
        }
    }

    public TokenInfoDTO getTokenInfo(TokenEntity tokenEntity) {
        String extension = tokenEntity.getExtension();
        logger.debug("getTokenInfo with extension = #{}", extension);
        JsonObject userInfo = new Gson().fromJson(extension, JsonObject.class);
        String userId = userInfo.get("userId").getAsString();
        String tenantId = userInfo.get("tenantId").getAsString();
        String mainAppId = userInfo.get("mainAppId") == null ? null : userInfo.get("mainAppId").getAsString();
        String appDomainId = userInfo.get("appDomainId") == null ? null : userInfo.get("appDomainId").getAsString();
        String appDomainName = userInfo.get("appDomainName") == null ? null : userInfo.get("appDomainName").getAsString();
        String tenantName = userInfo.get("tenantName") == null ? null : userInfo.get("tenantName").getAsString();
        Integer subType = userInfo.get("subType") == null ? null : userInfo.get("subType").getAsInt();
        TokenInfoDTO tokenInfoDTO = new TokenInfoDTO();
        tokenInfoDTO.userId = userId;
        tokenInfoDTO.tenantId = tenantId;
        tokenInfoDTO.holderId = tokenEntity.getHolder();
        tokenInfoDTO.mainAppId = mainAppId;
        tokenInfoDTO.appDomainName = appDomainName;
        tokenInfoDTO.appDomainId = appDomainId;
        tokenInfoDTO.tenantName = tenantName;
        tokenInfoDTO.username = tokenEntity.getUserName();
        tokenInfoDTO.holderName = tokenEntity.getTokenName();
        tokenInfoDTO.audience = tokenEntity.getAudience();
        tokenInfoDTO.expireTime = tokenEntity.getExpirationTime();
        tokenInfoDTO.issuer = tokenEntity.getIssuer();
        tokenInfoDTO.notBefore = tokenEntity.getNotBefore();
        tokenInfoDTO.subject = tokenEntity.getTokenName();
        tokenInfoDTO.orgType = tokenEntity.getHolderType();
        tokenInfoDTO.orgSubType = subType;
        return tokenInfoDTO;
    }
    //</editor-fold>

    private String buildConfigContent(CreateTokenDTO createTokenDTO) {
        JsonObject contentJo = new JsonObject();
        contentJo.addProperty("contentInfo", "text/plains:0");
        contentJo.addProperty("content", new Gson().toJson(createTokenDTO));
        return contentJo.toString();
    }

    public void refreshTokenResp(Event event) {
        logger.debug("Handle response for method REFRESH_TOKEN_DEVICE");
        ResponsePrimitive responsePrimitive = new Gson().fromJson(event.payload, ResponsePrimitive.class);
        String transactionKey = getTransactionKey(responsePrimitive.getRequestIdentifier());
        logger.info("transactionKey resp---" + transactionKey);
        Transaction transaction = (Transaction) systemCache.get(transactionKey, Transaction.class);
        if (transaction != null && transaction.getState() == TransactionState.BEGIN) {
            if (responsePrimitive.getResponseStatusCode().intValue() == ResponseStatusCode.OK.intValue()) {
                //resp to http
                ContentRequestPrimitive contentResptoHttp = new Gson().fromJson(transaction.getRequestPrimitive().getContent(), ContentRequestPrimitive.class);
                CreateTokenDTO createTokenDTO = new Gson().fromJson(contentResptoHttp.getContent(), CreateTokenDTO.class);
                Event eventRespHTTP = new Event();
                eventRespHTTP.id = transaction.getEventId();
                eventRespHTTP.type = EVENTTYPE_RESPONSE;
                eventRespHTTP.method = REFRESH_TOKEN;
                eventRespHTTP.statusCode = ResponseStatusCode.OK.intValue();
                eventRespHTTP.payload = new Gson().toJson(createTokenDTO);
                eventBus.publish(getExchangeFromRoutingKey(transaction.getReplyToRoutingKey()), transaction.getReplyToRoutingKey(), eventRespHTTP);

                Claims accessTokenCl = Jwts.parser()
                        .setSigningKey(vn.vnpt.oneiot.core.constants.Constants.JWT_SECRET)
                        .parseClaimsJws(createTokenDTO.accessToken)
                        .getBody();
                Claims refreshTokenCl = Jwts.parser()
                        .setSigningKey(vn.vnpt.oneiot.core.constants.Constants.JWT_SECRET)
                        .parseClaimsJws(createTokenDTO.refreshToken)
                        .getBody();
                List<TokenEntity> tokenEntities = tokenRepository.findAllByHolderIs(createTokenDTO.holderId);
                TokenEntity accessToken = null, refreshToken = null;
                for (TokenEntity tokenEntity : tokenEntities) {
                    if (tokenEntity.getActive().intValue() == Constants.EntityStatus.ACTIVE) accessToken = tokenEntity;
                    else refreshToken = tokenEntity;
                }
                if (accessToken != null) {
                    accessToken.setNotAfter(accessTokenCl.getExpiration().getTime());
                    accessToken.setNotBefore(System.currentTimeMillis());
                    accessToken.setTokenId(accessTokenCl.getId());
                    accessToken.setTokenObject(jwtAccessToken(accessToken));
                    refreshToken.setNotAfter(refreshTokenCl.getExpiration().getTime());
                    refreshToken.setNotBefore(System.currentTimeMillis());
                    refreshToken.setExtension(createTokenDTO.accessToken);
                    refreshToken.setTokenId(refreshTokenCl.getId());
                    refreshToken.setTokenObject(jwtRefreshToken(refreshToken));
                    tokenRepository.saveAll(Arrays.asList(accessToken, refreshToken));
                }
            }
        }
    }

    public static String getTransactionKey(String id) {
        if (!org.springframework.util.StringUtils.isEmpty(id))
            return ServiceKey + id;
        else return null;
    }

    public void deleteTokenByUsers(List<UserEntity> userEntities) {
        try {
            if (userEntities != null && !userEntities.isEmpty()) {
                for (UserEntity userEntity : userEntities) {
                    List<TokenEntity> tokenEntities = tokenRepository.findAllByHolderIsAndActiveIsAndUserNameIs(userEntity.getId(), Constants.EntityStatus.ACTIVE, userEntity.getEmail());
                    if (tokenEntities != null && !tokenEntities.isEmpty()) {
                        deleteAllByList(tokenEntities);
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    public void clearTokenExpired() {
        try {
            Calendar ca = Calendar.getInstance();
            ca.set(Calendar.HOUR_OF_DAY, 0);
            ca.set(Calendar.MINUTE, 0);
            ca.set(Calendar.SECOND, 0);
            ca.set(Calendar.MILLISECOND, 0);
            long timeCheckExpired = ca.getTimeInMillis();
            Query query = new BasicQuery(QueryBuilder.start("tkna").lessThan(timeCheckExpired).and("holderType").is(TOKEN_TYPE_USER).get().toString());
            DeleteResult deleteResult = mongoTemplate.getCollection(mongoTemplate.getCollectionName(TokenEntity.class)).deleteMany(query.getQueryObject());
            logger.info("Delete {} expired user token", deleteResult.getDeletedCount());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }


    /**
     * Kiểm tra token hợp lệ
     * Tối ưu bằng cách sử dụng cache và early return pattern
     */
    public Event checkToken(Event event) {
        Event resp = event.createResponse();
        try {
            // Parse payload một lần duy nhất
            JsonObject dataToken = Util.getInstanceGsonCustom().fromJson(event.payload, JsonObject.class);

            // Validate input
            String holderId = dataToken.has("username") ? dataToken.get("username").getAsString() : null;
            String accessToken = dataToken.has("password") ? dataToken.get("password").getAsString() : null;

            if (com.google.common.base.Strings.isNullOrEmpty(holderId) || com.google.common.base.Strings.isNullOrEmpty(accessToken)) {
                return createErrorResponse(resp, ResponseStatusCode.BAD_REQUEST.intValue(),
                        "username (holderId) and password (accessToken) must be not null");
            }
            // Kiểm tra cặp id và token có trong cache chưa
            String CheckTokenCacheKey = CHECK_TOKEN_CACHE_PREFIX + holderId + "-" + accessToken;
            String CheckTokenCache = (String) systemCache.get(CheckTokenCacheKey, String.class);

            if (CheckTokenCache != null) {
                resp.statusCode = ResponseStatusCode.OK.intValue();
                return resp;
            }
            // Kiểm tra identity từ cache trước
            String identityCacheKey = IDENTITY_CACHE_PREFIX + holderId;
            IdentityEntity entity = (IdentityEntity) systemCache.get(identityCacheKey, IdentityEntity.class);

            // Nếu không có trong cache, truy vấn từ database
            if (entity == null) {
                entity = identityRepository.findFirstById(holderId);
                if (entity != null) {
                    systemCache.putMilisecond(identityCacheKey, entity, CACHE_TTL, IdentityEntity.class);
                }
            }

            if (entity == null) {
                return createErrorResponse(resp, ResponseStatusCode.NOT_FOUND.intValue(),
                        "Entity (holderId) is not existed");
            }

            if (entity.getActive() == Constants.EntityStatus.IN_ACTIVE || entity.getActive() == Constants.EntityStatus.DELETED) {
                return createErrorResponse(resp, ResponseStatusCode.OPERATION_NOT_ALLOWED.intValue(),
                        "Entity is not ACTIVE");
            }

            // Tạo composite key cho token để tăng hiệu suất cache
            String tokenCacheKey = TOKEN_CACHE_PREFIX + accessToken + ":" + holderId;
            TokenEntity tokenEntity = (TokenEntity) systemCache.get(tokenCacheKey, TokenEntity.class);

            // Nếu không có trong cache, truy vấn từ database
            if (tokenEntity == null) {
                tokenEntity = tokenRepository.findFirstByTokenObjectIsAndHolderIsAndActiveIs(accessToken, holderId, Constants.EntityStatus.ACTIVE);
                if (tokenEntity != null) {
                    systemCache.putMilisecond(tokenCacheKey, tokenEntity, CACHE_TTL, TokenEntity.class);
                }
            }

            if (tokenEntity == null) {
                return createErrorResponse(resp, ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue(),
                        "password (accessToken) is not correct");
            }

            // Kiểm tra tenant từ cache trước
            String tenantCacheKey = TENANT_CACHE_PREFIX + entity.getTenantId();
            TenantEntity tenantEntity = (TenantEntity) systemCache.get(tenantCacheKey, TenantEntity.class);

            // Nếu không có trong cache, truy vấn từ database
            if (tenantEntity == null) {
                tenantEntity = tenantRepository.findFirstByCode(entity.getTenantId());
                if (tenantEntity != null) {
                    systemCache.putMilisecond(tenantCacheKey, tenantEntity, CACHE_TTL, TenantEntity.class);
                }
            }

            if (tenantEntity == null) {
                return createErrorResponse(resp, ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue(),
                        "tenant was deleted");
            }

            if (tenantEntity.getActive() != Constants.EntityStatus.ACTIVE) {
                return createErrorResponse(resp, ResponseStatusCode.OPERATION_NOT_ALLOWED.intValue(),
                        "Tenant is not ACTIVE");
            }

            systemCache.putMilisecond(CheckTokenCacheKey, accessToken, CACHE_TTL, String.class);

            resp.statusCode = ResponseStatusCode.OK.intValue();
            return resp;

        } catch (Exception e) {
            logger.error("Error checking token: {}", e.getMessage(), e);
            return createErrorResponse(resp, ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue(), e.getMessage());
        }
    }

    /**
     * Helper method để tạo response lỗi
     */
    private Event createErrorResponse(Event resp, int statusCode, String errorText) {
        resp.statusCode = statusCode;
        resp.errorText = errorText;
        return resp;
    }

    public Event validateToken(Event event) {
        Event eventRsp = event.createResponse();
        try {
            eventRsp.statusCode = ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue();
            JsonObject dataToken = Util.getInstanceGsonCustom().fromJson(event.payload, JsonObject.class);
            String token = dataToken.get("token").getAsString();
            String tenantId = dataToken.get("tenantId").getAsString();
            JsonObject dataCombo = Util.getInstanceGsonCustom().fromJson(new String(Base64.getDecoder().decode(token)), JsonObject.class);
            String clientId = dataCombo.get("clientId").getAsString();
            String secretId = dataCombo.get("secretId").getAsString();
            Application application = applicationRepository.findFirstByClientIdAndSecretId(clientId, secretId);
            if (application != null) {
                eventRsp.statusCode = ResponseStatusCode.OK.intValue();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            eventRsp.statusCode = ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue();
        }
        return eventRsp;
    }

    public Event genAuthCode(Event event) {
        String clientId = null;
        String responseType = null;
        String redirectUri = null;
        Map<String, Object> data = Util.getInstanceGsonCustom().fromJson(event.payload, new TypeToken<Map<String, Object>>() {
        }.getType());
        Map<String, Object> params = (Map<String, Object>) data.getOrDefault("params", new HashMap<>());
        String tokenObject = String.valueOf(data.getOrDefault("tokenObject", ""));
        if (params != null) {
            clientId = params.getOrDefault("client_id", "").toString();
            responseType = params.getOrDefault("response_type", "").toString();
            redirectUri = params.getOrDefault("redirect_uri", "").toString();
        }
        Application application = null;
        String authCode = "";
        if (clientId == null || responseType == null || redirectUri == null) {
            event.statusCode = ResponseStatusCode.NOT_FOUND.intValue();
            return event;
        }
        application = applicationRepository.findById(clientId).orElse(null);
        if (application != null) {
            authCode = String.format("%s.%s", UUID.randomUUID().toString(), Base64.getEncoder().encode("name=ONEIOT_PLATFORM".getBytes(StandardCharsets.UTF_8)));
        } else {
            event.statusCode = ResponseStatusCode.NOT_FOUND.intValue();
            return event;
        }
        TokenEntity tokenEntity = tokenRepository.findFirstByTokenObject(tokenObject);
        tokenEntity.setAuthCode(authCode);
        tokenRepository.save(tokenEntity);
        TokenEntity refreshTokenEntity = tokenRepository.findFirstByExtensionIs(tokenObject);
        refreshTokenEntity.setAuthCode(authCode);
        tokenRepository.save(refreshTokenEntity);
        CreateTokenDTO response = new CreateTokenDTO();
        if (application != null) {
            response.setUrlRedirect(genUrlRedirect(redirectUri, clientId, responseType, authCode, params));
        }
        event.payload = ObjectMapperUtil.toJsonString(response);
//        event.payload = tokenEntity.getTokenObject();
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    public Event createTokenAuth2(Event event) {
        try {
            Auth2Request auth2Request = Util.getInstanceGsonCustom().fromJson(event.payload, Auth2Request.class);
            if (auth2Request.getGrantType().equals("authorization_code")
                    && Strings.isNotEmpty(auth2Request.getCode()) && Strings.isNotEmpty(auth2Request.getClientId()) && Strings.isNotEmpty(auth2Request.getClientSecret())) {
                event.statusCode = ResponseStatusCode.NOT_FOUND.intValue();
                Application application = applicationRepository.findFirstByClientIdAndSecretId(auth2Request.getClientId(), auth2Request.getClientSecret());
                if (application != null) {
                    TokenEntity tokenEntity = tokenRepository.findFirstByAuthCodeStartingWithAndActive(String.format("%s.", auth2Request.getCode()), Constants.EntityStatus.ACTIVE);
                    if (tokenEntity != null && (tokenEntity.getNotAfter() - System.currentTimeMillis()) > 0 && (System.currentTimeMillis() - tokenEntity.getNotBefore()) > 0) {
                        UserEntity userEntity = userRepository.findFirstById(tokenEntity.getHolder());
                        if (userEntity.getType().equals(UserType.TENANT_ADMIN)) {
                            Auth2Response auth2Response = new Auth2Response();
                            auth2Response.setAccessToken(tokenEntity.getTokenObject());
                            auth2Response.setTokenType("Bearer");
                            auth2Response.setScope("openid");
                            auth2Response.setExpiresIn((tokenEntity.getNotAfter() - System.currentTimeMillis()) / 1000);
                            TokenEntity tokenEntityRefresh = tokenRepository.findFirstByExtensionIs(tokenEntity.getTokenObject());
                            auth2Response.setRefreshToken(tokenEntityRefresh.getTokenObject());
                            UserAuth2Info userAuth2Info = new UserAuth2Info();
                            userAuth2Info.setTenantId(userEntity.getTenantId());
                            userAuth2Info.setSub(userEntity.getTenantId());
                            userAuth2Info.setName(userEntity.getFullName());
                            userAuth2Info.setEmail(userEntity.getEmail());
                            userAuth2Info.setNbf(tokenEntity.getNotBefore());
                            userAuth2Info.setExp((tokenEntity.getNotAfter() - System.currentTimeMillis()) / 1000);
                            userAuth2Info.setMobile(userEntity.getPhone());
                            userAuth2Info.setTechID(userEntity.getTenantId());
                            String prefix = "a";
                            String suffix = "b";
                            String idToken = Jwts.builder()
                                    .setSubject(tokenEntity.getTokenName())
                                    .setId(tokenEntity.getTokenId())
                                    .setExpiration(new Date(tokenEntity.getNotAfter()))
                                    .setNotBefore(new Date(tokenEntity.getNotBefore()))
                                    .setIssuer(tokenEntity.getIssuer())
                                    .setAudience(tokenEntity.getAudience())
                                    .setClaims(userAuth2Info.toMap())
                                    .signWith(SignatureAlgorithm.HS256, JWT_SECRET)
                                    .compact();
                            auth2Response.setIdToken(idToken);
//                        auth2Response.setIdToken(String.format("%s.%s.%s", prefix, Base64.getEncoder().encodeToString(Util.getInstanceGsonCustom().toJson(userAuth2Info).getBytes(StandardCharsets.UTF_8)), suffix));
                            event.statusCode = ResponseStatusCode.OK.intValue();
                            event.payload = ObjectMapperUtil.toJsonString(auth2Response);
                            return event;
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            event.statusCode = ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue();
            return event;
        }
        event.statusCode = ResponseStatusCode.BAD_REQUEST.intValue();
        return event;
    }

    public Event validateTokenAuth2(Event event) {
        event.statusCode = ResponseStatusCode.OPERATION_NOT_ALLOWED.intValue();
        return event;
    }
}
