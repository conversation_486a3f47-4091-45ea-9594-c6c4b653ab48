package vn.vnpt.oneiot.core.mongo.entity;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(value = "CATEGORIES")
public class Category {
    private String id;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    private String name;

    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }

    private String parentId;

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }
}
