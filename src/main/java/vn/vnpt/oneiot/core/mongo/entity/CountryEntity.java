package vn.vnpt.oneiot.core.mongo.entity;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import vn.vnpt.oneiot.common.entities.ResourceEntity;

/**
 * Created by hoangpd
 * Date: 04/11/2023
 * Time: 3:30 PM
 * for all issues, contact me: <EMAIL>
 **/
@Document(collection = "BASE_COUNTRIES")
public class CountryEntity extends MgAbstractEntity {
    private String shortName;
    private String name;

    public String getShortName() {
        return this.shortName;
    }

    public String getName() {
        return this.name;
    }

    public CountryEntity name(String name) {
        this.setName(name);
        return this;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "CountryEntity{" +
                ", shortName='" + getShortName() + "'" +
                ", name='" + getName() + "'" +
                "}";
    }
}
