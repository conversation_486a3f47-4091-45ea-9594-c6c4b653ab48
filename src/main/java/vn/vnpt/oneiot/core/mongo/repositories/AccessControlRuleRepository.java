package vn.vnpt.oneiot.core.mongo.repositories;

import vn.vnpt.oneiot.common.entities.AccessControlRuleEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCustomRepository;

/**
 * Created by huyvv
 * Date: 09/03/2020
 * Time: 5:49 PM
 * for all issues, contact me: <EMAIL>
 **/
public interface AccessControlRuleRepository extends MgCustomRepository<AccessControlRuleEntity, String> {
    AccessControlRuleEntity findFirstByAccessControlRuleId(String id);
}
