package vn.vnpt.oneiot.core.mongo.repositories;


import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import vn.vnpt.oneiot.core.mongo.entity.UserEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCustomRepository;

import java.util.List;
import java.util.Set;

/**
 * Created by huyvv
 * Date: 16/01/2020
 * Time: 11:00 AM
 * for all issues, contact me: <EMAIL>
 **/


@Repository
public interface UserRepository extends MgCustomRepository<UserEntity, String> {
    UserEntity findOneByEmailIgnoreCase(String email);
    Page<UserEntity> findAllByIdIn(Set<Long> ids, Pageable pageable);

//    @EntityGraph(attributePaths = {"roleEntities","organizations"})
//    UserEntity findOneWithRolesAndOrganizationsById(Long id);
//
//    @EntityGraph(attributePaths = "roleEntities")
//    UserEntity findOneWithRoleEntitiesByEmail(String email);

    List<UserEntity> findAllByIdIn(Set<Long> ids);

    UserEntity findFirstByJwtToken(String token);

//    @Modifying
//    @Query(value = "UPDATE base_users SET jwt_token = NULL where id = ?1", nativeQuery = true)
//    void clearJwtToken(Long id);

    UserEntity findFirstById(String id);

    List<UserEntity> findByTenantId(String tenantId);
    List<UserEntity> findByRoleIds(String roleId);

    UserEntity findFirstByEmail(String email);
    List<UserEntity> findAllByIdInAndActive(Set<Long> ids, int active);
    List<UserEntity> findAllByActiveInAndUpdatedLessThan(int[] active, long time);
    List<UserEntity> findAllByActiveInAndCreatedLessThan(int[] active, long time);
    void  deleteUsersByIdIn(Set<Long> ids);

    List<UserEntity> findAllByTenantId(String tenantId);
    List<UserEntity> findAllByTenantIdIn(Set<Long> tenantIds);
    void  deleteAllByTenantIdIn(Set<String> ids);
    Integer countAllByTenantId(String tenantId);
}
