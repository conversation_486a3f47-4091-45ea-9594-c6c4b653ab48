package vn.vnpt.oneiot.core.mongo.generic;

import cz.jirutka.rsql.parser.RSQLParser;
import cz.jirutka.rsql.parser.RSQLParserException;
import cz.jirutka.rsql.parser.ast.Node;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;
import vn.vnpt.oneiot.base.constants.Constants;
import vn.vnpt.oneiot.base.errors.ErrorKey;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.rsql.CustomRsqlVisitor;
import vn.vnpt.oneiot.base.utils.DateUtils;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.common.entities.ResourceEntity;
import vn.vnpt.oneiot.common.exceptions.BadRequestException;
import vn.vnpt.oneiot.common.utils.Patterns;
import vn.vnpt.oneiot.core.api.nbi.models.*;
import vn.vnpt.oneiot.core.mongo.entity.UserEntity;

import javax.persistence.EntityNotFoundException;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import java.util.function.Function;

import static vn.vnpt.oneiot.common.utils.Patterns.ID_STRING;

/**
 * Created by huyvv
 * Date: 06/03/2020
 * Time: 11:13 PM
 * for all issues, contact me: <EMAIL>
 **/

@Transactional
@SuppressWarnings({"Duplicates", "unchecked"})
public abstract class MgCrudService<T extends ResourceEntity, ID> extends MgNoneCrudService<T, ID>{
    private static Logger logger = LoggerFactory.getLogger(MgCrudService.class);

    public MgCrudService(Class<T> typeEntityClass) {
        super(typeEntityClass);
    }

    public Event process(Event event){
        switch (event.method){
            case Constants.Method.CREATE:
                return processCreate(event);
            case Constants.Method.UPDATE:
                return processUpdate(event);
            case Constants.Method.DELETE:
                return processDelete(event);
            case Constants.Method.BATCH_DELETE:
                return processBatchDelete(event);
            case Constants.Method.ACTIVE:
                return processActive(event);
            case Constants.Method.DE_ACTIVE:
                return processDeActive(event);
            case Constants.Method.GET_ONE:
                return processGetOne(event);
            case Constants.Method.SEARCH:
                return processSearch(event);
            default:
                event.statusCode = ResponseStatusCode.NOT_IMPLEMENTED.intValue();
                return event;
        }
    }
    protected Event processCreate(Event event){
        T entity = ObjectMapperUtil.objectMapper(event.payload, typeEntityClass);
        event.payload = ObjectMapperUtil.toJsonString(create(entity));
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processUpdate(Event event){
        T entity = ObjectMapperUtil.objectMapper(event.payload, typeEntityClass);
        event.payload = ObjectMapperUtil.toJsonString(update((ID) entity.getResourceID(), entity));
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processDelete(Event event){
        StringIdInfo idInfo= ObjectMapperUtil.objectMapper(event.payload, StringIdInfo.class);
        softDelete((ID) idInfo.getId());
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processBatchDelete(Event event) {

        List<String> ids = ObjectMapperUtil.listMapper(event.payload, String.class);
        List<String> fail = new ArrayList<>();
        for(String id : ids){
            try{
                softDelete((ID) id);
            }catch (Exception ex){
                logger.error(ex.getMessage(), ex);
                fail.add(id);
            }
        }
        event.payload = ObjectMapperUtil.toJsonString(fail);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processActive(Event event){
        IdInfo idInfo = ObjectMapperUtil.objectMapper(event.payload, IdInfo.class);
        activate((ID) idInfo.getId());
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processDeActive(Event event){
        IdInfo idInfo = ObjectMapperUtil.objectMapper(event.payload, IdInfo.class);
        deactivate((ID) idInfo.getId());
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processGetOne(Event event){
        StringIdInfo stringIdInfo = ObjectMapperUtil.objectMapper(event.payload, StringIdInfo.class);
        event.payload = ObjectMapperUtil.toJsonString(get((ID) stringIdInfo.getId()));
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processSearch(Event event){
        SearchInfo searchInfo = ObjectMapperUtil.objectMapper(event.payload, SearchInfo.class);
        String orders = searchInfo.getOrders();
        Pageable pageable;
        if(orders == null || "".equals(orders)){
            pageable = PageRequest.of(searchInfo.getPageNumber(), searchInfo.getPageSize());
        }else {
            pageable = PageRequest.of
                    (searchInfo.getPageNumber(), searchInfo.getPageSize(), vn.vnpt.oneiot.base.utils.StringUtils.toSort(orders));
        }
        Page<T> page = search(searchInfo.getQuery(), pageable);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(page.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(page.getContent()));

        event.payload = ObjectMapperUtil.toJsonString(pageInfo);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }


//    @Override
//    public Page<T> search(String query, Pageable pageable) {
////        query=addMultipleTenantQuery(query);
//        return super.search(query,pageable);
//    }


    public void activate(ID id) {
        T t = get(id);
        if(t != null) {
            t.setActive(Constants.EntityStatus.ACTIVE);
            update(id, t);
        }
    }


    @Override
    public void softDelete(ID id){
    }
    public void deactivate(ID id) {
        T t = get(id);
        if(t != null) {
            t.setActive(Constants.EntityStatus.IN_ACTIVE);
            update(id, t);
        }
    }


    /**
     * functions before, after
     **/
    @Override
    protected void beforeCreate(T entity) {
        if(entity.getResourceID() == null){
            String generatedId = UUID.randomUUID().toString();
            if(entity.getResourceType() != null){
                entity.setResourceID(Patterns.getResourcePrefix(entity.getResourceType().intValue()) + generatedId);
                if (entity.getName() != null){
                    if (!Patterns.matchResource(Patterns.ID_PATTERN, entity.getName())){
                        throw new BadRequestException("Name provided is incorrect. Must be:" + ID_STRING);
                    }
                } else {
                    entity.setName(Patterns.getResourceName(entity.getResourceType().intValue()) +
                            vn.vnpt.oneiot.common.constants.Constants.PREFIX_SEPERATOR + generatedId);
                }
            }else {
                entity.setResourceID(generatedId);
            }

            entity.setCreationTime(DateUtils.nowByLong());
            entity.setLastModifiedTime(entity.getCreationTime());
        } else {
            T resource = super.get((ID) entity.getResourceID());
            if (resource != null) {
                entity.setLastModifiedTime(DateUtils.nowByLong());
            } else {
                if (entity.getName() != null){
                    if (!Patterns.matchResource(Patterns.ID_PATTERN, entity.getName())){
                        throw new BadRequestException("Name provided is incorrect. Must be:" + ID_STRING);
                    }
                } else {
                    String[] strArr = entity.getResourceID().split("/");
                    entity.setName(strArr[strArr.length-1]);
                }
                entity.setCreationTime(DateUtils.nowByLong());
                entity.setLastModifiedTime(entity.getCreationTime());
            }
        }
        if(entity.getActive() == null) {
            entity.setActive(Constants.EntityStatus.ACTIVE);
        }
    }

    @Override
    protected void beforeUpdate(T entity) {
        entity.setLastModifiedTime(DateUtils.nowByLong());
        if(entity.getActive() == null) {
            entity.setActive(Constants.EntityStatus.ACTIVE);
        }
    }

    public T update(T entity){
        beforeUpdate(entity);
        T old = get((ID) entity.getResourceID());
        if(old == null) {
            throw new EntityNotFoundException("No entity with id " + entity.getResourceID());
        }
        entity = repository.save(entity);
        afterUpdate(old,entity);
        return entity;
    }

}
