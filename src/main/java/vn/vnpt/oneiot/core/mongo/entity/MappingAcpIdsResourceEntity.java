package vn.vnpt.oneiot.core.mongo.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.ArrayList;
import java.util.List;

@Document(collection = "MAP_ACPID_RESOURCE")
public class MappingAcpIdsResourceEntity {
    @Id
    @Field("ri")
    String resourceId;
    @Field("accessControlPolicyIds")
    List<String> accessControlPolicyIds;

    @Field("huri")
    String huri;

    public List<String> getAccessControlPolicyIds() {
        if (accessControlPolicyIds == null) {
            accessControlPolicyIds = new ArrayList<>();
        }
        return accessControlPolicyIds;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setAccessControlPolicyIds(List<String> accessControlPolicyIds) {
        this.accessControlPolicyIds = accessControlPolicyIds;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getHuri() {
        return huri;
    }

    public void setHuri(String huri) {
        this.huri = huri;
    }
}
