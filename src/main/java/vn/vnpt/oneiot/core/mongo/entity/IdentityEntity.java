package vn.vnpt.oneiot.core.mongo.entity;

import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

@Document(collection = "IDENTITY")
public class IdentityEntity extends MgAbstractEntity {
    /** Trường active trong AbstractEntity để update trạng thái của Entity
        IN_ACTIVE = 0, ACTIVE = 1, REGISTER = 2, DELETED = 3;
    */
    @Field
    private String name;
    @Field
    private String userId; // email của user tạo
    @Field
    private String tenantId; // tenantCode của ứng dụng
    @Field
    private String mainAppId; // ID của main app của ứng dụng tạo
    // Type of Entity: Application(1), Device(2), Group (3), Application Domain (4)

    @Field
    private Integer type;

    // Subtype if Entity: GeneralDevice (1), Gateway (2), SubDevice (3), MainApp(0), UserApp(1)
    @Field
    private Integer subType;
    @Field
    private String description;
    @Field
    private String category; // category cho device

    @Field
    private Integer authorityType; //authority  type for application domain REGISTERED(1) , NON-REGISTRERED(0)

    @Transient
    private String accessToken;

    @Transient
    private String refreshToken;

    private Long expiry;

    private Long activationTime;

    private transient IdentityEntity domain;
    public IdentityEntity getDomain() {
        return domain;
    }

    public void setDomain(IdentityEntity domain) {
        this.domain = domain;
    }

    public Long getActivationTime() {
        return activationTime;
    }

    public void setActivationTime(Long activationTime) {
        this.activationTime = activationTime;
    }

    public Long getExpiry() {
        return expiry;
    }

    public void setExpiry(Long expiry) {
        this.expiry = expiry;
    }

    private List<String> memberIds;

    public List<String> getMemberIds() {
        return memberIds;
    }

    public void setMemberIds(List<String> memberIds) {
        this.memberIds = memberIds;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getMainAppId() {
        return mainAppId;
    }

    public void setMainAppId(String mainAppId) {
        this.mainAppId = mainAppId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getSubType() {
        return subType;
    }

    public void setSubType(Integer subType) {
        this.subType = subType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public Integer getAuthorityType() {
        return authorityType;
    }

    public void setAuthorityType(Integer authorityType) {
        this.authorityType = authorityType;
    }

    public IdentityEntity id(String id) {
        this.setId(id);
        return this;
    }


    public IdentityEntity name(String name) {
        this.name = name;
        return this;
    }

    public IdentityEntity userId(String userId) {
        this.userId = userId;
        return this;
    }

    public IdentityEntity tenantId(String tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public IdentityEntity mainAppId(String mainAppId) {
        this.mainAppId = mainAppId;
        return this;
    }

    public IdentityEntity type(Integer type) {
        this.type = type;
        return this;
    }

    public IdentityEntity subType(Integer subType) {
        this.subType = subType;
        return this;
    }

    public IdentityEntity description(String description) {
        this.description = description;
        return this;
    }

    public IdentityEntity category(String category) {
        this.category = category;
        return this;
    }

    public IdentityEntity authorityType(Integer authorityType) {
        this.authorityType = authorityType;
        return this;
    }

    public IdentityEntity accessToken(String accessToken) {
        this.accessToken = accessToken;
        return this;
    }

    public IdentityEntity refreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
        return this;
    }

}
