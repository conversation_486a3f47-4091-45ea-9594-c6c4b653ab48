package vn.vnpt.oneiot.core.mongo.services;

import com.google.gson.Gson;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.base.constants.Constants;
import vn.vnpt.oneiot.base.errors.EmailNotFoundException;
import vn.vnpt.oneiot.base.errors.ErrorKey;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.base.utils.StringUtils;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.common.entities.RoleEntity;
import vn.vnpt.oneiot.common.entities.TokenEntity;
import vn.vnpt.oneiot.core.api.nbi.models.ErrorInfo;
import vn.vnpt.oneiot.core.api.nbi.models.ForgotPasswordInfo;
import vn.vnpt.oneiot.core.api.nbi.models.ResetPasswordInfo;
import vn.vnpt.oneiot.core.api.nbi.models.TokenInfoDTO;
import vn.vnpt.oneiot.core.configuration.ApplicationProperties;
import vn.vnpt.oneiot.core.generic.services.MailService;
import vn.vnpt.oneiot.core.mongo.entity.PrivilegeEntity;
import vn.vnpt.oneiot.core.mongo.entity.TenantEntity;
import vn.vnpt.oneiot.core.mongo.entity.UserEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCrudAdminService;
import vn.vnpt.oneiot.core.mongo.repositories.*;
import vn.vnpt.oneiot.core.utils.Common;
import vn.vnpt.oneiot.core.utils.ErrorUtils;
import vn.vnpt.oneiot.core.utils.SecurityUtils;

//import javax.persistence.EntityManager;
import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

import static vn.vnpt.oneiot.core.constants.Constants.AUTHEN_ENDPOINT_ERROR_KEY.TOKEN_EXPIRED;
import static vn.vnpt.oneiot.core.constants.Constants.DEFAULT_ROLE_USER;
import static vn.vnpt.oneiot.core.generic.Constants.IDENTITY_TYPE.DEVICE_IDENTITY_TYPE;
import static vn.vnpt.oneiot.core.generic.Constants.Method.*;

/**
 * Created by huyvv
 * Date: 16/01/2020
 * Time: 11:00 AM
 * for all issues, contact me: <EMAIL>
 **/
@Service
@Transactional
@EnableConfigurationProperties(ApplicationProperties.class)
public class UserService extends MgCrudAdminService<UserEntity, String> {

    private static Logger logger = LoggerFactory.getLogger(UserService.class);
    private UserRepository userRepository;
    private TenantRepository tenantRepository;

    private DynamicRoleRepository roleRepository;
    private TokenService tokenService;
    private final ApplicationProperties applicationProperties;
    private MailService mailService;
    private TenantService tenantService;
    private DynamicRoleService dynamicRoleService;
    private PrivilegeService privilegeService;

//    @PersistenceContext
//    private EntityManager entityManager;

    @Autowired
    public void setPrivilegeService(PrivilegeService privilegeService) {
        this.privilegeService = privilegeService;
    }

    @Autowired
    public void setDynamicRoleService(DynamicRoleService dynamicRoleService) {
        this.dynamicRoleService = dynamicRoleService;
    }

    @Autowired
    public void setTenantService(TenantService tenantService) {
        this.tenantService = tenantService;
    }

    @Autowired
    public void setTenantRepository(TenantRepository tenantRepository) {
        this.tenantRepository = tenantRepository;
    }

    @Autowired
    public void setTokenService(TokenService tokenService) {
        this.tokenService = tokenService;
    }

    @Autowired
    public void setMailService(MailService mailService) {
        this.mailService = mailService;
    }

    @Autowired
    public UserService(UserRepository userRepository, ApplicationProperties applicationProperties,DynamicRoleRepository roleRepository) {
        super(UserEntity.class);
        this.repository = this.userRepository = userRepository;
        this.applicationProperties = applicationProperties;
        this.roleRepository =roleRepository;
    }

    @Override
    public Event process(Event event) {
        switch (event.method) {
            case CURRENT_USER:
                processCurrentUser(event);
                break;
            case RESET_PASSWORD:
                processResetPassword(event);
                break;
            case FORGOT_PASSWORD_INIT:
                processForgotPasswordInit(event);
                break;
            case FORGOT_PASSWORD_FINISH:
                processForgotPasswordFinish(event);
                break;
            case CHANGE_PASSWORD:
                processChangePassword(event);
                break;
            case VALIDATE_TOKEN_MAIL:
                processValidateTokenMail(event);
                break;
            case RE_SEND_EMAIL:
                reSendEmail(event);
                break;
            case COUNT_USER_BY_TENANT:
                countUserByTenant(event);
                break;
            default:
                event = super.process(event);
                break;
        }
        return event;
    }


    private Event handlerNoneEmail(Event event) {
        return ErrorUtils.handleErrorResponse(ResponseStatusCode.NOT_FOUND.intValue(), event, ErrorKey.AuthErrorKey.NONE_EMAIL);
    }

    private Event handlerCurrentPasswordInvalid(Event event) {
        return ErrorUtils.handleErrorResponse(ResponseStatusCode.BAD_REQUEST.intValue(), event, ErrorKey.UserErrorKey.CURRENT_PASSWORD_INVALID);
    }

    private Event handlerNotMatchConfirmPassword(Event event) {
        return ErrorUtils.handleErrorResponse(ResponseStatusCode.BAD_REQUEST.intValue(), event, ErrorKey.AuthErrorKey.CONFIRM_PASSWORD_NOT_MATCH);
    }

    private Event handlerInvalidToken(Event event) {
        return ErrorUtils.handleErrorResponse(ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue(), event, ErrorKey.AuthErrorKey.INVALID_TOKEN);
    }

    private Event processCurrentUser(Event event) {
        String token = event.payload;
        if (tokenService.validateToken(token)) {
            event.payload = ObjectMapperUtil.toJsonString(current(token));
            event.statusCode = ResponseStatusCode.OK.intValue();
        }
        return event;
    }

    @SuppressWarnings("Duplicates")
    private Event processResetPassword(Event event) {
        logger.info("******-----resettPass-----*********");
        ResetPasswordInfo resetPasswordInfo = ObjectMapperUtil.objectMapper(event.payload, ResetPasswordInfo.class);
        UserEntity userEntity = userRepository.findOneByEmailIgnoreCase(resetPasswordInfo.getEmail());
        if (userEntity == null || !Integer.valueOf(Constants.EntityStatus.REGISTER).equals(userEntity.getActive())) {
            return handlerNoneEmail(event);
        }
        if (!resetPasswordInfo.getNewPassword().equals(resetPasswordInfo.getConfirmPassword())) {
            return handlerNotMatchConfirmPassword(event);
        }
        if (userEntity.getActivationToken() == null
                || !resetPasswordInfo.getForgotPasswordToken().equals(userEntity.getActivationToken())
                || !StringUtils.checkExpireTime(userEntity.getActivationTokenCreated(), applicationProperties.getActivation().getExpirePeriodActiveMail())) {
            return handlerInvalidToken(event);
        }
//        userEntity.setEncryptedPassword(resetPasswordInfo.getNewPassword());
        userEntity.setActivationToken(null);
        userEntity.setActivationTokenCreated(null);
        userEntity.setActive(Constants.EntityStatus.ACTIVE);
        userEntity.setUpdatedBy(userEntity.getEmail());
        update(userEntity.getId(), userEntity);
        userEntity.setEncryptedPassword(resetPasswordInfo.getNewPassword());
        userRepository.save(userEntity);
        logger.debug("User active and reset password user: #{}", userEntity.getEmail());

        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    @SuppressWarnings("Duplicates")
    private Event processForgotPasswordInit(Event event) {
        ForgotPasswordInfo info = ObjectMapperUtil.objectMapper(event.payload, ForgotPasswordInfo.class);
        UserEntity userEntity = this.userRepository.findOneByEmailIgnoreCase(info.getEmail());
        if (userEntity == null) {
            return handlerNoneEmail(event);
        }
        userEntity.setForgotPasswordToken(UUID.randomUUID().toString());
        userEntity.setForgotPasswordTokenCreated(System.currentTimeMillis());
        userRepository.save(userEntity);
        if (applicationProperties.getActivation().isEnableMail()) {
            mailService.sendPasswordResetMail(userEntity);
            logger.debug("Request forgot password user: #{}", userEntity.getEmail());
        }

        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    @SuppressWarnings("Duplicates")
    private Event processForgotPasswordFinish(Event event) {
        ResetPasswordInfo resetPasswordInfo = ObjectMapperUtil.objectMapper(event.payload, ResetPasswordInfo.class);
        UserEntity userEntity = userRepository.findOneByEmailIgnoreCase(resetPasswordInfo.getEmail());
        if (userEntity == null) {
            return handlerNoneEmail(event);
        }
        if (!resetPasswordInfo.getNewPassword().equals(resetPasswordInfo.getConfirmPassword())) {
            return handlerNotMatchConfirmPassword(event);
        }
        if (userEntity.getForgotPasswordToken() == null
                || !resetPasswordInfo.getForgotPasswordToken().equals(userEntity.getForgotPasswordToken())
                || !StringUtils.checkExpireTime(userEntity.getForgotPasswordTokenCreated(), applicationProperties.getActivation().getExpirePeriodResetPassword())) {
            return handlerInvalidToken(event);
        }

        userEntity.setPassword(resetPasswordInfo.getNewPassword());
        userEntity.setForgotPasswordToken(null);
        userEntity.setForgotPasswordTokenCreated(null);
        userEntity.setEncryptedPassword(resetPasswordInfo.getNewPassword());
        userRepository.save(userEntity);
        logger.debug("Change forgot password user: #{}", userEntity.getEmail());
        event.statusCode = ResponseStatusCode.OK.intValue();

        return event;
    }

    private Event processChangePassword(Event event) {
        UserEntity userEntity = ObjectMapperUtil.objectMapper(event.payload, UserEntity.class);
        logger.debug("Resetting password for user #{}", userEntity.getId());
        UserEntity oldPasswordUserEntity = get(userEntity.getId());
        if (oldPasswordUserEntity == null) {
            return handlerNoneEmail(event);
        }
        //check trường hợp current password nhập không đúng, lưu ý cần phải xem user sửa password này có sửa chính nó không
        // nếu sửa chính nó thì cần check current password
        if (oldPasswordUserEntity.getId().equals(SecurityUtils.getUserId())
                && Boolean.FALSE.equals(oldPasswordUserEntity.authenticate(userEntity.getPassword()))) {
            return handlerCurrentPasswordInvalid(event);
        }

        if (userEntity.getNewPassword().equals(userEntity.getConfirmPassword())) {
            oldPasswordUserEntity.setEncryptedPassword(userEntity.getNewPassword());
            this.repository.save(oldPasswordUserEntity);
            event.statusCode = ResponseStatusCode.OK.intValue();
            logger.debug("Successfully reset password for user #{}", oldPasswordUserEntity.getId());
            event.payload = ObjectMapperUtil.toJsonString(oldPasswordUserEntity);
        } else {
            return handlerNotMatchConfirmPassword(event);
        }

        return event;
    }

    public UserEntity authenticate(String email, String password) {
        UserEntity userEntity;
        try {
            userEntity = findByEmail(email);
            logger.info("id Entity:"+userEntity.getId());
            if (userEntity.getActive() != null && userEntity.getActive().equals(Constants.EntityStatus.ACTIVE)
                    && Boolean.TRUE.equals(userEntity.authenticate(password))) {
                return userEntity;
            } else {
                return null;
            }
        } catch (EmailNotFoundException ex) {
            logger.error(ex.getMessage(), ex);
            return null;
        }
    }

    public UserEntity findByEmail(String email) {
        UserEntity userEntity = this.userRepository.findOneByEmailIgnoreCase(email);
        if (userEntity == null) {
            throw new EmailNotFoundException();
        }
        return userEntity;
    }

    public UserEntity current(String token) {
        TokenEntity tokenEntity = tokenService.parseToken(token);
        // check time in range
        long now = System.currentTimeMillis();
        if ((tokenEntity.getNotBefore() != null && tokenEntity.getNotBefore() > now)
                || (tokenEntity.getNotAfter() != null && tokenEntity.getNotAfter() < now)) {
            throw new ExpiredJwtException(null, null, TOKEN_EXPIRED);
        }
        TokenInfoDTO tokenInfoDTO = tokenService.getTokenInfo(tokenEntity);
        String userId = tokenInfoDTO.userId;
        UserEntity userEntity = this.userRepository.findFirstById(userId);
        if (userEntity == null) {
            throw new EmailNotFoundException();
        }

        if (userEntity.getRoleIds() != null && !userEntity.getRoleIds().isEmpty()) {
            List<vn.vnpt.oneiot.common.entities.RoleEntity> roleEntityList = dynamicRoleService.findByRoleIds(userEntity.getRoleIds());
            fetchAuthoritiesFromRoles(userEntity, roleEntityList);
//            userRepository.save(userEntity);
        }
        userEntity.setAuthCode(tokenEntity.getAuthCode());
        return userEntity;
    }

    @Override
    protected void beforeCreate(UserEntity entity) {
        super.beforeCreate(entity);
        String password = Common.randomString(8);
        entity.setEncryptedPassword(password);
        entity.setNewPassword(password);
        entity.setActivationToken(UUID.randomUUID().toString());
        entity.setActivationTokenCreated(System.currentTimeMillis());
        entity.setActive(Constants.EntityStatus.REGISTER);
        //set type for new user from type of current user login
        UserEntity currentUserEntityLogin = userRepository.findFirstById(String.valueOf(SecurityUtils.getUserId()));
        if (currentUserEntityLogin != null && entity.getType() == null) {
            if (currentUserEntityLogin.getType().intValue() == vn.vnpt.oneiot.core.generic.Constants.UserType.PLATFROM_ADMIN ||
            currentUserEntityLogin.getType().intValue() == vn.vnpt.oneiot.core.generic.Constants.UserType.PLATFORM_USER) {
                entity.setType(vn.vnpt.oneiot.core.generic.Constants.UserType.PLATFORM_USER);
            } else {
                entity.setType(vn.vnpt.oneiot.core.generic.Constants.UserType.TENANT_USER);
            }
        }
        if (entity.getTenantId() == null) {
            entity.setTenantId(SecurityUtils.getTenantId());
        }
        if (entity.getFullName() == null) {
            entity.setFullName(entity.getFirstName() + " " + entity.getLastName());
        }
        addDefaultUserRole(entity);
    }

    private void addDefaultUserRole(UserEntity entity) {
        if (!entity.getRoleIds().contains(DEFAULT_ROLE_USER)) {
            entity.getRoleIds().add(DEFAULT_ROLE_USER);
        }
    }

    @Override
    protected void afterCreate(UserEntity entity) {
        super.afterCreate(entity);
        //send email create successful
        if (applicationProperties.getActivation().isEnableMail()) {
            mailService.sendCreationEmail(entity);
        }
    }

    private Event reSendEmail(Event event) {
        UserEntity user = ObjectMapperUtil.objectMapper(event.payload, UserEntity.class);
        UserEntity userEntity = userRepository.findOneByEmailIgnoreCase(user.getEmail());
        // check nếu chưa kích hoạt thì mới gửi mail active
        if (userEntity == null) {
            event.statusCode = ResponseStatusCode.NOT_FOUND.intValue();
            event.errorText = "User not found";
            return event;
        }
        if (userEntity.getActive() == Constants.EntityStatus.REGISTER) {
            logger.debug("Re send email #{}", user.getEmail());
            userEntity.setActivationToken(UUID.randomUUID().toString());
            userEntity.setActivationTokenCreated(System.currentTimeMillis());
            userRepository.save(userEntity);
            mailService.sendCreationEmail(userEntity);
            event.statusCode = ResponseStatusCode.OK.intValue();
            return event;
        }
        if (userEntity.getActive() == Constants.EntityStatus.ACTIVE) {
            event.statusCode = ResponseStatusCode.CONFLICT.intValue();
            event.errorText = "User has already active";
        }
        event.statusCode = ResponseStatusCode.OPERATION_NOT_ALLOWED.intValue();
        event.errorText = "Not allow send activation email to inactive user";
        return event;
    }

    @Override
    protected void beforeUpdate(UserEntity entity) {
        super.beforeUpdate(entity);
        //set password again for entity
        UserEntity old = get(entity.getId());
        entity.setPassword(old.getPassword());
        entity.setJwtToken(old.getJwtToken());
        entity.setActivationToken(old.getActivationToken());
        entity.setActivationTokenCreated(old.getActivationTokenCreated());
        entity.setForgotPasswordToken(old.getForgotPasswordToken());
        entity.setForgotPasswordTokenCreated(old.getForgotPasswordTokenCreated());
        //set fullName again
        entity.setFullName(entity.getFirstName() + " " + entity.getLastName());
        List<String> rolesId = entity.getRoleIds();
        addDefaultUserRole(entity);
        if (!rolesId.isEmpty()) {
            List<RoleEntity> roles = roleRepository.findAllByRoleIdIn(rolesId);
            fetchAuthoritiesFromRoles(entity, roles);
        }
    }

    private void fetchAuthoritiesFromRoles(UserEntity entity, List<RoleEntity> roles) {
        List<String> priIds = new ArrayList<>();
        if (roles != null && !roles.isEmpty()) {
            for (RoleEntity roleEntity : roles) {
                entity.getAuthorities().add(roleEntity.getRoleName());
                for (String pvId : roleEntity.getPrivilegeIds()) {
                    if (!priIds.contains(pvId)) priIds.add(pvId);
                }
            }
        }
        if (!priIds.isEmpty()) {
            List<PrivilegeEntity> privilegeEntityList = privilegeService.findAllByIds(priIds);
            for (PrivilegeEntity privilegeEntity : privilegeEntityList) {
                entity.getAuthorities().add(privilegeEntity.getName());
            }
        }
    }

    @Override
    protected void afterUpdate(UserEntity old, UserEntity updated) {
        super.afterUpdate(old, updated);
        //change status tenant follow status of tenantAdmin
        TenantEntity tenantEntity = tenantRepository.findFirstByCode(updated.getTenantId());
        logger.info("tenantId:"+tenantEntity.getId());
        if (tenantEntity != null && tenantEntity.getAdminId().equals(updated.getId())) {
            tenantEntity.setActive(updated.getActive());
            tenantService.update(tenantEntity.getId(), tenantEntity);
        }
    }

    @SuppressWarnings("Duplicates")
    @Override
    protected Event processCreate(Event event) {
        //check trùng Email
        UserEntity userEntity = ObjectMapperUtil.objectMapper(event.payload, UserEntity.class);
        UserEntity other = userRepository.findOneByEmailIgnoreCase(userEntity.getEmail());
        if (other != null) {
            return ErrorUtils.handleErrorResponse(ResponseStatusCode.CONFLICT.intValue(), event, ErrorKey.UserErrorKey.DUPLICATE_EMAIL);
        }

        return super.processCreate(event);
    }

    @SuppressWarnings("Duplicates")
    private Event processValidateTokenMail(Event event) {
        ResetPasswordInfo resetPasswordInfo = ObjectMapperUtil.objectMapper(event.payload, ResetPasswordInfo.class);
        UserEntity userEntity = userRepository.findOneByEmailIgnoreCase(resetPasswordInfo.getEmail());
        if (userEntity == null) {
            return handlerNoneEmail(event);
        }
        if (resetPasswordInfo.getForgotPasswordToken() == null || resetPasswordInfo.getForgotPasswordToken().isEmpty()) {
            return handlerInvalidToken(event);
        }
        if (resetPasswordInfo.getActive().intValue() == Constants.EntityStatus.ACTIVE && !org.apache.commons.lang3.StringUtils.equals(resetPasswordInfo.getForgotPasswordToken(), userEntity.getActivationToken())) {
            return handlerInvalidToken(event);
        }
        if (resetPasswordInfo.getActive().intValue() == Constants.EntityStatus.IN_ACTIVE && !org.apache.commons.lang3.StringUtils.equals(resetPasswordInfo.getForgotPasswordToken(), userEntity.getForgotPasswordToken())) {
            return handlerInvalidToken(event);
        }

        long tokenTimePeriod;
        long tokenCreated;
        if (resetPasswordInfo.getActive().intValue() == Constants.EntityStatus.ACTIVE) {
            tokenTimePeriod = applicationProperties.getActivation().getExpirePeriodActiveMail();
            tokenCreated = userEntity.getActivationTokenCreated();
        } else {
            tokenTimePeriod = applicationProperties.getActivation().getExpirePeriodResetPassword();
            tokenCreated = userEntity.getForgotPasswordTokenCreated();
        }

        if ((userEntity.getActivationToken() == null && userEntity.getForgotPasswordToken() == null)
                || !StringUtils.checkExpireTime(tokenCreated, tokenTimePeriod)) {
            return handlerInvalidToken(event);
        }

        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }
    private Event countUserByTenant(Event event) {
        logger.info("[countUserByTenant] receive event : {}",ObjectMapperUtil.toJsonString(event));
        String tenantCode = event.payload ;
        if(org.apache.commons.lang3.StringUtils.isEmpty(tenantCode)){
            event.statusCode = ResponseStatusCode.BAD_REQUEST.intValue();
            logger.error("[countUserByTenant] tenantCode empty, event : {}",ObjectMapperUtil.toJsonString(event));
            return event;
        }
        Integer deviceTotal = userRepository.countAllByTenantId(tenantCode);
        event.statusCode = ResponseStatusCode.OK.intValue();
        event.payload = deviceTotal.toString();
        logger.info("[countUserByTenant] send event : {}",ObjectMapperUtil.toJsonString(event));
        return event ;
    }
//
//    public void deleteRoleRelationByUserId(Set<String> deleteIds) {
//        if (deleteIds.isEmpty()) return;
//        String query = "DELETE FROM base_user_role where user_id IN :ids";
//        Query q = entityManager.createNativeQuery(query);
//        q.setParameter("ids", deleteIds);
//        q.executeUpdate();
//    }

//    public void deleteUser(Set<String> deleteIds) {
//        if (deleteIds.isEmpty()) return;
//        String query = "DELETE FROM base_users where id IN :ids";
//        Query q = entityManager.createNativeQuery(query);
//        q.setParameter("ids", deleteIds);
//        q.executeUpdate();
//    }

//    @Override
//    protected void afterSoftDelete(UserEntity entity) {
//        super.afterSoftDelete(entity);
//        //kiểm tra user này có phải tenant admin không, nếu đúng thì phải thực hiện xóa tenant
//        TenantEntity tenantEntity = tenantRepository.findFirstByCode(entity.getTenantId());
//        if (tenantEntity != null && tenantEntity.getAdminId().equals(entity.getId())) {
//            tenantService.softDelete(tenantEntity.getId());
//        }
//    }

//    @Override
//    public String addMultipleTenantQuery(String query) {
//        if(query.isEmpty()){
//            query="tenantId==" + SecurityUtils.getTenantId();
//            logger.info("query1:"+query);
//            return query;
//        }else {
//            query=query + ";tenantId==" + SecurityUtils.getTenantId();
//            return query;
//        }
//    }
    @Override
    protected List<UserEntity> bonusInfoOrConvertInfoResultSearch(List<UserEntity> resultSearch){
        List<String> tenantIds = resultSearch.stream().map(el -> el.getTenantId()).collect(Collectors.toList());
        List<TenantEntity> tenants = tenantRepository.findAllByCodeIn(tenantIds);
        for(UserEntity user: resultSearch){
            if(user.getActive() != Constants.EntityStatus.DELETED){
                for(TenantEntity tenant : tenants){
                    if(tenant.getCode().equals(user.getTenantId())){
                        user.setActive(tenant.getActive() != Constants.EntityStatus.ACTIVE ? tenant.getActive() : user.getActive());
                    }
                }
            }
        }
        return resultSearch;
    }

    @Override
    protected UserEntity bonusInfoOrConvertInfoResultGetOne(UserEntity resultOne){
        TenantEntity tenantEntity = tenantRepository.findFirstByCode(resultOne.getTenantId());
        if(resultOne.getActive() != Constants.EntityStatus.DELETED){
            resultOne.setActive(tenantEntity.getActive() != Constants.EntityStatus.ACTIVE ? tenantEntity.getActive(): resultOne.getActive());
        }
        return resultOne;
    }

    @Override
    protected boolean checkValidActive(Event event, String id){
        UserEntity userEntity = userRepository.findFirstById(id);
        if(userEntity.getType() == vn.vnpt.oneiot.core.generic.Constants.UserType.TENANT_ADMIN) return true;
        TenantEntity tenantEntity = tenantRepository.findFirstByCode(userEntity.getTenantId());
        if(tenantEntity.getActive() != Constants.EntityStatus.ACTIVE){
            event.statusCode = ResponseStatusCode.BAD_REQUEST.intValue();
            event.errorText = "global.messages.tenant.inactive";
            return false;
        }
        return true;
    }
}
