package vn.vnpt.oneiot.core.mongo.services;

import com.mongodb.QueryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.support.DefaultConversionService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.core.query.BasicQuery;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.base.constants.Constants;
import vn.vnpt.oneiot.base.errors.ErrorKey;
import vn.vnpt.oneiot.base.errors.RemoveHadUsedEntityException;
import vn.vnpt.oneiot.base.errors.RemoveSystemEntityException;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.rsql.mongo.ComparisonToCriteriaConverter;
import vn.vnpt.oneiot.base.rsql.mongo.RsqlMongoAdapter;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.base.utils.StringUtils;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.common.entities.*;
import vn.vnpt.oneiot.common.exceptions.BadRequestException;
import vn.vnpt.oneiot.common.exceptions.PlatformException;
import vn.vnpt.oneiot.core.api.nbi.models.ErrorInfo;
import vn.vnpt.oneiot.core.api.nbi.models.StringIdInfo;
import vn.vnpt.oneiot.core.mongo.entity.UserEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCrudService;
import vn.vnpt.oneiot.core.mongo.repositories.DynamicRoleRepository;
import vn.vnpt.oneiot.core.mongo.repositories.UserRepository;
import vn.vnpt.oneiot.core.utils.ErrorUtils;
import vn.vnpt.oneiot.core.utils.SecurityUtils;
import javax.transaction.Transactional;
import java.util.HashSet;
import java.util.List;

/**
 * Created by huyvv
 * Date: 12/03/2020
 * Time: 2:50 PM
 * for all issues, contact me: <EMAIL>
 **/
@Service
@Transactional
public class DynamicRoleService extends MgCrudService<RoleEntity, String> {
    @SuppressWarnings("unused")
    private static Logger logger = LoggerFactory.getLogger(DynamicRoleService.class);

    private DynamicRoleRepository roleRepository;
    private AccessControlPolicyService accessControlPolicyService;
    private UserService userService;
    private UserRepository userRepository;

    @Autowired
    private MongoMappingContext mongoMappingContext;

    @Autowired
    private MongoOperations mongoOperations;

    @Autowired
    public void setUserRepository(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    @Autowired
    public void setUserService(UserService userService) {
        this.userService = userService;
    }

    @Autowired
    public void setAccessControlPolicyService(AccessControlPolicyService accessControlPolicyService) {
        this.accessControlPolicyService = accessControlPolicyService;
    }

    public DynamicRoleService(DynamicRoleRepository repository) {
        super(RoleEntity.class);
        this.repository = this.roleRepository = repository;
    }

    public RoleEntity findByRoleIdAndHolder(String roleId, String holder) {
        return roleRepository.findFirstByRoleIdIsAndHolderIs(roleId, holder);
    }

    public List<RoleEntity> findByRoleIds(List<String> roleIds) {
        return roleRepository.findAllByRoleIdIn(roleIds);
    }

    @Override
    public Event process(Event event) {
        event = super.process(event);
        switch (event.method){
            case Constants.Method.GET_FULL_INFORMATION:
                StringIdInfo idInfo = ObjectMapperUtil.objectMapper(event.payload, StringIdInfo.class);
                logger.info("idInfo01:"+idInfo.getId());
                RoleEntity roleEntity = this.roleRepository.findFirstByRoleId(idInfo.getId());
                event.payload = ObjectMapperUtil.toJsonString(roleEntity);
                event.statusCode = ResponseStatusCode.OK.intValue();
                break;
            default:
                break;
        }
        return event;
    }

    @Override
    public String addMultipleTenantQuery(String query) {

        UserEntity currentUserEntity = userService.get(SecurityUtils.getUserId());
        HashSet<String> listRoleId = new HashSet<>(currentUserEntity.getRoleIds());
        //user được nhìn thấy các role mà nó dc assign
        if(query.indexOf("tenantId")>=0){
            String[] itemQuerys = query.split(";");
            query = "";
            for(String itemQuery : itemQuerys){
                if(itemQuery.startsWith("tenantId")){
                    String queryDefaultRole = "roleId=in=(" + StringUtils.setStringToString(listRoleId) + ")";
//                    if(SecurityUtils.isCurrentUserInRole(vn.vnpt.oneiot.core.constants.Constants.ROLE_SYSTEM_ADMIN)) return query;
                    query += String.format("(%s, %s);", itemQuery, queryDefaultRole);
                }else{
                    query += itemQuery+";";
                }
            }
            query = query.substring(0, query.length() -1);
        }
        return query;
    }

    @Override
    public Page<RoleEntity> search(String query, Pageable pageable){
        if(pageable == null){
            pageable = PageRequest.of(0, 20);
        }
        pageable =PageRequest.of(0,20);
        query = addMultipleTenantQuery(query);
        ComparisonToCriteriaConverter converter = new ComparisonToCriteriaConverter(new DefaultConversionService(), mongoMappingContext);
        RsqlMongoAdapter adapter = new RsqlMongoAdapter(converter);
        Query queryMongo;
        if(query == null || query.isEmpty()){
            queryMongo = new Query().with(pageable);
        }else{
//            queryMongo = Query.query(adapter.getCriteria(query, typeEntityClass)).with(pageable);
            queryMongo = Query.query(adapter.getCriteria(vn.vnpt.oneiot.base.utils.StringUtils.convertRsqlLikeToMongoRegex(query), typeEntityClass)).with(pageable);
        }
        long total = mongoOperations.count(queryMongo, typeEntityClass);
        List<RoleEntity> list = mongoOperations.find(queryMongo, typeEntityClass);
        return new PageImpl<>(list, pageable, total);
    }

    @Override
    protected Event processCreate(Event event) {
        //check trùng tên role
        RoleEntity roleEntity = ObjectMapperUtil.objectMapper(event.payload, RoleEntity.class);
        RoleEntity other = roleRepository.findFirstByRoleName(roleEntity.getRoleName());
        if(other != null){
            return handlerDuplicateName(event);
        }

        return super.processCreate(event);
    }

    @Override
    protected Event processUpdate(Event event) {
        //check trùng tên role
        RoleEntity roleEntity = ObjectMapperUtil.objectMapper(event.payload, RoleEntity.class);
        RoleEntity other = roleRepository.findFirstByRoleName(roleEntity.getRoleName());
        if(other != null && !other.getRoleId().equals(roleEntity.getRoleId())){
            return handlerDuplicateName(event);
        }
        roleEntity.setUpdatedBy(SecurityUtils.getCurrentUserLogin());
        roleEntity.setLastModifiedTime(System.currentTimeMillis());
        event.payload = ObjectMapperUtil.toJsonString(roleEntity);
        return super.processUpdate(event);
    }

    private Event handlerDuplicateName(Event event){
        return ErrorUtils.handleErrorResponse(ResponseStatusCode.CONFLICT.intValue(), event, ErrorKey.RoleErrorKey.DUPLICATE_ROLE_NAME);
    }

    @Override
    protected Event processBatchDelete(Event event){
        return super.processBatchDelete(event);
    }

    // xoá role vs privileges trong User and Tenant

    @Override
    protected void beforeSoftDelete(RoleEntity roleEntity){
        List<UserEntity> userEntitys = userRepository.findByRoleIds(roleEntity.getRoleId());
        for (UserEntity userEntity : userEntitys){
            if (!userEntity.getEmail().equals(roleEntity.getCreatedBy())) {
                throw new PlatformException(ErrorKey.RoleErrorKey.EXIST_USER_USE_ROLE, ResponseStatusCode.BAD_REQUEST);
            }
            userEntity.getRoleIds().remove(roleEntity.getRoleId());
            userRepository.save(userEntity);
        }
    }

    @Override
    public void softDelete(String id) {
        RoleEntity entity = roleRepository.findFirstByRoleId(id);
        if(entity.getName()!= null && entity.getName().equals(Constants.SYSTEM)){
            throw new RemoveSystemEntityException();
        }
        beforeSoftDelete(entity);
        roleRepository.delete(entity);
        afterSoftDelete(entity);
    }

    @Override
    protected void beforeCreate(RoleEntity entity) {
        super.beforeCreate(entity);
        //check description null -> set to empty
        if (entity.getDescription() == null){
            entity.setDescription("");
        }
        entity.setRoleId(entity.getRoleName()+"_"+System.currentTimeMillis());
        entity.setName(entity.getRoleName());
        entity.setCreatedBy(SecurityUtils.getCurrentUserLogin());
        entity.setHolder(SecurityUtils.getCurrentUserLogin());
        entity.setTenantId(SecurityUtils.getTenantId());

    }

    @Override
    protected void afterCreate(RoleEntity entity){
        UserEntity userEntity = userRepository.findFirstByEmail(SecurityUtils.getCurrentUserLogin());
        userEntity.getRoleIds().add(entity.getRoleId());
        userRepository.save(userEntity);
    }
}
