package vn.vnpt.oneiot.core.mongo.services;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.core.mongo.entity.PrivilegeEntity;
import vn.vnpt.oneiot.core.mongo.generic.MgCrudAdminService;
import vn.vnpt.oneiot.core.mongo.repositories.PrivilegeRepository;

import javax.transaction.Transactional;

/**
 * Created by huyvv
 * Date: 16/01/2020
 * Time: 11:00 AM
 * for all issues, contact me: <EMAIL>
 **/
@Service
@Transactional
public class PrivilegeService extends MgCrudAdminService<PrivilegeEntity, String> {
    @SuppressWarnings("unused")
    private static Logger logger = LoggerFactory.getLogger(PrivilegeService.class);
    private PrivilegeRepository privilegeRepository;

    public PrivilegeService(PrivilegeRepository privilegeRepository) {
        super(PrivilegeEntity.class);
        this.repository = this.privilegeRepository = privilegeRepository;
    }

}
