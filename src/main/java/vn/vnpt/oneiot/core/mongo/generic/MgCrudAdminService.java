package vn.vnpt.oneiot.core.mongo.generic;


import com.google.gson.Gson;
import com.mongodb.client.result.DeleteResult;
import cz.jirutka.rsql.parser.RSQLParserException;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.support.DefaultConversionService;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.core.query.*;
import org.springframework.util.StringUtils;
import vn.vnpt.oneiot.base.constants.Constants;
import vn.vnpt.oneiot.base.errors.RemoveSystemEntityException;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.rsql.mongo.ComparisonToCriteriaConverter;
import vn.vnpt.oneiot.base.rsql.mongo.RsqlMongoAdapter;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.core.api.nbi.models.PageInfo;
import vn.vnpt.oneiot.core.api.nbi.models.SearchInfo;
import vn.vnpt.oneiot.core.api.nbi.models.StringIdInfo;
import vn.vnpt.oneiot.core.mongo.entity.MgAbstractEntity;
import vn.vnpt.oneiot.core.utils.SecurityUtils;

import javax.persistence.EntityNotFoundException;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


@Transactional
@SuppressWarnings({"Duplicates", "unchecked"})
public class MgCrudAdminService<T extends MgAbstractEntity, ID> extends MgNoneCrudService <T, ID> {

    private static Logger logger = LoggerFactory.getLogger(MgNoneCrudService.class);

    @Autowired
    private MongoOperations mongoOperations;

    @Autowired
    private MongoMappingContext mappingContext;

    @Autowired
    private MongoTemplate mongoTemplate;

    public MgCrudAdminService(Class<T> typeEntityClass) {
        super(typeEntityClass);
    }

    public Event process(Event event){
        logger.info("method:"+event.method);
        switch (event.method){
            case Constants.Method.CREATE:
                return processCreate(event);
            case Constants.Method.UPDATE:
                return processUpdate(event);
            case Constants.Method.DELETE:
                return processDelete(event);
            case Constants.Method.BATCH_DELETE:
                return processBatchDelete(event);
            case Constants.Method.ACTIVE:
                return processActive(event);
            case Constants.Method.DE_ACTIVE:
                return processDeActive(event);
            case Constants.Method.GET_ONE:
                return processGetOne(event);
            case Constants.Method.SEARCH:
                return processSearch(event);
            case Constants.Method.SEARCH_ONE_FIELD:
                return processSearchOneFiled(event);
            case Constants.Method.DELETE_BY_CONDITION:
                return deleteByCondition(event);
            case Constants.Method.COUNT:
                return processCount(event);
            default:
                event.statusCode = ResponseStatusCode.NOT_IMPLEMENTED.intValue();
                event.errorText = "Method " + event.method + " is not supported";
                return event;
        }
    }

    protected Event processCount(Event event){
        event.payload = String.valueOf(count(event.payload));
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processCreate(Event event){
        T entity = ObjectMapperUtil.objectMapper(event.payload, typeEntityClass);
        event.payload = ObjectMapperUtil.toJsonString(create(entity));
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processDelete(Event event){
        StringIdInfo idInfo = ObjectMapperUtil.objectMapper(event.payload, StringIdInfo.class);
        softDelete((ID) idInfo.getId());
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processBatchDelete(Event event) {
        List<Long> ids = ObjectMapperUtil.listMapper(event.payload, Long.class);
        List<Long> fail = new ArrayList<>();
        for(Long id : ids){
            try{
                softDelete((ID) id);
            }catch (Exception ex){
                logger.error(ex.getMessage(), ex);
                fail.add(id);
            }
        }
        event.payload = ObjectMapperUtil.toJsonString(fail);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected boolean checkValidActive(Event event, String id){
        return true;
    }

    protected Event processActive(Event event){
        StringIdInfo idInfo = ObjectMapperUtil.objectMapper(event.payload, StringIdInfo.class);
        if(!checkValidActive(event,idInfo.getId())){
            return event;
        }
        activate((ID) idInfo.getId());
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processDeActive(Event event){
        StringIdInfo idInfo = ObjectMapperUtil.objectMapper(event.payload, StringIdInfo.class);
        deactivate((ID) idInfo.getId());
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected Event processGetOne(Event event){
        StringIdInfo stringIdInfo = ObjectMapperUtil.objectMapper(event.payload, StringIdInfo.class);
        event.payload = ObjectMapperUtil.toJsonString(bonusInfoOrConvertInfoResultGetOne(get((ID) stringIdInfo.getId())));
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected T bonusInfoOrConvertInfoResultGetOne(T resultOne){
        return resultOne;
    }

    protected Event processSearch(Event event){
        SearchInfo searchInfo = ObjectMapperUtil.objectMapper(event.payload, SearchInfo.class);
        String orders = searchInfo.getOrders();
        Pageable pageable;
        if(orders == null || "".equals(orders)){
            pageable = PageRequest.of(searchInfo.getPageNumber(), searchInfo.getPageSize());
        }else {
            pageable = PageRequest.of
                    (searchInfo.getPageNumber(), searchInfo.getPageSize(), vn.vnpt.oneiot.base.utils.StringUtils.toSort(orders));
        }
        Page<T> page = search(searchInfo.getQuery(), pageable);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(page.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(bonusInfoOrConvertInfoResultSearch(page.getContent())));

        event.payload = ObjectMapperUtil.toJsonString(pageInfo);
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    protected List<T> bonusInfoOrConvertInfoResultSearch(List<T> resultSearch){
        return resultSearch;
    }

    protected Event processSearchOneFiled(Event event) {
        SearchInfo searchInfo = ObjectMapperUtil.objectMapper(event.payload, SearchInfo.class);
        String query = searchInfo.getQuery();
        ComparisonToCriteriaConverter converter = new ComparisonToCriteriaConverter(new DefaultConversionService(),mappingContext);
        RsqlMongoAdapter adapter = new RsqlMongoAdapter(converter);
        logger.debug("queryyyy:"+ query);
        Criteria criteria = adapter.getCriteria(query, typeEntityClass);
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.match(criteria), Aggregation.group(searchInfo.getResultField()));
        List<Object> results = mongoTemplate.aggregate(aggregation, typeEntityClass, Object.class).getMappedResults();
        String json = new Gson().toJson(results);
        JSONArray array = new JSONArray(json);
        List<Object> resultValues = IntStream.range(0, array.length())
                .mapToObj(index -> ((JSONObject)array.get(index)).optString("_id"))
                .collect(Collectors.toList());

        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(resultValues.size());
        pageInfo.setData(new Gson().toJson(resultValues));
        event.payload = ObjectMapperUtil.toJsonString(pageInfo);
        event.statusCode = ResponseStatusCode.OK.intValue();
        logger.info(ObjectMapperUtil.toJsonString(event.payload));
        return event;
    }

    protected Event deleteByCondition(Event event) {
        ComparisonToCriteriaConverter converter = new ComparisonToCriteriaConverter(new DefaultConversionService(),mappingContext);
        RsqlMongoAdapter adapter = new RsqlMongoAdapter(converter);
        logger.debug("deleteByCondition:" + event.payload);
        Query q = Query.query(adapter.getCriteria(vn.vnpt.oneiot.base.utils.StringUtils.convertRsqlLikeToMongoRegex(event.payload), typeEntityClass));
        DeleteResult deleteResult = mongoOperations.remove(q, typeEntityClass);
        event.payload = ObjectMapperUtil.toJsonString(deleteResult.getDeletedCount());
        event.statusCode = ResponseStatusCode.OK.intValue();
        logger.info(ObjectMapperUtil.toJsonString(event.payload));
        return event;
    }

    @Override
    public T create(T entity) {
        beforeCreate(entity);
        repository.save(entity);
        afterCreate(entity);
        return entity;
    }

    protected void beforeCreate(T entity) {
        entity.setCreated(System.currentTimeMillis());
        entity.setUpdated(entity.getCreated());
        if(entity.getCreatedBy() == null) {
            String currentUsername = SecurityUtils.getCurrentUserLogin();
            entity.setCreatedBy(currentUsername);
        }
        if (entity.getUpdatedBy() == null) {
            entity.setUpdatedBy(entity.getCreatedBy());
        }
        if(entity.getActive() == null) {
            entity.setActive(Constants.EntityStatus.ACTIVE);
        }
    }

    public List<T> searchAllByQuery(String query){
        if(StringUtils.isEmpty(query)){
            return repository.findAll();
        }
        try {
            ComparisonToCriteriaConverter converter = new ComparisonToCriteriaConverter(new DefaultConversionService(),mappingContext);
            RsqlMongoAdapter adapter = new RsqlMongoAdapter(converter);
            Query q = Query.query(adapter.getCriteria(vn.vnpt.oneiot.base.utils.StringUtils.convertRsqlLikeToMongoRegex(query), typeEntityClass));
            q.collation(Collation.of("en").strength(Collation.ComparisonLevel.identical()));
            List<T> data =mongoOperations.find(q, typeEntityClass);
            return data;
        } catch(RSQLParserException pe) {
            logger.error("SEARCH FAIL: {}",query);
            logger.error(pe.getMessage(), pe);
            return new ArrayList<>();
        } catch (Exception e) {
            logger.error("SEARCH FAIL: {}",query);
            logger.error(e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    public Page<T> searchByQuery(String query, Pageable pageable){
        if(StringUtils.isEmpty(query)){
            return repository.findAll(pageable);
        }
        try {
            ComparisonToCriteriaConverter converter = new ComparisonToCriteriaConverter(new DefaultConversionService(),mappingContext);
            RsqlMongoAdapter adapter = new RsqlMongoAdapter(converter);
            Query q = Query.query(adapter.getCriteria(vn.vnpt.oneiot.base.utils.StringUtils.convertRsqlLikeToMongoRegex(query), typeEntityClass)).with(pageable);
            q.collation(Collation.of("en").strength(Collation.ComparisonLevel.identical()));
            long total = mongoOperations.count(q, typeEntityClass);
            List<T> data =mongoOperations.find(q, typeEntityClass);
            return new PageImpl<>(data, pageable, total);
        } catch(RSQLParserException pe) {
            logger.error("SEARCH FAIL: {}",query);
            logger.error(pe.getMessage(), pe);
            return emptyPage();
        } catch (Exception e) {
            logger.error("SEARCH FAIL: {}",query);
            logger.error(e.getMessage(), e);
            return emptyPage();
        }
    }

    protected void beforeSoftDelete(T entity){
        entity.setActive(Constants.EntityStatus.DELETED);
        entity.setUpdated(System.currentTimeMillis());
        entity.setUpdatedBy(SecurityUtils.getCurrentUserLogin());
    }

    protected Event processUpdate(Event event){
        T entity = ObjectMapperUtil.objectMapper(event.payload, typeEntityClass);
        event.payload = ObjectMapperUtil.toJsonString(update((ID) entity.getId(), entity));
        event.statusCode = ResponseStatusCode.OK.intValue();
        return event;
    }

    @Override
    public T update(ID id, T entity) {
        beforeUpdate(entity);
        T old = get(id);
        logger.info("update id");
        if(entity.getCreated() == null) entity.setCreated(old.getCreated());
        if(entity.getCreatedBy() == null) entity.setCreatedBy(old.getCreatedBy());
        if(old == null) {
            throw new EntityNotFoundException("No entity with id " + id);
        }
        repository.save(entity);
        afterUpdate(old,entity);
        return entity;
    }

    @Override
    protected void beforeUpdate(T entity) {
        entity.setUpdated(System.currentTimeMillis());
        entity.setUpdatedBy(SecurityUtils.getCurrentUserLogin());
        if(entity.getActive() == null) {
            entity.setActive(Constants.EntityStatus.ACTIVE);
        }
    }

    public Page<T> emptyPage() {
        return new Page<T>() {
            @Override
            public int getTotalPages() {
                return 0;
            }

            @Override
            public long getTotalElements() {
                return 0;
            }

            @Override
            public <U> Page<U> map(Function<? super T, ? extends U> function) {
                return null;
            }

            @Override
            public int getNumber() {
                return 0;
            }

            @Override
            public int getSize() {
                return 0;
            }

            @Override
            public int getNumberOfElements() {
                return 0;
            }

            @Override
            public List<T> getContent() {
                return new ArrayList<>();
            }

            @Override
            public boolean hasContent() {
                return false;
            }

            @Override
            public Sort getSort() {
                return null;
            }

            @Override
            public boolean isFirst() {
                return false;
            }

            @Override
            public boolean isLast() {
                return false;
            }

            @Override
            public boolean hasNext() {
                return false;
            }

            @Override
            public boolean hasPrevious() {
                return false;
            }

            @Override
            public Pageable nextPageable() {
                return null;
            }

            @Override
            public Pageable previousPageable() {
                return null;
            }

            @Override
            public Iterator<T> iterator() {
                return null;
            }
        };
    }

    public Page<T> search(String query, Pageable pageable) {
        query = addMultipleTenantQuery(query);
        return searchByQuery(query, pageable);
    }

    public void softDelete(ID id){
        T entity = get(id);
        if(entity.getCreatedBy()!= null && entity.getCreatedBy().equals(Constants.SYSTEM)){
            throw new RemoveSystemEntityException();
        }
        beforeSoftDelete(entity);
        repository.save(entity);
        afterSoftDelete(entity);
    }
    public void deactivate(ID id) {
        T t = repository.findById(id).orElse(null);
        if(t != null) {
            t.setActive(Constants.EntityStatus.IN_ACTIVE);
            update(id, t);
        }
    }

    public void activate(ID id) {
        T t = repository.findById(id).orElse(null);
        if(t != null) {
            t.setActive(Constants.EntityStatus.ACTIVE);
            update(id, t);
        }
    }

    public List<T> findAllByIds(List<ID> ids) {
        Iterable<T> iterable = repository.findAllById(ids);
        if (iterable != null && iterable.iterator() != null && iterable.iterator().hasNext()) {
            List<T> result = new ArrayList<>();
            iterable.forEach(result::add);
            return result;
        }
        return null;
    }
    protected Event setResultSuccess(Event event, String payload) {
        event.statusCode = ResponseStatusCode.OK.intValue();
        event.errorText = vn.vnpt.oneiot.core.constants.Constants.ResultMessage.SUCCESS;
        event.payload = payload;
        return event;
    }

    public void updateDataUsingNativeQuery(String stringQuery, Update update) {
        Query query = new BasicQuery(stringQuery);
        mongoTemplate.updateMulti(query, update, mongoTemplate.getCollectionName(typeEntityClass));
    }

    public void updateByQuery(Query  query, Update update) {
        mongoTemplate.updateMulti(query, update, mongoTemplate.getCollectionName(typeEntityClass));
    }
}
