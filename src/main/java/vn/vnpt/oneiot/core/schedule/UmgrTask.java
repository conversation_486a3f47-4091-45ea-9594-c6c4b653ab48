package vn.vnpt.oneiot.core.schedule;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.base.constants.Constants;
import vn.vnpt.oneiot.core.configuration.ApplicationProperties;
import vn.vnpt.oneiot.core.mongo.entity.UserEntity;
import vn.vnpt.oneiot.core.mongo.repositories.UserRepository;
import vn.vnpt.oneiot.core.mongo.services.UserService;
import vn.vnpt.oneiot.core.mongo.services.TokenService;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by huyvv
 * Date: 18/02/2020
 * Time: 1:27 PM
 * for all issues, contact me: <EMAIL>
 **/
//@Service
//@Transactional
//public class UmgrTask {
//    private static Logger logger = LoggerFactory.getLogger(UmgrTask.class);
//    private ApplicationProperties applicationProperties;
//    private UserRepository userRepository;
//    private UserService userService;
//
//    @Autowired
//    private TokenService tokenService;
//
//    @Autowired
//    public void setUserRepository(UserRepository userRepository) {
//        this.userRepository = userRepository;
//    }
//
//    @Autowired
//    public void setUserService(UserService userService) {
//        this.userService = userService;
//    }
//
//    public UmgrTask(ApplicationProperties applicationProperties){
//        this.applicationProperties = applicationProperties;
//    }

//    @SuppressWarnings("Duplicates")
    /** Da implement tien trinh background de xoa du lieu nen khong can phan code nay nua **/
//    @Scheduled(fixedRate = 60000)
//    public void deleteRegisterUser() {
//        long currentTime = System.currentTimeMillis();
//        long inActiveTime = currentTime - applicationProperties.getActivation().getExpirePeriodActiveMail() * 1000;
//        long softDeleteTime = currentTime - applicationProperties.getTimeAfterSoftDelete() * 1000;
//
//        List<UserEntity> userEntityInActive = userRepository.findAllByActiveInAndUpdatedLessThan
//                (new int[]{Constants.EntityStatus.REGISTER}, inActiveTime);
//        List<UserEntity> userEntityRegister = userRepository.findAllByActiveInAndCreatedLessThan
//                (new int[]{Constants.EntityStatus.REGISTER}, inActiveTime);
//        List<UserEntity> userEntitySoftDelete = userRepository.findAllByActiveInAndUpdatedLessThan
//                (new int[]{Constants.EntityStatus.DELETED}, softDeleteTime);
//        List<UserEntity> userEntitySoftDeleteNoRegisterYet = userRepository.findAllByActiveInAndCreatedLessThan
//                (new int[]{Constants.EntityStatus.DELETED}, softDeleteTime);
//        List<UserEntity> userEntityList = new ArrayList<>();
//        userEntityList.addAll(userEntityInActive);
//        userEntityList.addAll(userEntityRegister);
//        userEntityList.addAll(userEntitySoftDelete);
//        userEntityList.addAll(userEntitySoftDeleteNoRegisterYet);
//        Set<String> deleteIds = new HashSet<>();
//        if(!userEntityList.isEmpty()){
//            logger.info("check in-active, soft-delete User for delete");
//        }
//        for(UserEntity userEntity : userEntityList){
//            deleteIds.add(userEntity.getId());
//        }
//
//        //delete role
//        userService.deleteRoleRelationByUserId(deleteIds);
//        //delete user
//        userService.deleteUser(deleteIds);
//        tokenService.deleteTokenByUsers(userEntityList);
//    }
//}
