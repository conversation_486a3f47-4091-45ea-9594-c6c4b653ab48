package vn.vnpt.oneiot.core.schedule;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.core.mongo.services.TokenService;

import javax.transaction.Transactional;

@Service
@Transactional
public class TokenTask {
    @Autowired
    public TokenTask(TokenService tokenService){
        this.tokenService = tokenService;
    }
    private TokenService tokenService;

    @Scheduled(initialDelay = 1000,fixedRate = 24*60*60*1000)
    public void clearTokenExpired(){
        tokenService.clearTokenExpired();
    }
}
