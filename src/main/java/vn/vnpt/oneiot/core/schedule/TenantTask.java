package vn.vnpt.oneiot.core.schedule;

import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.vnpt.oneiot.base.constants.AMQPConstant;
import vn.vnpt.oneiot.base.constants.Constants;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.event.EventBus;
import vn.vnpt.oneiot.core.configuration.ApplicationProperties;
import vn.vnpt.oneiot.core.mongo.entity.TenantEntity;
import vn.vnpt.oneiot.core.mongo.repositories.DynamicRoleRepository;
import vn.vnpt.oneiot.core.mongo.repositories.TenantRepository;
import vn.vnpt.oneiot.core.mongo.repositories.UserRepository;
import vn.vnpt.oneiot.core.jpa.services.SubscriptionInfoService;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static vn.vnpt.oneiot.core.generic.Constants.Method.*;
import static vn.vnpt.oneiot.core.generic.Constants.ROUTING_KEY_APPLICATION;

/**
 * Created by huyvv
 * Date: 26/02/2020
 * Time: 2:49 PM
 * for all issues, contact me: <EMAIL>
 **/
@Service
@Transactional
public class TenantTask {

    private static Logger logger = LoggerFactory.getLogger(TenantTask.class);
    private EventBus eventBus;
    private ApplicationProperties applicationProperties;
    private UserRepository userRepository;
    private TenantRepository tenantRepository;
    private DynamicRoleRepository roleRepository;
    private SubscriptionInfoService subscriptionInfoService;

    @Autowired
    public void setEventBus(EventBus eventBus) {
        this.eventBus = eventBus;
    }

    @Autowired
    public void setTenantRepository(TenantRepository tenantRepository) {
        this.tenantRepository = tenantRepository;
    }

    @Autowired
    public void setUserRepository(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    @Autowired
    public void setRoleRepository(DynamicRoleRepository roleRepository) {
        this.roleRepository = roleRepository;
    }

    @Autowired
    public void setSubscriptionInfoService(SubscriptionInfoService subscriptionInfoService) {
        this.subscriptionInfoService = subscriptionInfoService;
    }

    public TenantTask(ApplicationProperties applicationProperties) {
        this.applicationProperties = applicationProperties;
    }

    @SuppressWarnings("Duplicates")
//    @Scheduled(fixedRate = 60000)
    public void deleteTenantOutDate() {
        long currentTime = System.currentTimeMillis();
        long inActiveTime = currentTime - applicationProperties.getActivation().getExpirePeriodActiveMail() * 1000;
        long softDeleteTime = currentTime - applicationProperties.getTimeAfterSoftDelete() * 1000;

        List<TenantEntity> tenantEntityInActive = tenantRepository.findAllByActiveInAndUpdatedLessThan
                (new int[]{Constants.EntityStatus.REGISTER}, inActiveTime);
        List<TenantEntity> tenantEntityRegister = tenantRepository.findAllByActiveInAndCreatedLessThan
                (new int[]{Constants.EntityStatus.REGISTER}, inActiveTime);
        List<TenantEntity> tenantEntitySoftDelete = tenantRepository.findAllByActiveInAndUpdatedLessThan
                (new int[]{Constants.EntityStatus.DELETED}, softDeleteTime);

        List<TenantEntity> tenantEntityList = new ArrayList<>();
        tenantEntityList.addAll(tenantEntityInActive);
        tenantEntityList.addAll(tenantEntityRegister);
        tenantEntityList.addAll(tenantEntitySoftDelete);

        Set<String> tenantIds = new HashSet<>();
        for(TenantEntity tenantEntity : tenantEntityList){
            tenantIds.add(tenantEntity.getCode());
        }
        if(!tenantEntityList.isEmpty()){
            logger.info("check in-active, soft-delete Tenant for delete");
            //delete role
            roleRepository.deleteAllByTenantIdIn(tenantIds);
            //delete user
            userRepository.deleteAllByTenantIdIn(tenantIds);

            //delete devices of tenant
            Event eventDevice = new Event();
            eventDevice.method = DELETE_DEVICES_BY_TENANT_IDS;
            eventDevice.payload = new Gson().toJson(tenantIds);
            eventBus.publish(AMQPConstant.AMQP_EXCHANGE_INTERNAL, AMQPConstant.ROUTING_KEY_INTERNAL_DEVICEMGMT, eventDevice);
            //delete apps of tenant
            Event eventApp = new Event();
            eventApp.method = DELETE_APPS_BY_TENANT_IDS;
            eventApp.payload = new Gson().toJson(tenantIds);
            eventBus.publish(AMQPConstant.ROUTING_KEY_INTERNAL_DEVICEMGMT, ROUTING_KEY_APPLICATION, eventApp);
            //delete subscriptionInfo
            for (String tenant : tenantIds) {
                subscriptionInfoService.deleteBySubscriptionId(tenant);
            }

            //delete tenant, phải xóa tenant sau vì các bảng khác có khóa ngoại đến tenant
            tenantRepository.deleteAllByCodeIn(tenantIds);
        }
    }
}
