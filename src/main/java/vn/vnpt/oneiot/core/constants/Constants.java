package vn.vnpt.oneiot.core.constants;


import static vn.vnpt.oneiot.base.constants.AMQPConstant.*;

/**
 * Created by huyvv
 * Date: 16/01/2020
 * Time: 11:00 AM
 * for all issues, contact me: <EMAIL>
 **/
public class Constants {
    public static final Long ROOT_TENANT = 1L;
    public static final Long BASE_ID_VALUE = 0L;
    public static final String JWT_SECRET = "secret_ONE_iot_2020";
    public static final String AUTH_TOKEN_PREFIX = "Bearer ";
    public static final String AUTH_HEADER_STRING = "Authorization";
    public static final String JWT_SCOPE = "scope";
    public static final String JWT_USER_ID = "user_id";
    public static final String JWT_TENANT_ID = "tenant_id";
    public static final String JWT_PERMISSION = "permissions";
    public static final Integer IS_DEFAULT = 1;
    public static final Integer BYTE = 1024;


    // ROLE ID
    public static final String DEFAULT_ROLE_USER = "ROLE_USER";
    public static final String ROLE_ANONYMOUS = "ROLE_ANONYMOUS";


    public static final String ROLE_SYSTEM_ADMIN = "ROLE_SYSTEM_ADMIN";
    public static final String ROLE_PLATFORM_ADMIN = "ROLE_PLATFORM_ADMIN";

    public static final String ROLE_TENANT_ADMIN = "ROLE_TENANT_ADMIN";
    public static final String ROLE_TENANT_USER = "ROLE_TENANT_USER";

    public static final String ROLE_MAIN_APP = "ROLE_MAIN_APP";
    public static final String ROLE_USER_APP = "ROLE_USER_APP";
    public static final String ROLE_DEVICE = "ROLE_DEVICE";



    // PRIVILEGE NAME

    public static final String PRIV_USER_CREATE = "User_Create";
    public static final String PRIV_USER_VIEW = "User_View";
    public static final String PRIV_USER_UPDATE = "User_Update";
    public static final String PRIV_USER_DELETE = "User_Delete";

    public static final String DEFAULT_TENANT = "DEFAULT_TENANT";
    public static final String PUBLIC_TENANT = "PUBLIC_TENANT";
    public static final String ADMIN_EMAIL = "admin";

    public static final String APP_CREATE = "App_Create";
    public static final String DEVICE_CREATE = "Device_Create";

    public static final String CNT_CONFIG_PLATFORM = "cnt_config_platform";
    public static final String ROUTING_KEY_API_CHARGING_SERVICE_RATE_PLAN_ORDER = ROUTING_KEY_API_CHARGING_ACCOUNTING_SINGLE + ".ratePlanOrder";;

    public static class REDIS_PARAMS {
        public final static String PATTERN_SET = "__keyevent@*__:set";
    }

    public interface AUTHEN_ENDPOINT_ERROR_KEY {
        String USER_NOT_FOUND_OR_NOT_ACTIVE = "UserNotFoundOrNotActive";
        String TENANT_NOT_FOUND_OR_NOT_ACTIVE = "TenantNotFoundOrNotActive";
        String ACCESS_TOKEN_INVALID = "AccessTokenInvalid";
        String REFRESH_TOKEN_INVALID = "RefreshTokenInvalid";
        String MISSING_ACCESS_TOKEN = "MissingAccessToken";
        String MISSING_REFRESH_TOKEN = "MissingRefreshToken";
        String ACCESS_TOKEN_NOT_BELONG_REFRESH_TOKEN = "AccessTokenNotBelongRefreshToken";
        String REFRESH_TOKEN_NOT_CONTAIN_ACCESS_TOKEN = "RefreshTokenNotContainAccessToken";
        String TOKEN_EXPIRED = "TokenExpired";
        String OPERATION_NOT_ALLOW = "OperationNotAllow";
        String TOKEN_TYPE_NOT_SUPPORT = "TokenTypeNotSupport";
        String UNEXPECTED_ERROR = "UnexpectedError";
        String APPLICATION_NOT_FOUND = "ApplicationNotFound";
        String APPLICATION_NOT_ACTIVE = "ApplicationNotActive";
        String TENANT_NOT_ACTIVE = "TenantNotActive";
        String USER_NOT_ACTIVE = "UserNotActive";
        String USERAPP_REACH_LIMITED = "UserAppReachLimited";
        String DEVICE_REACH_LIMITED = "DeviceReachLimited";
        String NOT_REGISTER_RATING_PLAN = "NotRegisterRatingPlan";
        String API_REACH_LIMITED = "ApiReachLimited";
        String TELEMETRY_REACH_LIMITED = "TelemetryReachLimited";
        String DATA_REACH_LIMITED = "DataReachLimited";
    }

    public static final String QUEUE_ORCHESTRATION_ROLLBACK_AUTHORIZATION = QUEUE_ORCHESTRATION_ROLLBACK + AUTHORIZATION_SERVICE;
    public static final String ROUTING_ORCHESTRATION_ROLLBACK_AUTHORIZATION = ROUTING_ORCHESTRATION_ROLLBACK + AUTHORIZATION_SERVICE;
    public static final String QUEUE_ORCHESTRATION_RETRY_AUTHORIZATION = QUEUE_ORCHESTRATION_RETRY + AUTHORIZATION_SERVICE;
    public static final String ROUTING_ORCHESTRATION_RETRY_AUTHORIZATION= ROUTING_ORCHESTRATION_RETRY + AUTHORIZATION_SERVICE;

    public static final class ResourceConstant {
        public static final String defaultAsarNameSAe = "asar-default.SAe." + vn.vnpt.oneiot.common.constants.Constants.M2M_SP_ID;
        public static final String defaultAsarNameCAe = "asar-default.CAe." + vn.vnpt.oneiot.common.constants.Constants.M2M_SP_ID;
    }

    public static final String ServiceKey = "Authorization";

    public static final class ResultMessage {
        public static final String SUCCESS = "OK";
        public static final String ERROR = "ERROR";
        public static final String NOK_MALFORMATTED_PARAMETER = "NOK_MALFORMATTED_PARAMETER";
        public static final String NOK_PARAMETER_INVALID = "NOK_PARAMETER_INVALID";
        public static final String TOKEN_INVALID = "TOKEN_INVALID";
        public static final String DEVICE_NOT_FOUND = "DEVICE_NOT_FOUND";
        public static final String NOK_INTERNAL_SERVER_ERROR = "NOK_INTERNAL_SERVER_ERROR";
        public static final String UNKNOWN = "UNKNOWN";
        public static final String APPLICATION_NOT_FOUND = "APPLICATION_NOT_FOUND";
        public static final String DATA_CHECK_ERROR = "Data check reach limit error";
        public static final String API_REACH_LIMIT = "Number Api Reach Limited";
        public static final String TELEMETRY_REACH_LIMIT = "Number Telemetry Reach Limited";
        public static final String DATA_VOLUME_REACH_LIMIT = "Data Volume Reach Limited";
        public static final String RATING_PLAN_EXPIRED = "Rating Plan Expired";
        public static final String NOT_REGISTER_RATING_PLAN = "Not Register Rating Plan";
    }

    public static final String CONTAINER_COMMAND_NAME = "cnt_command";
    public static final String CONTAINER_TELEMETRY_NAME = "cnt_telemetry";
}
