package vn.vnpt.oneiot.core.constants;

public interface UpdateFlagConstants {
    int AUTHENTICATION_AUTHORIZATION_FLAG = 0x01;
    int SUBSCRIPTION_NOTIFICATION_FLAG = 0x02;
    int REGISTRATION_GROUP_FLAG = 0x04;
    int DEVICE_MANAGEMENT_FLAG = 0x08;
    int DATA_MANAGEMENT_FLAG = 0x10;
    int CHARGING_ACCOUNTING_FLAG = 0x20;
    int APPLICATION_MANAGEMENT_FLAG = 0x40;
    int ORCHESTRATION_FLAG = 0x80;
    int HTTP_API_FLAG = 0x100;
    int MQTT_ADAPTER_FLAG = 0x200;
    int ALL_FLAG = AUTHENTICATION_AUTHORIZATION_FLAG | SUBSCRIPTION_NOTIFICATION_FLAG | REGISTRATION_GROUP_FLAG |
            DEVICE_MANAGEMENT_FLAG | DATA_MANAGEMENT_FLAG | CHARGING_ACCOUNTING_FLAG | ORCHESTRATION_FLAG |
            APPLICATION_MANAGEMENT_FLAG | HTTP_API_FLAG | MQTT_ADAPTER_FLAG;
}
