package vn.vnpt.oneiot.core.constants;

public interface TransactionState {

    public final Integer BEGIN = 1;
    public final Integer REQUEST_VALIDATION = BEGIN;
    public final Integer REQUEST_SYNCHRONIZED_TOKEN_FROM_DEVICE_MODULE = 2;
    public final Integer FINISH_SYNCHRONIZED_TOKEN_FROM_DEVICE_MODULE = 3;
    public final Integer FINISH_SYNCHRONIZED_TOKEN_FROM_SUBMODULE = 4;
    public final Integer AUTHORIZATION = 4;
    public final Integer RESOURCE_PROCESSING = 5;
    public final Integer PARENT_UPDATING = 6;
    public final Integer SEND_RESPONSE = 7;
    public final Integer RECORD_EVENT = 8;
    public final Integer FINAL = RECORD_EVENT;
}
