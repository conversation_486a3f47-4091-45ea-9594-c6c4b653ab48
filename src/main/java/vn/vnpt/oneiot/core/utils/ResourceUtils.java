package vn.vnpt.oneiot.core.utils;

import vn.vnpt.oneiot.common.constants.ResourceType;

public class ResourceUtils {

    public static boolean supportAccessControlPolicyIds(int resourceType) {
        if (resourceType == ResourceType.CSE_BASE ||
        resourceType == ResourceType.REMOTE_CSE ||
        resourceType == ResourceType.AE ||
        resourceType == ResourceType.CONTAINER ||
        resourceType == ResourceType.SUBSCRIPTION ||
        resourceType == ResourceType.SCHEDULE ||
        resourceType == ResourceType.LOCATION_POLICY ||
        resourceType == ResourceType.DELIVERY ||
        resourceType == ResourceType.REQUEST ||
        resourceType == ResourceType.GROUP ||
        resourceType == ResourceType.MGMT_CMD ||
        resourceType == ResourceType.MGMT_OBJ ||
        resourceType == ResourceType.EXEC_INSTANCE ||
        resourceType == ResourceType.NODE ||
        resourceType == ResourceType.M2M_SERVICE_SUBSCRIPTION_PROFILE ||
        resourceType == ResourceType.SERVICE_SUBSCRIBED_NODE ||
        resourceType == ResourceType.STATS_CONFIG ||
        resourceType == ResourceType.EVENT_CONFIG ||
        resourceType == ResourceType.STATS_COLLECT ||
        resourceType == ResourceType.SERVICE_SUBSCRIBED_APP_RULE ||
        resourceType == ResourceType.FLEXCONTAINER ||
        resourceType == ResourceType.TIMESERIES ||
        resourceType == ResourceType.DYNAMIC_AUTHORIZATION_CONSULTATION) {
            return true;
        }
        return false;
    }
}
