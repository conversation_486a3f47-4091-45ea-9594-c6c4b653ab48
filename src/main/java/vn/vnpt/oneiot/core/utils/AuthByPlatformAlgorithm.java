package vn.vnpt.oneiot.core.utils;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.common.constants.ResponseStatusCode;
import vn.vnpt.oneiot.common.entities.PermissionEntity;
import vn.vnpt.oneiot.common.entities.RoleEntity;
import vn.vnpt.oneiot.common.entities.TokenEntity;
import vn.vnpt.oneiot.common.exceptions.PlatformException;
import vn.vnpt.oneiot.core.api.nbi.models.TokenInfoDTO;
import vn.vnpt.oneiot.core.mongo.entity.*;
import vn.vnpt.oneiot.core.mongo.repositories.IdentityRepository;
import vn.vnpt.oneiot.core.mongo.repositories.RelationRepository;
import vn.vnpt.oneiot.core.mongo.repositories.RelationtypeRepository;
import vn.vnpt.oneiot.core.mongo.services.*;

import java.util.*;
import java.util.stream.Collectors;

import static vn.vnpt.oneiot.core.generic.Constants.UserType.PLATFORM_USER;
import static vn.vnpt.oneiot.core.generic.Constants.UserType.PLATFROM_ADMIN;

/**
 * Author: kiendt
 * Date: 4/17/2020
 * Contact: <EMAIL>
 */
@Component
public class AuthByPlatformAlgorithm {

    private static Logger logger = LoggerFactory.getLogger(AuthByPlatformAlgorithm.class);

    @Autowired
    private TokenService tokenService;

    @Autowired
    private UserService userService;

    @Autowired
    private PrivilegeService  privilegeService;

    @Autowired
    private DynamicRoleService dynamicRoleService;

    @Autowired
    private IdentityRepository identityRepository;

    @Autowired
    private RelationtypeRepository relationtypeRepository;

    @Autowired
    private RelationRepository relationRepository;

    @Autowired
    private RelationService relationService;

    /**
     * check if token expired or out of time
     *
     * @param token
     * @return
     */
    public TokenEntity isValidToken(String token) {
        try {
            TokenEntity tokenInfo = tokenService.parseToken(token);
            long now = System.currentTimeMillis();
            // check time in range
            if (tokenInfo.getNotBefore() < now && now < tokenInfo.getNotAfter()) {
                return tokenInfo;
            }
            return null;
        } catch (Exception e) {
            logger.error("Token invalid ", e);
            return null;
        }
    }


//    public List<String> hasOperationPrivilege(TokenEntity tokenInfo, List<String> operationList) {
//        List<String> validOperation = new ArrayList<>();
//        logger.debug("tokenInfo = {}", ObjectMapperUtil.toJsonString(tokenInfo));
//        // Xac thuc operation cho application
//        if (tokenInfo.getPermissions() != null && !tokenInfo.getPermissions().isEmpty()) {
//            for (PermissionEntity p : tokenInfo.getPermissions()) {
//                List<String> tempValidOp = getValidOperationByRoleIds(p.getRoleIDs(), operationList);
//                // Chay lan dau trong vong lap
//                if (validOperation.isEmpty()) {
//                    validOperation.addAll(tempValidOp);
//                    continue;
//                }
//                // Tu lan thu 2 trong vong lap
//                for (String op : tempValidOp) if (!validOperation.contains(op)) validOperation.add(op);
//            }
//        }
//        // Xac thuc operation cho user
//        else {
//            String extension = tokenInfo.getExtension();
//            JsonObject userInfo = new Gson().fromJson(extension, JsonObject.class);
//            String userId = userInfo.get("userId").getAsString();
//            logger.debug("check operation privilege for {}, with operation: {}, userId: {}", tokenInfo.getTokenId(), operationList, userId);
//            if (userId != null) {
//                UserEntity user = userService.get(userId);
//                validOperation = getValidOperationByRoleIds(user.getRoleIds(), operationList);
//            }
//        }
//        return validOperation;
//    }


    public List<String> hasOperationPrivilege(TokenEntity tokenInfo, List<String> operationList, List<String> targetIds, TokenInfoDTO tokenInfoDTO) {
        List<String> validOperation = new ArrayList<>();
        logger.debug("tokenInfo = {}", ObjectMapperUtil.toJsonString(tokenInfo));
        // Xac thuc operation cho application hoac device
        if (tokenInfo.getPermissions() != null && !tokenInfo.getPermissions().isEmpty()) {
            for (PermissionEntity p : tokenInfo.getPermissions()) {
                List<String> tempValidOp = getValidOperationByRoleIds(p.getRoleIDs(), operationList);
                // Chay lan dau trong vong lap
                if (validOperation.isEmpty()) {
                    validOperation.addAll(tempValidOp);
                    continue;
                }
                // Tu lan thu 2 trong vong lap
                for (String op : tempValidOp) if (!validOperation.contains(op)) validOperation.add(op);
            }
            if (targetIds == null || targetIds.isEmpty()) return validOperation;
            if (hasAccessGrantForAppOrDevice(targetIds, operationList, tokenInfoDTO)) return validOperation;
            return null;
        }
        // Xac thuc operation cho user
        else {
            String extension = tokenInfo.getExtension();
            JsonObject userInfo = new Gson().fromJson(extension, JsonObject.class);
            String userId = userInfo.get("userId").getAsString();
            logger.debug("check operation privilege for {}, with operation: {}, userId: {}", tokenInfo.getTokenId(), operationList, userId);
            if (userId != null) {
                UserEntity user = userService.get(userId);
                validOperation = getValidOperationByRoleIds(user.getRoleIds(), operationList);
                if (user.getType().intValue() != PLATFORM_USER && user.getType().intValue() != PLATFROM_ADMIN) {
                    if (hasAccessGrantForAppOrDevice(targetIds, operationList, tokenInfoDTO))
                        return validOperation;
                    return null;
                }
                return validOperation;
            }
        }
        return validOperation;
    }

    boolean hasAccessGrantForAppOrDevice(List<String> targetIdsInput, List<String> operations, TokenInfoDTO tokenInfoDTO) {
        if (targetIdsInput == null || targetIdsInput.isEmpty()) return true;
        // Loai bo duplicate string trong ds target can check
        List<String> targetIds = new ArrayList<>();
        targetIds.addAll(new HashSet<>(targetIdsInput));
        // Loai bo originator string trong ds target can check
        if (targetIds.contains(tokenInfoDTO.holderId)) targetIds.remove(tokenInfoDTO.holderId);
        if (targetIds == null || targetIds.isEmpty()) return true;

        List<IdentityEntity> listTarget = identityRepository.findAllByIdIn(targetIds);
        if (listTarget == null || listTarget.size() < targetIds.size()) {
            if (listTarget != null) listTarget.forEach(e -> {targetIds.remove(e.getId());});
            throw new PlatformException(targetIds + " is not existed", ResponseStatusCode.NOT_FOUND);
        }

        if(tokenInfoDTO.orgType.equals(TokenService.TOKEN_TYPE_USER)) {
            // If user, chi can check tenant
            for (IdentityEntity target : listTarget) {
                if (!target.getTenantId().equals(tokenInfoDTO.tenantId)) return false;
            }

        }else if(tokenInfoDTO.mainAppId != null && tokenInfoDTO.holderId != null && tokenInfoDTO.mainAppId.equals(tokenInfoDTO.holderId)) {
            // ELSE If MainApp (full quyen voi userapp, group, device)- Truy vấn IDENTITY
            for (IdentityEntity target : listTarget) {
                if (!target.getMainAppId().equals(tokenInfoDTO.holderId)) return false;
            }
        }else if(tokenInfoDTO.mainAppId != null && tokenInfoDTO.holderId != null && !tokenInfoDTO.mainAppId.equals(tokenInfoDTO.holderId)){
            for (IdentityEntity target : listTarget) {
                if (!target.getMainAppId().equals(tokenInfoDTO.mainAppId)) return false;
            }
            List<RelationEntity> relationList = relationRepository.findAllByOriginatorIdAndTargetIdIn(tokenInfoDTO.holderId, targetIds);
            Map<String, List<String>> mapRoleIds = new HashMap<>();
            Map<String, RelationTypeEntity> mapRelationType = relationService.getMapRelationType();
            relationList.stream().forEach(re -> {
                if (mapRoleIds.get(re.getTargetId()) == null) {
                    RelationTypeEntity relationTypeEntity = mapRelationType.get(re.getRelationTypeStr());
                    List<String> listTemp = new ArrayList<>();
                    listTemp.addAll(relationTypeEntity.getRoleIds());
                    mapRoleIds.put(re.getTargetId(), listTemp);
                } else {
                    RelationTypeEntity relationTypeEntity = mapRelationType.get(re.getRelationTypeStr());
                    relationTypeEntity.getRoleIds().removeAll(mapRoleIds.get(re.getTargetId()));
                    mapRoleIds.get(re.getTargetId()).addAll(relationTypeEntity.getRoleIds());
                }
            });
            for (String id : targetIds) {
                List<RoleEntity> roleEntities = dynamicRoleService.findByRoleIds(mapRoleIds.get(id));
                if (roleEntities == null || roleEntities.isEmpty()) return false;
                boolean hasGrantPrivilege = false;
                for (RoleEntity role : roleEntities) {
                    List<String> grantPrivileges = role.getPrivilegeIds().stream().map(e -> e.substring(5)).collect(Collectors.toList());
                    if (operations.stream().anyMatch(op -> grantPrivileges.contains(op))) {
                        hasGrantPrivilege = true;
                        break;
                    }
                }
                if (!hasGrantPrivilege) return hasGrantPrivilege;
            }
        }
        return true;
    }

    List<String> getValidOperationByRoleIds(List<String> roleIds, List<String> operationList) {
        List<PrivilegeEntity> privilegeEntityList = new ArrayList<>();
        List<String> validOperation = new ArrayList<>();
        if (roleIds != null && !roleIds.isEmpty()) {
            List<vn.vnpt.oneiot.common.entities.RoleEntity> roleEntityList = dynamicRoleService.findByRoleIds(roleIds);
            if (roleEntityList != null && !roleEntityList.isEmpty()) {
                List<String> pvIds = new ArrayList<>();
                for (vn.vnpt.oneiot.common.entities.RoleEntity roleEntity : roleEntityList) {
                    for (String pvId : roleEntity.getPrivilegeIds()) {
                        if (!pvIds.contains(pvId)) pvIds.add(pvId);
                    }
                }
                if (!pvIds.isEmpty()) privilegeEntityList = privilegeService.findAllByIds(pvIds);
            }
        }
        if (privilegeEntityList != null) {
            for (PrivilegeEntity privilegeEntity : privilegeEntityList) {
                for (String operation : operationList) {
                    if (privilegeEntity.getName().equals(operation)) validOperation.add(operation);
                }
            }
        }
        return validOperation;
    }
}
