@startuml
'https://plantuml.com/sequence-diagram

autonumber

EMQX -> http_adapter: Authentication Request
note left
       URL: /api/emqx/auth
       method: POST
       body:{
                "password": "Device_token",
                "username": "Device_id"
            }
    end note
http_adapter -> auth_core: Check token request
alt case holderId or accessToken is null or empty
    auth_core -> http_adapter: Check token response
    note right
       resp.statusCode = ResponseStatusCode.BAD_REQUEST.intValue();
       resp.errorText = "username (holderId) and password (accessToken) must be not null";
       return resp;
    end note
http_adapter --> EMQX: Authentication Response
end
alt Find IdentityEntity by holderId
    else entity == null
    auth_core -> http_adapter: Check token response
    note right
        resp.statusCode = ResponseStatusCode.NOT_FOUND.intValue();
         resp.errorText = "Entity (holderId) is not existed";
        return resp;
    end note
    else entity is IN_ACTIVE or DELETED
        auth_core -> http_adapter: Check token response
        note right
            resp.statusCode = ResponseStatusCode.OPERATION_NOT_ALLOWED.intValue();
            resp.errorText = "Entity is not ACTIVE";
            return resp;
        end note
http_adapter --> EMQX: Authentication Response
end
alt Find TokenEntity by accessToken, holderId, and ACTIVE
    else entity == null
    auth_core -> http_adapter: Check token response
    note right
        resp.statusCode = ResponseStatusCode.ORIGINATOR_HAS_NO_PRIVILEGE.intValue();
        resp.errorText = "password (accessToken) is not correct";
        return resp;
    end note
http_adapter --> EMQX: Authentication Response
end
alt Find TenantEntity by code
    else tenantEntity == null
        auth_core -> http_adapter: Check token response
        note right
            resp.statusCode = ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue();
            resp.errorText = "tenant was deleted";
            return resp;
        end note
    else tenantEntity is not ACTIVE
        auth_core -> http_adapter: Check token response
        note right
             resp.statusCode = ResponseStatusCode.OPERATION_NOT_ALLOWED.intValue();
             resp.errorText = "Tenant is not ACTIVE";
            return resp;
        end note
http_adapter --> EMQX: Authentication Response
end
alt Handle Exception
        auth_core -> http_adapter: Check token response
        note right
             event.statusCode = ResponseStatusCode.INTERNAL_SERVER_ERROR.intValue();
             event.errorText = e.getMessage();
             return resp;
        end note
http_adapter --> EMQX: Authentication Response
end
auth_core -> http_adapter: Check token response
note right
     resp.statusCode = ResponseStatusCode.OK.intValue();
     return resp;
end note
http_adapter --> EMQX: Authentication Response


@enduml
