package vn.vnpt.oneiot.core.utils;

import vn.vnpt.oneiot.base.constants.Constants;
import vn.vnpt.oneiot.base.event.Event;
import vn.vnpt.oneiot.base.utils.ObjectMapperUtil;
import vn.vnpt.oneiot.core.api.nbi.models.ErrorInfo;

/**
 * Author: kiendt
 * Date: 4/20/2020
 * Contact: <EMAIL>
 */
public class ErrorUtils {

    public static Event handleErrorResponse(int errorCode, Event event, String errorMsg) {
        event.errorText = errorMsg;
        event.statusCode = errorCode;
        return event;
    }
}
