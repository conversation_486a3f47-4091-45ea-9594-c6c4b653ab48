package vn.vnpt.oneiot.core;

import org.bson.BsonRegularExpression;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import vn.vnpt.oneiot.base.utils.DateUtils;
import vn.vnpt.oneiot.common.constants.Constants;
import vn.vnpt.oneiot.common.constants.ResourceType;
import vn.vnpt.oneiot.common.constants.ShortName;
import vn.vnpt.oneiot.common.entities.*;
import vn.vnpt.oneiot.common.utils.ResourceUtils;
import vn.vnpt.oneiot.core.mongo.repositories.TenantRepository;
import vn.vnpt.oneiot.core.mongo.entity.MappingAcpIdsResourceEntity;
import vn.vnpt.oneiot.core.mongo.repositories.AccessControlOriginatorRepository;
import vn.vnpt.oneiot.core.mongo.repositories.AccessControlPolicyRepository;
import vn.vnpt.oneiot.core.mongo.repositories.AccessControlRuleRepository;
import vn.vnpt.oneiot.core.mongo.repositories.MappingAcpIdsResourceRepository;
import vn.vnpt.oneiot.core.mongo.services.M2MServiceSubscriptionProfileService;
import vn.vnpt.oneiot.core.mongo.services.ServiceSubscribedAppRuleService;
import vn.vnpt.oneiot.core.mongo.services.ServiceSubscribedNodeService;

import java.math.BigInteger;
import java.util.Arrays;


@Component
public class CSEInitialize implements ApplicationRunner {

    protected static final Logger logger = LoggerFactory.getLogger(CSEInitialize.class);

    AccessControlOriginatorRepository accessControlOriginatorRepository;

    AccessControlRuleRepository accessControlRuleRepository;

    AccessControlPolicyRepository accessControlPolicyRepository;

    MappingAcpIdsResourceRepository mappingAcpIdsResourceRepository;

    TenantRepository tenantRepository;

    M2MServiceSubscriptionProfileService msspService;

    ServiceSubscribedNodeService svsnService;

    ServiceSubscribedAppRuleService asarService;

    public static String acpAdminId = null;

    public static String acpGuestId = null;

    public static String defaultMsspId = null;

    public static String csebaseSvsnId = null;

    public static String defaultAsarIdSAe = null;

    public static String defaultAsarIdCAe = null;

    private static boolean isCertificateRelease;

    @Value("${certificateRelease}")
    public void setIsCertificate(boolean isCertificateRelease) {
        CSEInitialize.isCertificateRelease = isCertificateRelease;
    }

    public static boolean isCertificateRelease() {
        return isCertificateRelease;
    }

    public void setCertificateRelease(boolean certificateRelease) {
        isCertificateRelease = certificateRelease;
    }

    @Autowired
    public void setAsarService(ServiceSubscribedAppRuleService asarService) {
        this.asarService = asarService;
    }

    @Autowired
    public void setSsvnService(ServiceSubscribedNodeService serviceSubscribedNodeService) {
        this.svsnService = serviceSubscribedNodeService;
    }

    @Autowired
    public void setMsspService(M2MServiceSubscriptionProfileService msspService) {
        this.msspService = msspService;
    }

    @Autowired
    public void setTenantRepository(TenantRepository tenantRepository) {
        this.tenantRepository = tenantRepository;
    }

    @Autowired
    public void setMappingAcpIdsResourceRepository(MappingAcpIdsResourceRepository mappingAcpIdsResourceRepository) {
        this.mappingAcpIdsResourceRepository = mappingAcpIdsResourceRepository;
    }

    @Autowired
    public void setAccessControlPolicyRepository(AccessControlPolicyRepository accessControlPolicyRepository) {
        this.accessControlPolicyRepository = accessControlPolicyRepository;
    }

    @Autowired
    public void setAccessControlRuleRepository(AccessControlRuleRepository accessControlRuleRepository) {
        this.accessControlRuleRepository = accessControlRuleRepository;
    }

    @Autowired
    public void setAccessControlOriginatorRepository(AccessControlOriginatorRepository accessControlOriginatorRepository) {
        this.accessControlOriginatorRepository = accessControlOriginatorRepository;
    }

    /**
     * Initialize the current launching CSE.
     */
    public void init() throws InterruptedException {
        logger.info("Initializating the Authorization with certificate release " + isCertificateRelease);
        initACP();
        initServiceSubscriptionProfile();
        initCSEBase();
    }

    /**
     * Creates a default {@link AccessControlPolicyEntity} resource in DataBase.
     */
    private void initACP() {


        /** init admin profile **/
        AccessControlPolicyEntity acp = accessControlPolicyRepository.findFirstByNameAndParentID(Constants.ADMIN_PROFILE_ID, "/" + Constants.CSE_ID);
        if (acp != null) {
            acpAdminId = acp.getResourceID();
            logger.info("Acp admin for cse base already existed, not init acp admin");
        } else {
            acp = new AccessControlPolicyEntity();
            acp.setParentID("/" + Constants.CSE_ID);
            acp.setCreationTime(DateUtils.nowByLong());
            acp.setLastModifiedTime(acp.getCreationTime());
            acp.setResourceID(ResourceUtils.generateResourceId(ResourceType.ACCESS_CONTROL_POLICY));
            acp.setName(Constants.ADMIN_PROFILE_ID);
            acp.setResourceType(BigInteger.valueOf(ResourceType.ACCESS_CONTROL_POLICY));
            acp.setHierarchicalURI(Constants.CSE_NAME + "/" + acp.getName());

            // Self-privileges - all rights for admin
            AccessControlRuleEntity ruleEntity = new AccessControlRuleEntity();
            AccessControlOriginatorEntity adminOriginatorEntity = accessControlOriginatorRepository.findFirstByOriginatorIDIs(Constants.ADMIN_REQUESTING_ENTITY);
            if (adminOriginatorEntity == null)
                adminOriginatorEntity = accessControlOriginatorRepository.save(new AccessControlOriginatorEntity(Constants.ADMIN_REQUESTING_ENTITY));
            /*Add rule for admin*/
            ruleEntity = accessControlRuleRepository.findFirstByAccessControlRuleId(ShortName.ACR +
                    Constants.PREFIX_SEPERATOR +
                    Constants.ADMIN_REQUESTING_ENTITY);
            if (ruleEntity == null) {
                ruleEntity = new AccessControlRuleEntity();
                ruleEntity.setAccessControlRuleId(ShortName.ACR +
                        Constants.PREFIX_SEPERATOR +
                        Constants.ADMIN_REQUESTING_ENTITY);
                ruleEntity.getAccessControlOriginators().add(adminOriginatorEntity);
                ruleEntity.setCreate(true);
                ruleEntity.setRetrieve(true);
                ruleEntity.setUpdate(true);
                ruleEntity.setDelete(true);
                ruleEntity.setNotify(true);
                ruleEntity.setDiscovery(true);
                ruleEntity = accessControlRuleRepository.save(ruleEntity);
            }
            acp.getSelfPrivileges().add(ruleEntity);

            // Privileges - all rights for hosting cse
            AccessControlOriginatorEntity cseOriginatorEntity = accessControlOriginatorRepository.findFirstByOriginatorIDIs("/" + Constants.CSE_ID);
            if (cseOriginatorEntity == null)
                cseOriginatorEntity = accessControlOriginatorRepository.save(new AccessControlOriginatorEntity("/" + Constants.CSE_ID));
            ruleEntity = accessControlRuleRepository.findFirstByAccessControlRuleId(ShortName.ACR +
                    Constants.PREFIX_SEPERATOR +
                    Constants.ADMIN_REQUESTING_ENTITY +
                    Constants.PREFIX_SEPERATOR +
                    Constants.CSE_ID);
            if (ruleEntity == null) {
                ruleEntity = new AccessControlRuleEntity();
                ruleEntity.setCreate(true);
                ruleEntity.setRetrieve(true);
                ruleEntity.setUpdate(true);
                ruleEntity.setDelete(true);
                ruleEntity.setNotify(true);
                ruleEntity.setDiscovery(true);
                ruleEntity.getAccessControlOriginators().add(adminOriginatorEntity);
                ruleEntity.getAccessControlOriginators().add(cseOriginatorEntity);
                ruleEntity.setAccessControlRuleId(ShortName.ACR +
                        Constants.PREFIX_SEPERATOR +
                        Constants.ADMIN_REQUESTING_ENTITY +
                        Constants.PREFIX_SEPERATOR +
                        Constants.CSE_ID);
                ruleEntity = accessControlRuleRepository.save(ruleEntity);
            }
            acp.getPrivileges().add(ruleEntity);

            /* Save acp admin */
            acp = accessControlPolicyRepository.save(acp);
            acpAdminId = acp.getResourceID();
        }


        /** Guest profile **/
        acp = accessControlPolicyRepository.findFirstByNameAndParentID(Constants.GUEST_PROFILE_ID, "/" + Constants.CSE_ID);
        if (acp != null) {
            acpGuestId = acp.getResourceID();
            logger.info("Acp Guest for cse base already existed, not init acp guest");
        } else {
            acp = new AccessControlPolicyEntity();
            acp.setParentID("/" + Constants.CSE_ID);
            acp.setCreationTime(DateUtils.nowByLong());
            acp.setLastModifiedTime(acp.getCreationTime());
            acp.setResourceID(ResourceUtils.generateResourceId(ResourceType.ACCESS_CONTROL_POLICY));
            acp.setName(Constants.GUEST_PROFILE_ID);
            acp.setResourceType(BigInteger.valueOf(ResourceType.ACCESS_CONTROL_POLICY));
            acp.setHierarchicalURI(Constants.CSE_NAME + "/" + acp.getName());

            // privileges for ALL originators (read + discovery)
            AccessControlRuleEntity ruleEntity = accessControlRuleRepository.findFirstByAccessControlRuleId(ShortName.ACR +
                    vn.vnpt.oneiot.common.constants.Constants.PREFIX_SEPERATOR +
                    Constants.GUEST_REQUESTING_ENTITY);
            AccessControlOriginatorEntity guestOriginatorEntity = accessControlOriginatorRepository.findFirstByOriginatorIDIs(Constants.GUEST_REQUESTING_ENTITY);
            if (guestOriginatorEntity == null)
                guestOriginatorEntity = accessControlOriginatorRepository.save(new AccessControlOriginatorEntity(Constants.GUEST_REQUESTING_ENTITY));
            if (ruleEntity == null) {
                ruleEntity = new AccessControlRuleEntity();
                ruleEntity.setRetrieve(true);
                ruleEntity.setDiscovery(true);
                ruleEntity.getAccessControlOriginators().add(guestOriginatorEntity);
                ruleEntity.setAccessControlRuleId(ShortName.ACR +
                        Constants.PREFIX_SEPERATOR +
                        Constants.GUEST_REQUESTING_ENTITY);
                ruleEntity = accessControlRuleRepository.save(ruleEntity);
            }
            acp.getPrivileges().add(ruleEntity);

            /** Save guest acp **/
            acp = accessControlPolicyRepository.save(acp);
            acpGuestId = acp.getResourceID();
        }
    }


    private void initServiceSubscriptionProfile() {
        M2MServiceSubscriptionProfileEntity msspEntity = msspService.getOneByParentIdAndName("/" + Constants.CSE_ID, Constants.MSSP_BASE_NAME);
        if (msspEntity == null) {
            msspEntity = msspService.createBaseMssp();
        }
        defaultMsspId = msspEntity.getResourceID();
        /** Create ServiceSubscriberNode **/
        ServiceSubscribedNodeEntity svsnEntity = svsnService.getOneByParentIdAndName(msspEntity.getResourceID(), ShortName.SVSN + Constants.PREFIX_SEPERATOR + Constants.CSE_ID);
        if (svsnEntity == null) {
            svsnEntity = new ServiceSubscribedNodeEntity();
            svsnEntity.setCseid("/" + vn.vnpt.oneiot.common.constants.Constants.CSE_ID);
            svsnEntity.setNodeID(svsnEntity.getCseid());
            svsnEntity.setAccessControlPolicyIds(Arrays.asList(new String[]{acpAdminId}));
            svsnEntity.setResourceType(ResourceType.SERVICE_SUBSCRIBED_NODE);
            svsnEntity.setName(ShortName.SVSN + Constants.PREFIX_SEPERATOR + Constants.CSE_ID);
            svsnEntity.setHierarchicalURI(msspEntity.getHierarchicalURI() + "/" + svsnEntity.getName());
            svsnEntity.setParentID(msspEntity.getResourceID());
            svsnEntity = svsnService.create(svsnEntity);
        }
        csebaseSvsnId = svsnEntity.getResourceID();
        ServiceSubscribedAppRuleEntity asarEntity = asarService.getOneByParentIdAndName("/" + Constants.CSE_ID,
                vn.vnpt.oneiot.core.constants.Constants.ResourceConstant.defaultAsarNameSAe);
        if (asarEntity == null) {
            asarEntity = new ServiceSubscribedAppRuleEntity();
            asarEntity.setServiceSubscribedNodeLinks(Arrays.asList(new String[]{csebaseSvsnId}));
            asarEntity.setApplicableCredIDs(new BsonRegularExpression("^None$"));
            asarEntity.setAllowedAppIDs(new BsonRegularExpression(".*"));
            asarEntity.setAllowedAEs(new BsonRegularExpression("^Svnpt.*$"));
            asarEntity.setAllowedAEsString("^Svnpt.*$");
            asarEntity.setAccessControlPolicyIds(Arrays.asList(new String[]{acpAdminId}));
            asarEntity.setName(vn.vnpt.oneiot.core.constants.Constants.ResourceConstant.defaultAsarNameSAe);
            asarEntity.setHierarchicalURI(Constants.CSE_NAME + "/" + asarEntity.getName());
            asarEntity.setParentID("/" + vn.vnpt.oneiot.common.constants.Constants.CSE_ID);
            asarEntity.setResourceType(ResourceType.SERVICE_SUBSCRIBED_APP_RULE);
            asarEntity.setResourceID(ResourceUtils.generateResourceId(ResourceType.SERVICE_SUBSCRIBED_APP_RULE));
            asarEntity = asarService.create(asarEntity);
        }
        defaultAsarIdSAe = asarEntity.getResourceID();

        asarEntity = asarService.getOneByParentIdAndName("/" + Constants.CSE_ID,
                vn.vnpt.oneiot.core.constants.Constants.ResourceConstant.defaultAsarNameCAe);
        if (asarEntity == null) {
            asarEntity = new ServiceSubscribedAppRuleEntity();
            asarEntity.setServiceSubscribedNodeLinks(Arrays.asList(new String[]{csebaseSvsnId}));
            asarEntity.setApplicableCredIDs(new BsonRegularExpression("^None$"));
            asarEntity.setAllowedAppIDs(new BsonRegularExpression(".*"));
            asarEntity.setAllowedAEs(new BsonRegularExpression("^Cvnpt.*$"));
            asarEntity.setAllowedAEsString("^Cvnpt.*$");
            asarEntity.setAccessControlPolicyIds(Arrays.asList(new String[]{acpAdminId}));
            asarEntity.setName(vn.vnpt.oneiot.core.constants.Constants.ResourceConstant.defaultAsarNameCAe);
            asarEntity.setHierarchicalURI(Constants.CSE_NAME + "/" + asarEntity.getName());
            asarEntity.setParentID("/" + vn.vnpt.oneiot.common.constants.Constants.CSE_ID);
            asarEntity.setResourceType(ResourceType.SERVICE_SUBSCRIBED_APP_RULE);
            asarEntity.setResourceID(ResourceUtils.generateResourceId(ResourceType.SERVICE_SUBSCRIBED_APP_RULE));
            asarEntity = asarService.create(asarEntity);
        }
        defaultAsarIdCAe = asarEntity.getResourceID();
        if (svsnEntity.getRuleLinks() == null) {
            svsnEntity.setRuleLinks(Arrays.asList(new String[] {defaultAsarIdSAe, defaultAsarIdCAe}));
            svsnEntity = svsnService.update(svsnEntity);
        } else {
            if (!svsnEntity.getRuleLinks().contains(defaultAsarIdSAe)) {
                svsnEntity.getRuleLinks().add(defaultAsarIdSAe);
            }
            if (!svsnEntity.getRuleLinks().contains(defaultAsarIdCAe)) {
                svsnEntity.getRuleLinks().add(defaultAsarIdCAe);
            }
            svsnEntity = svsnService.update(svsnEntity);
        }
    }

    private void initCSEBase() throws InterruptedException {
        MappingAcpIdsResourceEntity mappingAcpIdsResourceEntity = mappingAcpIdsResourceRepository.findFirstByResourceIdIs("/" + Constants.CSE_ID);
        if (mappingAcpIdsResourceEntity != null) {
            logger.info("Mapping acpid for cse base has already initialized, do nothing");
            return;
        }
        mappingAcpIdsResourceEntity = new MappingAcpIdsResourceEntity();
        mappingAcpIdsResourceEntity.setResourceId("/" + Constants.CSE_ID);
        mappingAcpIdsResourceEntity.setHuri(Constants.CSE_NAME);
        // Gan acp admin profile cho CSE base
        if (CSEInitialize.acpAdminId == null || CSEInitialize.acpGuestId == null) {
            logger.info("ACP for CSE base cannot be initialized");
            throw new InterruptedException("Acp admin and acp guest haven't initialized yet");
        }

        mappingAcpIdsResourceEntity.getAccessControlPolicyIds().add(CSEInitialize.acpAdminId);
        mappingAcpIdsResourceEntity.getAccessControlPolicyIds().add(CSEInitialize.acpGuestId);
        mappingAcpIdsResourceRepository.save(mappingAcpIdsResourceEntity);
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        init();
    }
}
