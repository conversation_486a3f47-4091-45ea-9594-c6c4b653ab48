spring:
  data:
    mongodb:
      uri: mongodb://auth:auth%40#123@localhost:27017/authorization
#      uri: mongodb://devIoT:devIoT#123@************:27017/authorization-dev?authSource=admin
#      host: ************
#      port: 27017
#      authentication-database: admin
#      username: devIoT
#      password: devIoT#123
#      database: authorization-dev
  rabbitmq:
    host: ************
    port: 5672
    username: admin
    password: oneiot@2022
  redis:
    hostname: ************
    port: 6379
    password: oneiot@2020
jhipster:
  mail: # specific JHipster mail property, for standard properties see MailProperties
    from: <EMAIL>
    base-url: http://**********:9090

baseUrl: http://**********:9090

logging:
  level:
    root: ERROR
    org.springframework: INFO
    vn.vnpt.oneiot: DEBUG
