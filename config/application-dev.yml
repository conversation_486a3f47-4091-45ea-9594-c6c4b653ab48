spring:
  data:
    mongodb:
      uri: mongodb://auth:auth%40#123@localhost:27017,localhost:27018,localhost:27019/authorization
#      uri: mongodb://devIoT:devIoT#123@************:27017/authorization-dev?authSource=admin
#      host: ************
#      port: 27017
#      authentication-database: admin
#      username: devIoT
#      password: devIoT#123
#      database: authorization-dev
  rabbitmq:
    addresses: localhost:5672
    port: 5672
    username: admin
    password: One-IOT
    listener:
      simple:
        prefetch: 20
        concurrency: 100
  redis:
    password: One-IOT
    sentinel:
      master: mymaster
      nodes: localhost:6377
    jedis:
      pool:
        max-active: 1000
        min-idle: 0
        max-wait: -1
        max-idle: 10
jhipster:
  mail: # specific JHipster mail property, for standard properties see MailProperties
    from: <EMAIL>
    base-url: http://**********:9090

baseUrl: http://**********:9090

logging:
  level:
    root: ERROR
    org.springframework: INFO
    vn.vnpt.oneiot: DEBUG
