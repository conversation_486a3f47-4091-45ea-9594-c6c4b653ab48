server:
  port: 8282

spring:
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true
  redis:
    jedis:
      pool:
        max-active: 1000
        min-idle: 0
        max-wait: -1
        max-idle: 10
  mail:
    host: mail.vnpt-technology.vn
    port: 25
    username: oneiot
    password: <PERSON><PERSON>@123$$
    protocol: smtp
#    properties:
#      mail.smtp.auth: false
#      mail.smtp.ssl.enable: false
#      mail.smtp.starttls.enable: false
  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: -1

application:
  activation:
    expirePeriodActiveMail: 2592000 #seconds - 30days
    expirePeriodResetPassword: 86400 #seconds - 1day
    enableMail: true #true/false
  subNameQueue: huy1
  timeAfterSoftDelete: 2592000
  tokenTime:
    remember: 94608000 #seconds - until end of 2027 (3 years)
    noRemember: 94608000 # seconds - until end of 2027 (3 years)
    refreshToken: 94608000 # seconds - until end of 2027 (3 years)

master-orches-sychronized: true
retry-number-sychronized: 3
certificateRelease: false
defaultAEStemPrefix: S
transactionTimeout: 10000
sendRefreshTokenToDevice: true
logging:
  level:
    root: ERROR
    org.springframework: INFO
    vn.vnpt.oneiot: DEBUG
    vn.vnpt.oneiot.core: DEBUG
